#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接池管理器 - 串口连接的复用和资源管理
提供串口连接的池化管理，支持连接复用、异常恢复和资源优化

核心特性:
- 连接复用: 避免频繁的连接创建和销毁
- 异常恢复: 自动检测和恢复异常连接
- 资源管理: 统一管理串口连接资源
- 状态监控: 实时监控连接池状态和健康度
- 线程安全: 支持多线程环境下的安全操作

作者: LD (Lead Developer)
创建时间: 2025-08-07
版本: 1.0
"""

import threading
import time
from typing import Dict, Optional, List, Callable
from enum import Enum
import logging

from .serial_manager import SerialManager, ConnectionState
from utils.serial_config_manager import SerialConfig
from utils.exceptions import (
    ResourceNotAvailableError, InitializationError, 
    SerialConnectionError, ThreadError
)


class PoolState(Enum):
    """连接池状态枚举"""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    DEGRADED = "degraded"  # 部分连接异常
    INACTIVE = "inactive"
    ERROR = "error"


class ConnectionInfo:
    """连接信息类"""
    
    def __init__(self, manager: SerialManager, config: SerialConfig):
        self.manager = manager
        self.config = config
        self.created_time = time.time()
        self.last_used_time = time.time()
        self.use_count = 0
        self.error_count = 0
        self.is_healthy = True
        self.lock = threading.Lock()
    
    def mark_used(self):
        """标记连接被使用"""
        with self.lock:
            self.last_used_time = time.time()
            self.use_count += 1
    
    def mark_error(self):
        """标记连接错误"""
        with self.lock:
            self.error_count += 1
            self.is_healthy = False
    
    def mark_healthy(self):
        """标记连接健康"""
        with self.lock:
            self.is_healthy = True
    
    def get_idle_time(self) -> float:
        """获取空闲时间"""
        with self.lock:
            return time.time() - self.last_used_time


class ConnectionPool:
    """
    串口连接池管理器
    
    提供串口连接的池化管理，支持连接复用、异常恢复和资源优化。
    通过连接池减少连接创建开销，提高系统性能和稳定性。
    """
    
    def __init__(self, max_connections: int = 5, max_idle_time: float = 300.0,
                 health_check_interval: float = 60.0, name: str = "ConnectionPool"):
        """
        初始化连接池
        
        Args:
            max_connections: 最大连接数
            max_idle_time: 最大空闲时间（秒）
            health_check_interval: 健康检查间隔（秒）
            name: 连接池名称
        """
        self._max_connections = max_connections
        self._max_idle_time = max_idle_time
        self._health_check_interval = health_check_interval
        self._name = name
        self._logger = logging.getLogger(f"datastudio.communication.{name}")
        
        # 连接池存储
        self._connections: Dict[str, ConnectionInfo] = {}  # port -> ConnectionInfo
        self._pool_lock = threading.RLock()
        
        # 状态管理
        self._state = PoolState.INITIALIZING
        self._last_health_check = 0.0
        
        # 健康检查线程
        self._health_check_thread: Optional[threading.Thread] = None
        self._health_check_stop_event = threading.Event()
        
        # 统计信息
        self._stats = {
            'total_connections_created': 0,
            'total_connections_destroyed': 0,
            'active_connections': 0,
            'healthy_connections': 0,
            'total_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'health_checks': 0,
            'recovered_connections': 0
        }
        
        # 延迟启动健康检查（在第一次使用时启动）
        self._health_check_started = False
        self._state = PoolState.ACTIVE
        
        self._logger.info(f"连接池初始化完成: {self._name} (最大连接数: {max_connections})")
    
    def get_connection(self, config: SerialConfig, 
                      auto_connect: bool = True) -> SerialManager:
        """
        获取串口连接
        
        Args:
            config: 串口配置
            auto_connect: 是否自动连接
            
        Returns:
            串口管理器实例
            
        Raises:
            ResourceNotAvailableError: 无法获取连接
            SerialConnectionError: 连接失败
        """
        with self._pool_lock:
            # 延迟启动健康检查线程
            if not self._health_check_started:
                self._start_health_check()
                self._health_check_started = True

            self._stats['total_requests'] += 1
            port = config.port
            
            # 检查是否已有连接
            if port in self._connections:
                conn_info = self._connections[port]
                if conn_info.is_healthy and conn_info.manager.is_connected():
                    conn_info.mark_used()
                    self._stats['cache_hits'] += 1
                    self._logger.debug(f"复用现有连接: {port}")
                    return conn_info.manager
                else:
                    # 连接不健康，移除并重新创建
                    self._remove_connection(port)
            
            # 检查连接数限制
            if len(self._connections) >= self._max_connections:
                # 尝试清理空闲连接
                self._cleanup_idle_connections()
                
                if len(self._connections) >= self._max_connections:
                    raise ResourceNotAvailableError(
                        "连接池", f"连接数已达上限: {self._max_connections}"
                    )
            
            # 创建新连接
            try:
                manager = SerialManager(config, f"PooledSerial-{port}")
                
                if auto_connect:
                    if not manager.connect():
                        raise SerialConnectionError(port, "自动连接失败")
                
                # 添加到连接池
                conn_info = ConnectionInfo(manager, config)
                self._connections[port] = conn_info
                
                # 添加状态回调
                manager.add_state_callback(
                    "pool_monitor", 
                    lambda state: self._on_connection_state_change(port, state)
                )
                
                conn_info.mark_used()
                self._stats['cache_misses'] += 1
                self._stats['total_connections_created'] += 1
                self._update_stats()
                
                self._logger.info(f"创建新连接: {port}")
                return manager
                
            except Exception as e:
                self._logger.error(f"创建连接失败: {port} - {e}")
                raise SerialConnectionError(port, f"连接创建失败: {e}")
    
    def release_connection(self, port: str) -> bool:
        """
        释放连接（标记为可复用，不实际关闭）
        
        Args:
            port: 串口端口
            
        Returns:
            释放是否成功
        """
        with self._pool_lock:
            if port in self._connections:
                conn_info = self._connections[port]
                # 连接保持在池中，只更新使用时间
                conn_info.last_used_time = time.time()
                self._logger.debug(f"释放连接到池: {port}")
                return True
            return False
    
    def remove_connection(self, port: str) -> bool:
        """
        移除并关闭连接
        
        Args:
            port: 串口端口
            
        Returns:
            移除是否成功
        """
        with self._pool_lock:
            return self._remove_connection(port)
    
    def get_pool_stats(self) -> Dict[str, any]:
        """
        获取连接池统计信息
        
        Returns:
            统计信息字典
        """
        with self._pool_lock:
            self._update_stats()
            stats = self._stats.copy()
            stats.update({
                'pool_state': self._state.value,
                'max_connections': self._max_connections,
                'current_connections': len(self._connections),
                'connection_details': [
                    {
                        'port': port,
                        'is_healthy': info.is_healthy,
                        'is_connected': info.manager.is_connected(),
                        'use_count': info.use_count,
                        'error_count': info.error_count,
                        'idle_time': info.get_idle_time(),
                        'created_time': info.created_time
                    }
                    for port, info in self._connections.items()
                ]
            })
            return stats
    
    def health_check(self) -> Dict[str, bool]:
        """
        执行健康检查
        
        Returns:
            各连接的健康状态
        """
        with self._pool_lock:
            results = {}
            unhealthy_ports = []
            
            for port, conn_info in self._connections.items():
                try:
                    # 检查连接状态
                    is_connected = conn_info.manager.is_connected()
                    
                    if is_connected:
                        conn_info.mark_healthy()
                        results[port] = True
                    else:
                        conn_info.mark_error()
                        results[port] = False
                        unhealthy_ports.append(port)
                        
                except Exception as e:
                    self._logger.warning(f"健康检查失败: {port} - {e}")
                    conn_info.mark_error()
                    results[port] = False
                    unhealthy_ports.append(port)
            
            # 尝试恢复不健康的连接
            for port in unhealthy_ports:
                if self._try_recover_connection(port):
                    results[port] = True
                    self._stats['recovered_connections'] += 1
            
            self._stats['health_checks'] += 1
            self._last_health_check = time.time()
            
            # 更新池状态
            self._update_pool_state()
            
            return results

    def shutdown(self) -> None:
        """关闭连接池"""
        with self._pool_lock:
            self._logger.info("正在关闭连接池...")

            # 停止健康检查线程
            self._health_check_stop_event.set()
            if self._health_check_thread and self._health_check_thread.is_alive():
                self._health_check_thread.join(timeout=5.0)

            # 关闭所有连接
            for port in list(self._connections.keys()):
                self._remove_connection(port)

            self._state = PoolState.INACTIVE
            self._logger.info("连接池已关闭")

    def _remove_connection(self, port: str) -> bool:
        """内部移除连接方法"""
        if port not in self._connections:
            return False

        conn_info = self._connections[port]
        try:
            # 移除状态回调
            conn_info.manager.remove_state_callback("pool_monitor")

            # 断开连接
            conn_info.manager.disconnect()

            # 从池中移除
            del self._connections[port]

            self._stats['total_connections_destroyed'] += 1
            self._logger.debug(f"移除连接: {port}")
            return True

        except Exception as e:
            self._logger.error(f"移除连接失败: {port} - {e}")
            return False

    def _cleanup_idle_connections(self) -> int:
        """清理空闲连接"""
        idle_ports = []

        for port, conn_info in self._connections.items():
            if conn_info.get_idle_time() > self._max_idle_time:
                idle_ports.append(port)

        cleaned_count = 0
        for port in idle_ports:
            if self._remove_connection(port):
                cleaned_count += 1

        if cleaned_count > 0:
            self._logger.info(f"清理空闲连接: {cleaned_count} 个")

        return cleaned_count

    def _try_recover_connection(self, port: str) -> bool:
        """尝试恢复连接"""
        if port not in self._connections:
            return False

        conn_info = self._connections[port]
        try:
            # 尝试重新连接
            if conn_info.manager.reconnect(max_attempts=2, delay=0.5):
                conn_info.mark_healthy()
                self._logger.info(f"连接恢复成功: {port}")
                return True
            else:
                self._logger.warning(f"连接恢复失败: {port}")
                return False

        except Exception as e:
            self._logger.error(f"连接恢复异常: {port} - {e}")
            return False

    def _on_connection_state_change(self, port: str, state: ConnectionState) -> None:
        """连接状态变化回调"""
        if port in self._connections:
            conn_info = self._connections[port]

            if state == ConnectionState.ERROR:
                conn_info.mark_error()
                self._logger.warning(f"连接状态异常: {port}")
            elif state == ConnectionState.CONNECTED:
                conn_info.mark_healthy()

    def _start_health_check(self) -> None:
        """启动健康检查线程"""
        def health_check_worker():
            while not self._health_check_stop_event.is_set():
                try:
                    # 执行健康检查
                    self.health_check()

                    # 清理空闲连接
                    self._cleanup_idle_connections()

                except Exception as e:
                    self._logger.error(f"健康检查异常: {e}")

                # 等待下次检查
                self._health_check_stop_event.wait(self._health_check_interval)

        self._health_check_thread = threading.Thread(
            target=health_check_worker,
            name=f"{self._name}-HealthCheck",
            daemon=True
        )
        self._health_check_thread.start()
        self._logger.debug("健康检查线程已启动")

    def _update_stats(self) -> None:
        """更新统计信息"""
        active_count = 0
        healthy_count = 0

        for conn_info in self._connections.values():
            if conn_info.manager.is_connected():
                active_count += 1
            if conn_info.is_healthy:
                healthy_count += 1

        self._stats['active_connections'] = active_count
        self._stats['healthy_connections'] = healthy_count

    def _update_pool_state(self) -> None:
        """更新连接池状态"""
        if not self._connections:
            self._state = PoolState.ACTIVE
            return

        healthy_ratio = self._stats['healthy_connections'] / len(self._connections)

        if healthy_ratio >= 0.8:
            self._state = PoolState.ACTIVE
        elif healthy_ratio >= 0.5:
            self._state = PoolState.DEGRADED
        else:
            self._state = PoolState.ERROR

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.shutdown()
        return False

    def __str__(self) -> str:
        """字符串表示"""
        return f"ConnectionPool(connections={len(self._connections)}, state={self._state.value})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"ConnectionPool(name={self._name}, max_connections={self._max_connections}, "
                f"current_connections={len(self._connections)}, state={self._state.value})")
