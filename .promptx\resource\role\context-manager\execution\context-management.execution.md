<execution>
  <constraint>
    ## 上下文管理技术约束
    - **Serena文档访问限制**：必须通过Serena MCP工具访问文档
    - **时间戳准确性要求**：必须使用mcp.server_time获取准确时间
    - **文档格式约束**：上下文信息必须以结构化格式存储
    - **并发访问限制**：多角色同时访问时的冲突处理
  </constraint>
  
  <rule>
    ## 上下文管理强制规则
    - **完整性检查规则**：每次上下文传递前必须进行完整性检查
    - **冲突检测规则**：发现信息冲突时必须立即标记和处理
    - **版本控制规则**：所有上下文变更必须记录版本信息
    - **访问权限规则**：不同角色对上下文信息的访问权限控制
  </rule>
  
  <guideline>
    ## 上下文管理指导原则
    - **用户中心原则**：始终以用户需求为核心组织上下文
    - **效率优先原则**：优化上下文传递的效率和准确性
    - **透明化原则**：上下文管理过程对用户透明
    - **持续改进原则**：基于使用效果持续优化策略
  </guideline>
  
  <process>
    ## 上下文管理详细流程
    
    ### 阶段1：信息收集
    1. 读取当前对话历史和用户输入
    2. 通过Serena MCP读取相关文档
    3. 检查项目状态和环境变化
    4. 收集外部依赖信息
    
    ### 阶段2：信息分析
    1. 分析信息间的关联关系
    2. 识别潜在的信息冲突
    3. 评估信息的重要性和相关性
    4. 检查信息的时效性和准确性
    
    ### 阶段3：上下文生成
    1. 生成结构化的上下文摘要
    2. 标记关键决策点和约束条件
    3. 识别需要特别关注的风险点
    4. 生成针对目标角色的上下文包
    
    ### 阶段4：上下文传递
    1. 以智能提示词形式传递上下文
    2. 确保目标角色正确接收上下文
    3. 记录上下文传递的时间和内容
    4. 监控上下文使用效果
    
    ### 阶段5：效果跟踪
    1. 跟踪上下文在决策中的使用情况
    2. 收集角色对上下文质量的反馈
    3. 分析上下文管理的改进点
    4. 更新上下文管理策略
  </process>
  
  <criteria>
    ## 上下文管理质量标准
    - **完整性标准**：关键信息覆盖率≥95%
    - **准确性标准**：信息准确率≥98%
    - **时效性标准**：信息更新延迟≤1分钟
    - **相关性标准**：相关信息识别率≥90%
    - **效率标准**：上下文生成时间≤30秒
  </criteria>
</execution>
