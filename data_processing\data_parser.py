"""
数据解析器模块 - 基于JSON配置的动态数据解析引擎

该模块实现了完全基于JSON配置驱动的数据字段解析功能，支持任意自由串口协议的数据解析。
核心特性：
- 动态配置驱动：所有数据解析逻辑完全由JSON配置决定，零硬编码
- 多数据类型支持：支持int8/16/32、float32/64等数据类型
- 字节序处理：支持大端和小端字节序的动态处理
- 缩放计算：支持配置化的数据缩放和单位转换
- 协议无关：完全不依赖具体协议实现

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import logging
import struct
from typing import List, Dict, Any, Union, Optional
from dataclasses import dataclass
from utils.exceptions import DataProcessingError


@dataclass
class DataField:
    """数据字段定义"""
    name: str           # 字段名称
    offset: int         # 数据在帧中的偏移
    length: int         # 数据字段长度（字节）
    data_type: str      # 数据类型
    endian: str         # 字节序
    scale_factor: float # 缩放系数
    unit: str           # 数据单位
    
    def __post_init__(self):
        """初始化后验证"""
        if self.offset < 0:
            raise ValueError(f"字段 {self.name} 的偏移量不能为负数")
        if self.length <= 0:
            raise ValueError(f"字段 {self.name} 的长度必须大于0")
        if self.data_type not in ['int8', 'int16', 'int32', 'float32', 'float64']:
            raise ValueError(f"字段 {self.name} 的数据类型 {self.data_type} 不支持")
        if self.endian not in ['big', 'little']:
            raise ValueError(f"字段 {self.name} 的字节序 {self.endian} 不支持")


@dataclass
class ParsedData:
    """解析后的数据"""
    field_name: str     # 字段名称
    raw_value: Union[int, float]  # 原始值
    scaled_value: float # 缩放后的值
    unit: str          # 单位
    raw_bytes: bytes   # 原始字节数据


class DataParser:
    """
    基于JSON配置的动态数据解析器
    
    核心功能：
    1. 配置驱动的数据字段解析
    2. 多种数据类型支持
    3. 大端和小端字节序处理
    4. 缩放系数和单位转换
    """
    
    def __init__(self, fields_config: List[Dict[str, Any]]):
        """
        初始化数据解析器
        
        Args:
            fields_config: 数据字段配置列表
        """
        self.logger = logging.getLogger(f"{__name__}.DataParser")
        self.fields = []
        
        # 解析字段配置
        for field_config in fields_config:
            try:
                field = DataField(
                    name=field_config["name"],
                    offset=field_config["offset"],
                    length=field_config["length"],
                    data_type=field_config["data_type"],
                    endian=field_config["endian"],
                    scale_factor=field_config["scale_factor"],
                    unit=field_config.get("unit", "")
                )
                self.fields.append(field)
            except Exception as e:
                raise DataProcessingError(
                    f"数据字段配置解析失败: {str(e)}",
                    error_code="FIELD_CONFIG_ERROR",
                    details={"field_config": field_config}
                )
        
        # 按偏移量排序字段
        self.fields.sort(key=lambda f: f.offset)
        
        # 统计信息
        self.stats = {
            "frames_parsed": 0,
            "fields_parsed": 0,
            "parse_errors": 0
        }
        
        self.logger.info(f"数据解析器初始化完成 - 字段数量: {len(self.fields)}")
        for field in self.fields:
            self.logger.debug(f"字段: {field.name}, 偏移: {field.offset}, "
                            f"类型: {field.data_type}, 字节序: {field.endian}")
    
    def parse_frame(self, frame_data: bytes) -> List[ParsedData]:
        """
        解析帧数据
        
        Args:
            frame_data: 完整的帧数据
            
        Returns:
            解析后的数据列表
        """
        if not frame_data:
            return []
        
        parsed_results = []
        
        try:
            for field in self.fields:
                try:
                    parsed_data = self._parse_field(frame_data, field)
                    parsed_results.append(parsed_data)
                    self.stats["fields_parsed"] += 1
                except Exception as e:
                    self.logger.error(f"解析字段 {field.name} 失败: {str(e)}")
                    self.stats["parse_errors"] += 1
                    # 继续解析其他字段
                    continue
            
            self.stats["frames_parsed"] += 1
            
        except Exception as e:
            self.logger.error(f"解析帧数据失败: {str(e)}")
            self.stats["parse_errors"] += 1
            raise DataProcessingError(
                f"帧数据解析失败: {str(e)}",
                error_code="FRAME_PARSE_ERROR",
                details={"frame_length": len(frame_data)}
            )
        
        return parsed_results
    
    def _parse_field(self, frame_data: bytes, field: DataField) -> ParsedData:
        """
        解析单个字段
        
        Args:
            frame_data: 帧数据
            field: 字段定义
            
        Returns:
            解析后的数据
        """
        # 检查偏移量和长度是否有效
        if field.offset + field.length > len(frame_data):
            raise ValueError(f"字段 {field.name} 超出帧数据范围 "
                           f"(偏移: {field.offset}, 长度: {field.length}, 帧长度: {len(frame_data)})")
        
        # 提取原始字节数据
        raw_bytes = frame_data[field.offset:field.offset + field.length]
        
        # 根据数据类型和字节序解析数据
        raw_value = self._unpack_data(raw_bytes, field.data_type, field.endian)
        
        # 应用缩放系数
        scaled_value = raw_value * field.scale_factor
        
        return ParsedData(
            field_name=field.name,
            raw_value=raw_value,
            scaled_value=scaled_value,
            unit=field.unit,
            raw_bytes=raw_bytes
        )
    
    def _unpack_data(self, data: bytes, data_type: str, endian: str) -> Union[int, float]:
        """
        解包二进制数据
        
        Args:
            data: 二进制数据
            data_type: 数据类型
            endian: 字节序
            
        Returns:
            解析后的数值
        """
        # 确定字节序前缀
        endian_prefix = '>' if endian == 'big' else '<'
        
        # 数据类型映射
        type_mapping = {
            'int8': 'b',      # signed char
            'int16': 'h',     # short
            'int32': 'i',     # int
            'float32': 'f',   # float
            'float64': 'd'    # double
        }
        
        if data_type not in type_mapping:
            raise ValueError(f"不支持的数据类型: {data_type}")
        
        format_string = endian_prefix + type_mapping[data_type]
        
        try:
            # 对于int8，不需要字节序
            if data_type == 'int8':
                format_string = 'b'
            
            result = struct.unpack(format_string, data)[0]
            return result
            
        except struct.error as e:
            raise ValueError(f"数据解包失败: {str(e)}, 数据: {data.hex()}, 格式: {format_string}")
    
    def get_field_names(self) -> List[str]:
        """获取所有字段名称"""
        return [field.name for field in self.fields]
    
    def get_field_by_name(self, name: str) -> Optional[DataField]:
        """根据名称获取字段定义"""
        for field in self.fields:
            if field.name == name:
                return field
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            "frames_parsed": 0,
            "fields_parsed": 0,
            "parse_errors": 0
        }
        self.logger.info("统计信息已重置")
    
    def validate_frame_compatibility(self, frame_length: int) -> bool:
        """
        验证帧长度是否与字段配置兼容
        
        Args:
            frame_length: 帧长度
            
        Returns:
            是否兼容
        """
        for field in self.fields:
            if field.offset + field.length > frame_length:
                self.logger.warning(f"字段 {field.name} 超出帧长度 "
                                  f"(需要: {field.offset + field.length}, 实际: {frame_length})")
                return False
        return True
    
    def get_frame_requirements(self) -> Dict[str, int]:
        """
        获取帧数据要求
        
        Returns:
            包含最小帧长度等要求的字典
        """
        if not self.fields:
            return {"min_frame_length": 0}
        
        max_end_offset = max(field.offset + field.length for field in self.fields)
        
        return {
            "min_frame_length": max_end_offset,
            "field_count": len(self.fields),
            "max_offset": max(field.offset for field in self.fields),
            "max_field_length": max(field.length for field in self.fields)
        }
