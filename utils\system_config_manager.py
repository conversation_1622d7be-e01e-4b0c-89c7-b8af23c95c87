#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置管理器
专门管理系统运行参数配置

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

import json
import threading
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass

from .constants import DEFAULT_CONFIG_DIR, LOG_LEVELS
from .exceptions import (
    ConfigFileNotFoundError, ConfigParsingError, ConfigValidationError
)


@dataclass
class ErrorHandlingConfig:
    """错误处理配置"""
    max_consecutive_errors: int = 5
    pause_on_max_errors: bool = True
    recovery_strategy: str = "manual"  # "manual" or "auto_retry"
    error_weights: Dict[str, int] = None

    def __post_init__(self):
        if self.error_weights is None:
            self.error_weights = {
                "frame_error": 1,
                "parse_error": 1,
                "comm_error": 2
            }


@dataclass
class QueueConfig:
    """队列配置"""
    response_queue_size: int = 100
    queue_warning_threshold: float = 0.8
    batch_processing_size: int = 10


@dataclass
class PerformanceConfig:
    """性能配置"""
    buffer_size: int = 4096
    processing_timeout: float = 0.1
    max_processing_threads: int = 2


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    file_max_size: str = "10MB"
    file_backup_count: int = 30
    format: str = "[%(asctime)s] [%(levelname)s] [%(name)s] - %(message)s"


@dataclass
class MonitoringConfig:
    """监控配置"""
    enable_performance_monitoring: bool = True
    monitoring_interval: float = 1.0
    alert_thresholds: Dict[str, int] = None

    def __post_init__(self):
        if self.alert_thresholds is None:
            self.alert_thresholds = {
                "cpu_usage": 80,
                "memory_usage": 80,
                "queue_usage": 90
            }


@dataclass
class SystemConfig:
    """系统配置数据类"""
    error_handling: ErrorHandlingConfig
    queue_config: QueueConfig
    performance: PerformanceConfig
    logging: LoggingConfig
    monitoring: MonitoringConfig


class SystemConfigManager:
    """系统配置管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        import logging
        self._logger = logging.getLogger("datastudio.system_config")
        self._config: Optional[SystemConfig] = None
        self._config_file_path: Optional[Path] = None
        self._lock = threading.RLock()
        
        # 加载默认配置
        self._load_default_config()
    
    def _load_default_config(self) -> None:
        """加载默认系统配置"""
        with self._lock:
            self._config = SystemConfig(
                error_handling=ErrorHandlingConfig(),
                queue_config=QueueConfig(),
                performance=PerformanceConfig(),
                logging=LoggingConfig(),
                monitoring=MonitoringConfig()
            )
    
    def load_config(self, config_path: Optional[str] = None) -> SystemConfig:
        """
        加载系统配置
        
        Args:
            config_path: 配置文件路径，None表示使用默认配置
            
        Returns:
            系统配置对象
        """
        with self._lock:
            if config_path:
                config_file = Path(config_path)
                if not config_file.exists():
                    raise ConfigFileNotFoundError(str(config_file))
                
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    # 验证和解析配置
                    self._config = self._parse_config(config_data)
                    self._config_file_path = config_file
                    
                    self._logger.info(f"系统配置加载成功: {config_file}")
                    
                except json.JSONDecodeError as e:
                    raise ConfigParsingError(f"JSON解析失败: {e}", str(config_file))
                except Exception as e:
                    self._logger.error(f"加载系统配置失败: {e}")
                    raise
            
            return self._config
    
    def _parse_config(self, config_data: Dict[str, Any]) -> SystemConfig:
        """解析配置数据"""
        # 解析错误处理配置
        error_handling_data = config_data.get('error_handling', {})
        continuous_mode_data = error_handling_data.get('continuous_mode', {})
        error_types_data = continuous_mode_data.get('error_types', {})
        
        error_weights = {}
        for error_type, config in error_types_data.items():
            error_weights[error_type] = config.get('weight', 1)
        
        error_handling = ErrorHandlingConfig(
            max_consecutive_errors=continuous_mode_data.get('max_consecutive_errors', 5),
            pause_on_max_errors=continuous_mode_data.get('pause_on_max_errors', True),
            recovery_strategy=continuous_mode_data.get('recovery_strategy', 'manual'),
            error_weights=error_weights if error_weights else None
        )
        
        # 解析队列配置
        queue_data = config_data.get('queue_config', {})
        queue_config = QueueConfig(
            response_queue_size=queue_data.get('response_queue_size', 100),
            queue_warning_threshold=queue_data.get('queue_warning_threshold', 0.8),
            batch_processing_size=queue_data.get('batch_processing_size', 10)
        )
        
        # 解析性能配置
        performance_data = config_data.get('performance', {})
        performance = PerformanceConfig(
            buffer_size=performance_data.get('buffer_size', 4096),
            processing_timeout=performance_data.get('processing_timeout', 0.1),
            max_processing_threads=performance_data.get('max_processing_threads', 2)
        )
        
        # 解析日志配置
        logging_data = config_data.get('logging', {})
        logging_config = LoggingConfig(
            level=logging_data.get('level', 'INFO'),
            file_max_size=logging_data.get('file_max_size', '10MB'),
            file_backup_count=logging_data.get('file_backup_count', 30),
            format=logging_data.get('format', '[%(asctime)s] [%(levelname)s] [%(name)s] - %(message)s')
        )
        
        # 验证日志级别
        if logging_config.level not in LOG_LEVELS:
            raise ConfigValidationError(f"无效的日志级别: {logging_config.level}")
        
        # 解析监控配置
        monitoring_data = config_data.get('monitoring', {})
        alert_thresholds_data = monitoring_data.get('alert_thresholds', {})
        
        monitoring = MonitoringConfig(
            enable_performance_monitoring=monitoring_data.get('enable_performance_monitoring', True),
            monitoring_interval=monitoring_data.get('monitoring_interval', 1.0),
            alert_thresholds=alert_thresholds_data if alert_thresholds_data else None
        )
        
        return SystemConfig(
            error_handling=error_handling,
            queue_config=queue_config,
            performance=performance,
            logging=logging_config,
            monitoring=monitoring
        )
    
    def get_config(self) -> SystemConfig:
        """获取系统配置"""
        with self._lock:
            return self._config
    
    def get_error_handling_config(self) -> ErrorHandlingConfig:
        """获取错误处理配置"""
        with self._lock:
            return self._config.error_handling
    
    def get_queue_config(self) -> QueueConfig:
        """获取队列配置"""
        with self._lock:
            return self._config.queue_config
    
    def get_performance_config(self) -> PerformanceConfig:
        """获取性能配置"""
        with self._lock:
            return self._config.performance
    
    def get_logging_config(self) -> LoggingConfig:
        """获取日志配置"""
        with self._lock:
            return self._config.logging
    
    def get_monitoring_config(self) -> MonitoringConfig:
        """获取监控配置"""
        with self._lock:
            return self._config.monitoring
    
    def get_config_file_path(self) -> Optional[Path]:
        """获取当前配置文件路径"""
        with self._lock:
            return self._config_file_path
    
    def reload_config(self) -> Optional[SystemConfig]:
        """重新加载配置"""
        with self._lock:
            if self._config_file_path:
                return self.load_config(str(self._config_file_path))
            return self._config
    
    def create_default_config_template(self, output_path: str) -> None:
        """
        创建默认系统配置模板
        
        Args:
            output_path: 输出文件路径
        """
        template = {
            "error_handling": {
                "continuous_mode": {
                    "max_consecutive_errors": 5,
                    "pause_on_max_errors": True,
                    "recovery_strategy": "manual",
                    "error_types": {
                        "frame_error": {"weight": 1},
                        "parse_error": {"weight": 1},
                        "comm_error": {"weight": 2}
                    }
                }
            },
            "queue_config": {
                "response_queue_size": 100,
                "queue_warning_threshold": 0.8,
                "batch_processing_size": 10
            },
            "performance": {
                "buffer_size": 4096,
                "processing_timeout": 0.1,
                "max_processing_threads": 2
            },
            "logging": {
                "level": "INFO",
                "file_max_size": "10MB",
                "file_backup_count": 30,
                "format": "[%(asctime)s] [%(levelname)s] [%(name)s] - %(message)s"
            },
            "monitoring": {
                "enable_performance_monitoring": True,
                "monitoring_interval": 1.0,
                "alert_thresholds": {
                    "cpu_usage": 80,
                    "memory_usage": 80,
                    "queue_usage": 90
                }
            }
        }
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, indent=2, ensure_ascii=False)
        
        self._logger.info(f"系统配置模板已创建: {output_file}")


# 全局系统配置管理器实例
_system_config_manager = SystemConfigManager()


def get_system_config_manager() -> SystemConfigManager:
    """获取系统配置管理器实例"""
    return _system_config_manager
