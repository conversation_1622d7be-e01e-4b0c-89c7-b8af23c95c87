"""
协议流程控制器 - 业务逻辑层核心组件

该模块实现了完整的协议流程控制和业务逻辑管理，负责：
- 组合串口通信和协议解析功能
- 基于JSON配置的动态协议流程执行
- 三种工作模式的统一管理
- 业务流程状态管理

核心特性：
- 动态配置驱动：所有业务逻辑完全由JSON配置决定
- 协议无关：完全不依赖具体协议实现
- 状态管理：完整的业务流程状态跟踪
- 串口集成：协调串口通信和数据处理

作者: DataStudio开发团队
创建时间: 2025-08-08
"""

import logging
import time
import threading
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum

from utils.serial_config_manager import SerialConfigManager
from utils.exceptions import DataProcessingError
from communication.serial_manager import SerialManager
from data_processing.frame_detector import FrameDetector, FrameDetectionConfig
from data_processing.data_parser import DataParser, ParsedData
from data_processing.response_validator import ResponseValidator, ValidationConfig, ValidationResult
from data_processing.queue_manager import QueueManager, QueueConfig


class WorkMode(Enum):
    """工作模式枚举"""
    IDLE = "idle"                    # 空闲状态
    SETUP = "setup"                  # 设置模式
    SINGLE_QUERY = "single_query"    # 单次查询模式
    CONTINUOUS = "continuous"        # 循环查询模式


class FlowState(Enum):
    """流程状态枚举"""
    STOPPED = "stopped"              # 已停止
    INITIALIZING = "initializing"    # 初始化中
    RUNNING = "running"              # 运行中
    PAUSED = "paused"               # 已暂停
    ERROR = "error"                 # 错误状态


@dataclass
class FlowResult:
    """流程执行结果"""
    step_name: str                   # 步骤名称
    command_results: List[Any]       # 指令执行结果列表
    execution_time: float            # 执行耗时
    success: bool                   # 是否成功
    error_message: Optional[str] = None  # 错误信息


@dataclass
class ContinuousDataResult:
    """连续数据处理结果"""
    frame_data: bytes               # 原始帧数据
    parsed_fields: List[ParsedData] # 解析后的字段数据
    timestamp: float                # 时间戳
    frame_number: int              # 帧序号


class ProtocolFlowController:
    """
    协议流程控制器
    
    核心功能：
    1. 协议流程管理和执行
    2. 串口通信协调
    3. 工作模式管理
    4. 业务状态管理
    5. 数据处理协调
    """
    
    def __init__(self, config_path: str):
        """
        初始化协议流程控制器
        
        Args:
            config_path: 协议配置文件路径
        """
        self.logger = logging.getLogger(f"{__name__}.ProtocolFlowController")
        
        # 加载协议配置
        self.config_manager = SerialConfigManager()
        self.protocol_config = self.config_manager.load_config(config_path)
        
        # 初始化组件
        self._initialize_components()
        
        # 状态管理
        self.current_mode = WorkMode.IDLE
        self.flow_state = FlowState.STOPPED
        self.frame_counter = 0
        
        # 线程管理
        self.continuous_thread = None
        self.stop_event = threading.Event()
        
        # 统计信息
        self.stats = {
            "flows_executed": 0,
            "flows_successful": 0,
            "flows_failed": 0,
            "commands_executed": 0,
            "commands_successful": 0,
            "commands_failed": 0,
            "frames_processed": 0,
            "fields_parsed": 0,
            "total_processing_time": 0.0
        }
        
        self.logger.info(f"协议流程控制器初始化完成 - 协议: {self.protocol_config.protocol_info.name}")
    
    def _initialize_components(self):
        """初始化所有组件"""
        # 初始化串口管理器
        self.serial_manager = SerialManager(
            config=self.protocol_config.serial_config,
            name="ProtocolFlowController_SerialManager"
        )
        
        # 初始化应答验证器
        self.response_validator = ResponseValidator()
        
        # 初始化连续数据处理组件（如果配置存在）
        if self.protocol_config.continuous_data:
            # 初始化帧检测器
            frame_config = FrameDetectionConfig.from_json_config({
                "header": self.protocol_config.continuous_data.frame_detection.header,
                "tail": self.protocol_config.continuous_data.frame_detection.tail,
                "min_length": self.protocol_config.continuous_data.frame_detection.min_length,
                "max_length": self.protocol_config.continuous_data.frame_detection.max_length
            })
            self.frame_detector = FrameDetector(frame_config)
            
            # 初始化数据解析器
            parsing_config = []
            for field in self.protocol_config.continuous_data.data_parsing:
                parsing_config.append({
                    "name": field.name,
                    "offset": field.offset,
                    "length": field.length,
                    "data_type": field.data_type,
                    "endian": field.endian,
                    "scale_factor": field.scale_factor,
                    "unit": field.unit
                })
            self.data_parser = DataParser(parsing_config)
            
            # 初始化队列管理器
            queue_config = QueueConfig(
                queue_size=1000,
                warning_threshold=0.8,
                batch_size=50
            )
            self.queue_manager = QueueManager(queue_config)
        else:
            self.frame_detector = None
            self.data_parser = None
            self.queue_manager = None
    
    def connect(self) -> bool:
        """
        连接串口设备
        
        Returns:
            连接是否成功
        """
        try:
            success = self.serial_manager.connect()
            if success:
                self.logger.info(f"串口连接成功: {self.protocol_config.serial_config.port}")
                self.flow_state = FlowState.STOPPED
            else:
                self.logger.error(f"串口连接失败: {self.protocol_config.serial_config.port}")
                self.flow_state = FlowState.ERROR
            return success
        except Exception as e:
            self.logger.error(f"串口连接异常: {str(e)}")
            self.flow_state = FlowState.ERROR
            return False
    
    def disconnect(self):
        """断开串口连接"""
        try:
            # 停止连续模式
            self.stop_continuous_mode()
            
            # 断开串口
            self.serial_manager.disconnect()
            self.flow_state = FlowState.STOPPED
            self.current_mode = WorkMode.IDLE
            self.logger.info("串口连接已断开")
        except Exception as e:
            self.logger.error(f"断开串口连接异常: {str(e)}")
    
    def execute_protocol_flow(self, step_name: str = None) -> List[FlowResult]:
        """
        执行协议流程
        
        Args:
            step_name: 步骤名称，None表示执行所有步骤
            
        Returns:
            流程执行结果列表
        """
        if self.flow_state == FlowState.ERROR:
            raise DataProcessingError("流程控制器处于错误状态，无法执行协议流程")
        
        self.flow_state = FlowState.RUNNING
        results = []
        
        try:
            # 获取要执行的步骤
            steps_to_execute = []
            if step_name:
                # 执行指定步骤
                for step in self.protocol_config.protocol_flow.steps:
                    if step.name == step_name:
                        steps_to_execute.append(step)
                        break
            else:
                # 执行所有步骤
                steps_to_execute = self.protocol_config.protocol_flow.steps
            
            # 执行步骤
            for step in steps_to_execute:
                start_time = time.time()
                self.logger.info(f"执行协议步骤: {step.name}")
                
                command_results = []
                step_success = True
                error_message = None
                
                for command_id in step.commands:
                    try:
                        result = self._execute_command(command_id)
                        command_results.append(result)
                        
                        if not result.success:
                            self.logger.warning(f"步骤 {step.name} 中的指令 {command_id} 执行失败")
                            step_success = False
                            
                    except Exception as e:
                        self.logger.error(f"步骤 {step.name} 中的指令 {command_id} 执行异常: {str(e)}")
                        step_success = False
                        error_message = str(e)
                        # 继续执行其他指令
                        continue
                
                execution_time = time.time() - start_time
                
                # 创建流程结果
                flow_result = FlowResult(
                    step_name=step.name,
                    command_results=command_results,
                    execution_time=execution_time,
                    success=step_success,
                    error_message=error_message
                )
                
                results.append(flow_result)
                
                # 更新统计信息
                self.stats["flows_executed"] += 1
                if step_success:
                    self.stats["flows_successful"] += 1
                else:
                    self.stats["flows_failed"] += 1
                self.stats["total_processing_time"] += execution_time
            
            self.flow_state = FlowState.STOPPED
            return results
            
        except Exception as e:
            self.flow_state = FlowState.ERROR
            self.logger.error(f"协议流程执行失败: {str(e)}")
            raise DataProcessingError(
                f"协议流程执行失败: {str(e)}",
                error_code="PROTOCOL_FLOW_ERROR",
                details={"error": str(e)}
            )
    
    def _execute_command(self, command_id: str):
        """
        执行单个指令（内部方法）

        Args:
            command_id: 指令ID

        Returns:
            指令执行结果
        """
        from business_logic.command_executor import CommandExecutor
        
        # 创建指令执行器
        executor = CommandExecutor(
            config_manager=self.config_manager,
            serial_manager=self.serial_manager,
            response_validator=self.response_validator
        )
        
        # 执行指令
        result = executor.execute_command(command_id)
        
        # 更新统计信息
        self.stats["commands_executed"] += 1
        if result.success:
            self.stats["commands_successful"] += 1
        else:
            self.stats["commands_failed"] += 1
        
        return result
    
    def start_continuous_mode(self, data_callback: Callable[[ContinuousDataResult], None] = None):
        """
        启动连续数据模式
        
        Args:
            data_callback: 数据处理回调函数
        """
        if not self.frame_detector or not self.data_parser:
            raise DataProcessingError(
                "连续数据处理组件未配置，无法启动连续模式",
                error_code="CONTINUOUS_DATA_NOT_CONFIGURED"
            )
        
        if self.current_mode == WorkMode.CONTINUOUS:
            self.logger.warning("连续模式已经在运行中")
            return
        
        self.current_mode = WorkMode.CONTINUOUS
        self.flow_state = FlowState.RUNNING
        self.stop_event.clear()
        
        # 启动连续数据处理线程
        self.continuous_thread = threading.Thread(
            target=self._continuous_data_worker,
            args=(data_callback,),
            daemon=True
        )
        self.continuous_thread.start()
        
        self.logger.info("连续数据模式已启动")
    
    def stop_continuous_mode(self):
        """停止连续数据模式"""
        if self.current_mode != WorkMode.CONTINUOUS:
            return
        
        self.stop_event.set()
        
        if self.continuous_thread and self.continuous_thread.is_alive():
            self.continuous_thread.join(timeout=5.0)
        
        self.current_mode = WorkMode.IDLE
        self.flow_state = FlowState.STOPPED
        self.logger.info("连续数据模式已停止")
    
    def _continuous_data_worker(self, data_callback: Callable[[ContinuousDataResult], None] = None):
        """
        连续数据处理工作线程
        
        Args:
            data_callback: 数据处理回调函数
        """
        self.logger.info("连续数据处理线程启动")
        
        try:
            while not self.stop_event.is_set():
                try:
                    # 读取串口数据
                    raw_data = self.serial_manager.read_data()
                    if not raw_data:
                        time.sleep(0.01)  # 短暂休眠避免CPU占用过高
                        continue
                    
                    # 处理连续数据
                    results = self._process_continuous_data(raw_data)
                    
                    # 调用回调函数
                    if data_callback:
                        for result in results:
                            data_callback(result)
                    
                except Exception as e:
                    self.logger.error(f"连续数据处理异常: {str(e)}")
                    time.sleep(0.1)  # 异常时稍长休眠
                    
        except Exception as e:
            self.logger.error(f"连续数据处理线程异常: {str(e)}")
            self.flow_state = FlowState.ERROR
        finally:
            self.logger.info("连续数据处理线程结束")
    
    def _process_continuous_data(self, raw_data: bytes) -> List[ContinuousDataResult]:
        """
        处理连续数据
        
        Args:
            raw_data: 原始数据
            
        Returns:
            连续数据处理结果列表
        """
        results = []
        
        try:
            # 帧检测
            frames = self.frame_detector.process_data(raw_data)
            
            # 处理每个检测到的帧
            for frame_data in frames:
                self.frame_counter += 1
                
                # 数据解析
                parsed_fields = self.data_parser.parse_frame(frame_data)
                
                # 创建结果
                result = ContinuousDataResult(
                    frame_data=frame_data,
                    parsed_fields=parsed_fields,
                    timestamp=time.time(),
                    frame_number=self.frame_counter
                )
                
                results.append(result)
                
                # 将结果放入队列
                if self.queue_manager:
                    self.queue_manager.put(result, "continuous_data")
                
                # 更新统计信息
                self.stats["frames_processed"] += 1
                self.stats["fields_parsed"] += len(parsed_fields)
            
            return results
            
        except Exception as e:
            self.logger.error(f"连续数据处理失败: {str(e)}")
            raise DataProcessingError(
                f"连续数据处理失败: {str(e)}",
                error_code="CONTINUOUS_DATA_PROCESSING_ERROR",
                details={"error": str(e)}
            )
    
    def get_protocol_info(self) -> Dict[str, Any]:
        """获取协议信息"""
        return {
            "name": self.protocol_config.protocol_info.name,
            "description": self.protocol_config.protocol_info.description,
            "version": self.protocol_config.protocol_info.version,
            "serial_config": {
                "port": self.protocol_config.serial_config.port,
                "baudrate": self.protocol_config.serial_config.baudrate,
                "databits": self.protocol_config.serial_config.databits,
                "parity": self.protocol_config.serial_config.parity,
                "stopbits": self.protocol_config.serial_config.stopbits,
                "timeout": self.protocol_config.serial_config.timeout
            },
            "single_commands": len(self.protocol_config.single_commands),
            "continuous_commands": len(self.protocol_config.continuous_commands),
            "protocol_steps": len(self.protocol_config.protocol_flow.steps),
            "continuous_data_configured": self.protocol_config.continuous_data is not None
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 添加组件统计信息
        if self.frame_detector:
            stats["frame_detector_stats"] = self.frame_detector.get_statistics()
        if self.data_parser:
            stats["data_parser_stats"] = self.data_parser.get_statistics()
        if self.queue_manager:
            stats["queue_manager_stats"] = self.queue_manager.get_statistics()
        
        stats["response_validator_stats"] = self.response_validator.get_statistics()
        stats["serial_manager_stats"] = self.serial_manager.get_statistics()
        
        # 计算成功率
        if stats["commands_executed"] > 0:
            stats["command_success_rate"] = stats["commands_successful"] / stats["commands_executed"] * 100
        else:
            stats["command_success_rate"] = 0.0
            
        if stats["flows_executed"] > 0:
            stats["flow_success_rate"] = stats["flows_successful"] / stats["flows_executed"] * 100
        else:
            stats["flow_success_rate"] = 0.0
        
        # 添加状态信息
        stats["current_mode"] = self.current_mode.value
        stats["flow_state"] = self.flow_state.value
        stats["is_connected"] = self.serial_manager.is_connected()
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            "flows_executed": 0,
            "flows_successful": 0,
            "flows_failed": 0,
            "commands_executed": 0,
            "commands_successful": 0,
            "commands_failed": 0,
            "frames_processed": 0,
            "fields_parsed": 0,
            "total_processing_time": 0.0
        }
        
        # 重置组件统计信息
        if self.frame_detector:
            self.frame_detector.reset_statistics()
        if self.data_parser:
            self.data_parser.reset_statistics()
        if self.queue_manager:
            self.queue_manager.reset_statistics()
        
        self.response_validator.reset_statistics()
        self.serial_manager.reset_statistics()
        self.frame_counter = 0
        
        self.logger.info("统计信息已重置")
    
    def get_queued_data(self, max_items: int = None) -> List[ContinuousDataResult]:
        """获取队列中的连续数据"""
        if not self.queue_manager:
            return []
        
        items = self.queue_manager.get_batch(max_items)
        return [item.data for item in items if item.item_type == "continuous_data"]
    
    def pause_continuous_mode(self):
        """暂停连续模式"""
        if self.current_mode == WorkMode.CONTINUOUS and self.flow_state == FlowState.RUNNING:
            self.flow_state = FlowState.PAUSED
            self.logger.info("连续模式已暂停")
    
    def resume_continuous_mode(self):
        """恢复连续模式"""
        if self.current_mode == WorkMode.CONTINUOUS and self.flow_state == FlowState.PAUSED:
            self.flow_state = FlowState.RUNNING
            self.logger.info("连续模式已恢复")
    
    def is_connected(self) -> bool:
        """检查串口连接状态"""
        return self.serial_manager.is_connected()
    
    def get_current_mode(self) -> WorkMode:
        """获取当前工作模式"""
        return self.current_mode
    
    def get_flow_state(self) -> FlowState:
        """获取当前流程状态"""
        return self.flow_state