"""
帧检测器模块 - 基于JSON配置的动态帧检测引擎

该模块实现了完全基于JSON配置驱动的帧检测功能，支持任意自由串口协议的帧检测。
核心特性：
- 动态配置驱动：所有帧检测逻辑完全由JSON配置决定，零硬编码
- 状态机实现：高效的帧检测状态机，支持复杂的帧结构
- 数据流处理：处理数据粘连、分片、不完整帧等复杂情况
- 协议无关：完全不依赖具体协议实现

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import logging
from typing import List, Optional, Dict, Any, Tuple
from enum import Enum
from dataclasses import dataclass
from utils.helper_utils import hex_to_bytes, bytes_to_hex
from utils.exceptions import DataProcessingError


class FrameDetectionState(Enum):
    """帧检测状态枚举"""
    SEARCHING_HEADER = "searching_header"    # 搜索帧头状态
    COLLECTING_DATA = "collecting_data"      # 收集数据状态
    VALIDATING_FRAME = "validating_frame"    # 验证帧状态


@dataclass
class FrameDetectionConfig:
    """帧检测配置数据类"""
    header: bytes           # 帧头字节序列
    tail: bytes            # 帧尾字节序列
    min_length: int        # 最小帧长度
    max_length: int        # 最大帧长度
    
    @classmethod
    def from_json_config(cls, config: Dict[str, Any]) -> 'FrameDetectionConfig':
        """从JSON配置创建帧检测配置对象"""
        try:
            header = hex_to_bytes(config["header"])
            tail = hex_to_bytes(config["tail"])
            min_length = config["min_length"]
            max_length = config["max_length"]
            
            # 验证配置合理性
            if min_length <= 0 or max_length <= 0:
                raise ValueError("帧长度必须大于0")
            if min_length > max_length:
                raise ValueError("最小帧长度不能大于最大帧长度")
            if len(header) == 0 or len(tail) == 0:
                raise ValueError("帧头和帧尾不能为空")
                
            return cls(header=header, tail=tail, min_length=min_length, max_length=max_length)
            
        except Exception as e:
            raise DataProcessingError(
                f"帧检测配置解析失败: {str(e)}",
                error_code="FRAME_CONFIG_ERROR",
                details={"config": config}
            )


class FrameDetector:
    """
    基于JSON配置的动态帧检测器
    
    核心功能：
    1. 动态配置驱动的帧检测
    2. 状态机实现的高效检测算法
    3. 数据粘连和分片处理
    4. 完整帧提取和验证
    """
    
    def __init__(self, config: FrameDetectionConfig):
        """
        初始化帧检测器
        
        Args:
            config: 帧检测配置对象
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.FrameDetector")
        
        # 状态机相关
        self.state = FrameDetectionState.SEARCHING_HEADER
        self.buffer = bytearray()  # 数据缓冲区
        self.current_frame = bytearray()  # 当前正在构建的帧
        
        # 统计信息
        self.stats = {
            "total_bytes_processed": 0,
            "frames_detected": 0,
            "invalid_frames": 0,
            "header_matches": 0,
            "tail_matches": 0
        }
        
        self.logger.info(f"帧检测器初始化完成 - 帧头: {bytes_to_hex(config.header)}, "
                        f"帧尾: {bytes_to_hex(config.tail)}, "
                        f"长度范围: {config.min_length}-{config.max_length}")
    
    def process_data(self, data: bytes) -> List[bytes]:
        """
        处理输入数据，检测并提取完整帧
        
        Args:
            data: 输入的原始数据
            
        Returns:
            检测到的完整帧列表
        """
        if not data:
            return []
            
        self.buffer.extend(data)
        self.stats["total_bytes_processed"] += len(data)
        
        frames = []
        
        # 状态机处理循环
        while len(self.buffer) > 0:
            buffer_len_before = len(self.buffer)

            if self.state == FrameDetectionState.SEARCHING_HEADER:
                frame = self._search_header()
                if frame:
                    frames.append(frame)
            elif self.state == FrameDetectionState.COLLECTING_DATA:
                frame = self._collect_data()
                if frame:
                    frames.append(frame)
            elif self.state == FrameDetectionState.VALIDATING_FRAME:
                frame = self._validate_frame()
                if frame:
                    frames.append(frame)
            else:
                # 未知状态，重置到搜索帧头状态
                self.logger.warning(f"未知状态: {self.state}，重置到搜索帧头状态")
                self.state = FrameDetectionState.SEARCHING_HEADER

            # 防止无限循环：如果缓冲区长度没有变化，说明没有数据被消耗
            if len(self.buffer) == buffer_len_before and len(self.buffer) > 0:
                # 如果在搜索帧头状态且缓冲区没有变化，清空缓冲区避免无限循环
                if self.state == FrameDetectionState.SEARCHING_HEADER:
                    self.logger.debug(f"搜索帧头时缓冲区无变化，清空缓冲区: {bytes_to_hex(bytes(self.buffer))}")
                    self.buffer.clear()
                else:
                    # 其他状态下强制重置到搜索帧头状态
                    self.logger.warning("检测到可能的无限循环，强制重置状态")
                    self._reset_to_search_header()
                    self.buffer.clear()
                break
                
        return frames
    
    def _search_header(self) -> Optional[bytes]:
        """搜索帧头"""
        header_len = len(self.config.header)
        
        # 在缓冲区中搜索帧头
        for i in range(len(self.buffer) - header_len + 1):
            if self.buffer[i:i + header_len] == self.config.header:
                # 找到帧头
                self.stats["header_matches"] += 1
                
                # 丢弃帧头之前的数据
                if i > 0:
                    discarded = self.buffer[:i]
                    self.logger.debug(f"丢弃帧头前数据: {bytes_to_hex(bytes(discarded))}")
                    self.buffer = self.buffer[i:]
                
                # 开始构建新帧
                self.current_frame = bytearray(self.config.header)
                self.buffer = self.buffer[header_len:]  # 移除已处理的帧头
                
                # 转换到收集数据状态
                self.state = FrameDetectionState.COLLECTING_DATA
                return self._collect_data()  # 立即尝试收集数据
                
        # 没有找到帧头，保留最后可能的部分帧头数据
        if len(self.buffer) >= header_len:
            # 保留可能的部分帧头
            keep_size = header_len - 1
            self.buffer = self.buffer[-keep_size:]
            
        return None
    
    def _collect_data(self) -> Optional[bytes]:
        """收集帧数据"""
        tail_len = len(self.config.tail)
        
        # 检查是否有足够的数据来检测帧尾
        while len(self.buffer) >= tail_len:
            # 检查当前位置是否是帧尾
            if self.buffer[:tail_len] == self.config.tail:
                # 找到帧尾
                self.stats["tail_matches"] += 1
                
                # 将帧尾添加到当前帧
                self.current_frame.extend(self.config.tail)
                self.buffer = self.buffer[tail_len:]  # 移除已处理的帧尾
                
                # 转换到验证帧状态
                self.state = FrameDetectionState.VALIDATING_FRAME
                return self._validate_frame()  # 立即验证帧
            else:
                # 不是帧尾，将一个字节添加到当前帧
                self.current_frame.append(self.buffer[0])
                self.buffer = self.buffer[1:]
                
                # 检查帧长度是否超过最大限制
                if len(self.current_frame) > self.config.max_length:
                    self.logger.debug(f"帧长度超过最大限制 {self.config.max_length}，重新搜索帧头")
                    self.stats["invalid_frames"] += 1
                    self._reset_to_search_header()
                    return None
                    
        return None
    
    def _validate_frame(self) -> Optional[bytes]:
        """验证完整帧"""
        frame_length = len(self.current_frame)
        
        # 验证帧长度
        if frame_length < self.config.min_length or frame_length > self.config.max_length:
            self.logger.debug(f"帧长度 {frame_length} 不在有效范围 "
                            f"{self.config.min_length}-{self.config.max_length} 内")
            self.stats["invalid_frames"] += 1
            self._reset_to_search_header()
            return None
        
        # 验证帧头和帧尾
        header_len = len(self.config.header)
        tail_len = len(self.config.tail)
        
        if (self.current_frame[:header_len] != self.config.header or 
            self.current_frame[-tail_len:] != self.config.tail):
            self.logger.debug("帧头或帧尾验证失败")
            self.stats["invalid_frames"] += 1
            self._reset_to_search_header()
            return None
        
        # 帧验证成功
        valid_frame = bytes(self.current_frame)
        self.stats["frames_detected"] += 1
        
        self.logger.debug(f"检测到有效帧: {bytes_to_hex(valid_frame)}")
        
        # 重置状态，准备检测下一帧
        self._reset_to_search_header()
        
        return valid_frame
    
    def _reset_to_search_header(self):
        """重置到搜索帧头状态"""
        self.state = FrameDetectionState.SEARCHING_HEADER
        self.current_frame.clear()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            "total_bytes_processed": 0,
            "frames_detected": 0,
            "invalid_frames": 0,
            "header_matches": 0,
            "tail_matches": 0
        }
        self.logger.info("统计信息已重置")
    
    def reset(self):
        """重置帧检测器状态"""
        self._reset_to_search_header()
        self.buffer.clear()
        self.reset_statistics()
        self.logger.info("帧检测器状态已重置")
