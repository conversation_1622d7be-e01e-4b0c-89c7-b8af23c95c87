"""
日志管理器 - 业务逻辑层组件

该模块实现了结构化日志记录和性能监控，负责：
- 结构化日志记录和性能监控
- 日志轮转和存储管理
- 日志级别控制和过滤
- 多格式日志输出

核心特性：
- 结构化日志：支持JSON格式的结构化日志记录
- 性能监控：集成性能指标监控和记录
- 自动轮转：支持按大小和时间的日志轮转
- 多输出：支持控制台、文件、网络等多种输出方式

作者: DataStudio开发团队
创建时间: 2025-08-08
"""

import logging
import logging.handlers
import json
import time
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class LogFormat(Enum):
    """日志格式枚举"""
    SIMPLE = "simple"           # 简单格式
    DETAILED = "detailed"       # 详细格式
    JSON = "json"              # JSON格式
    PERFORMANCE = "performance" # 性能监控格式


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: float            # 时间戳
    level: str                 # 日志级别
    logger_name: str           # 日志器名称
    message: str               # 日志消息
    module: str                # 模块名称
    function: str              # 函数名称
    line_number: int           # 行号
    thread_id: int             # 线程ID
    process_id: int            # 进程ID
    extra_data: Dict[str, Any] = None  # 额外数据


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str                  # 指标名称
    value: float               # 指标值
    unit: str                  # 单位
    timestamp: float           # 时间戳
    tags: Dict[str, str] = None  # 标签


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record):
        """格式化日志记录为JSON"""
        log_entry = {
            "timestamp": record.created,
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.thread,
            "process": record.process
        }
        
        # 添加额外数据
        if hasattr(record, 'extra_data') and record.extra_data:
            log_entry["extra"] = record.extra_data
        
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry, ensure_ascii=False)


class PerformanceFormatter(logging.Formatter):
    """性能监控格式化器"""
    
    def format(self, record):
        """格式化性能日志"""
        if hasattr(record, 'performance_data'):
            perf_data = record.performance_data
            return f"[PERF] {perf_data['name']}: {perf_data['value']}{perf_data['unit']} | {record.getMessage()}"
        else:
            return super().format(record)


class LoggerManager:
    """
    日志管理器
    
    核心功能：
    1. 结构化日志记录和管理
    2. 性能监控和指标记录
    3. 日志轮转和存储管理
    4. 多格式日志输出
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化日志管理器
        
        Args:
            config: 日志配置
        """
        self.config = config or {}
        self.loggers: Dict[str, logging.Logger] = {}
        self.performance_metrics: List[PerformanceMetric] = []
        
        # 默认配置
        self.default_config = {
            "log_level": LogLevel.INFO,
            "log_format": LogFormat.DETAILED,
            "log_dir": "logs",
            "max_file_size": 10 * 1024 * 1024,  # 10MB
            "backup_count": 5,
            "enable_console": True,
            "enable_file": True,
            "enable_performance": True
        }
        
        # 合并配置
        self.effective_config = {**self.default_config, **self.config}
        
        # 创建日志目录
        self._create_log_directory()
        
        # 初始化根日志器
        self._setup_root_logger()
        
        # 性能监控
        self.performance_enabled = self.effective_config["enable_performance"]
        
        self.logger = self.get_logger("LoggerManager")
        self.logger.info("日志管理器初始化完成")
    
    def _create_log_directory(self):
        """创建日志目录"""
        log_dir = Path(self.effective_config["log_dir"])
        log_dir.mkdir(parents=True, exist_ok=True)
    
    def _setup_root_logger(self):
        """设置根日志器"""
        root_logger = logging.getLogger()
        root_logger.setLevel(self.effective_config["log_level"].value)
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 添加控制台处理器
        if self.effective_config["enable_console"]:
            console_handler = self._create_console_handler()
            root_logger.addHandler(console_handler)
        
        # 添加文件处理器
        if self.effective_config["enable_file"]:
            file_handler = self._create_file_handler()
            root_logger.addHandler(file_handler)
    
    def _create_console_handler(self) -> logging.Handler:
        """创建控制台处理器"""
        handler = logging.StreamHandler()
        handler.setLevel(self.effective_config["log_level"].value)
        
        # 设置格式化器
        formatter = self._create_formatter(self.effective_config["log_format"])
        handler.setFormatter(formatter)
        
        return handler
    
    def _create_file_handler(self) -> logging.Handler:
        """创建文件处理器"""
        log_file = Path(self.effective_config["log_dir"]) / "datastudio.log"
        
        handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=self.effective_config["max_file_size"],
            backupCount=self.effective_config["backup_count"],
            encoding='utf-8'
        )
        handler.setLevel(self.effective_config["log_level"].value)
        
        # 设置格式化器
        formatter = self._create_formatter(self.effective_config["log_format"])
        handler.setFormatter(formatter)
        
        return handler
    
    def _create_formatter(self, format_type: LogFormat) -> logging.Formatter:
        """创建格式化器"""
        if format_type == LogFormat.SIMPLE:
            return logging.Formatter(
                fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        elif format_type == LogFormat.DETAILED:
            return logging.Formatter(
                fmt='%(asctime)s - %(name)s - %(levelname)s - [%(module)s:%(funcName)s:%(lineno)d] - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        elif format_type == LogFormat.JSON:
            return JSONFormatter()
        elif format_type == LogFormat.PERFORMANCE:
            return PerformanceFormatter()
        else:
            return logging.Formatter()
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            日志器实例
        """
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def log_performance(self, name: str, value: float, unit: str = "", 
                       tags: Dict[str, str] = None, message: str = ""):
        """
        记录性能指标
        
        Args:
            name: 指标名称
            value: 指标值
            unit: 单位
            tags: 标签
            message: 附加消息
        """
        if not self.performance_enabled:
            return
        
        # 创建性能指标
        metric = PerformanceMetric(
            name=name,
            value=value,
            unit=unit,
            timestamp=time.time(),
            tags=tags or {}
        )
        
        self.performance_metrics.append(metric)
        
        # 记录性能日志
        perf_logger = self.get_logger("Performance")
        
        # 创建日志记录
        record = logging.LogRecord(
            name="Performance",
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg=message or f"Performance metric: {name}",
            args=(),
            exc_info=None
        )
        
        # 添加性能数据
        record.performance_data = {
            "name": name,
            "value": value,
            "unit": unit,
            "tags": tags or {}
        }
        
        perf_logger.handle(record)
    
    def log_structured(self, logger_name: str, level: LogLevel, message: str,
                      extra_data: Dict[str, Any] = None):
        """
        记录结构化日志
        
        Args:
            logger_name: 日志器名称
            level: 日志级别
            message: 日志消息
            extra_data: 额外数据
        """
        logger = self.get_logger(logger_name)
        
        # 创建日志记录
        record = logging.LogRecord(
            name=logger_name,
            level=level.value,
            pathname="",
            lineno=0,
            msg=message,
            args=(),
            exc_info=None
        )
        
        # 添加额外数据
        if extra_data:
            record.extra_data = extra_data
        
        logger.handle(record)
    
    def create_component_logger(self, component_name: str, 
                              log_file: str = None) -> logging.Logger:
        """
        为组件创建专用日志器
        
        Args:
            component_name: 组件名称
            log_file: 专用日志文件名
            
        Returns:
            组件日志器
        """
        logger = self.get_logger(component_name)
        
        # 如果指定了专用日志文件，添加文件处理器
        if log_file:
            log_path = Path(self.effective_config["log_dir"]) / log_file
            
            file_handler = logging.handlers.RotatingFileHandler(
                filename=log_path,
                maxBytes=self.effective_config["max_file_size"],
                backupCount=self.effective_config["backup_count"],
                encoding='utf-8'
            )
            file_handler.setLevel(self.effective_config["log_level"].value)
            
            formatter = self._create_formatter(self.effective_config["log_format"])
            file_handler.setFormatter(formatter)
            
            logger.addHandler(file_handler)
        
        return logger
    
    def set_log_level(self, level: LogLevel, logger_name: str = None):
        """
        设置日志级别
        
        Args:
            level: 日志级别
            logger_name: 日志器名称，None表示设置根日志器
        """
        if logger_name:
            logger = self.get_logger(logger_name)
            logger.setLevel(level.value)
        else:
            logging.getLogger().setLevel(level.value)
        
        self.logger.info(f"日志级别已设置为: {level.name}")
    
    def add_file_handler(self, logger_name: str, file_path: str,
                        format_type: LogFormat = LogFormat.DETAILED):
        """
        为指定日志器添加文件处理器
        
        Args:
            logger_name: 日志器名称
            file_path: 文件路径
            format_type: 格式类型
        """
        logger = self.get_logger(logger_name)
        
        file_handler = logging.handlers.RotatingFileHandler(
            filename=file_path,
            maxBytes=self.effective_config["max_file_size"],
            backupCount=self.effective_config["backup_count"],
            encoding='utf-8'
        )
        file_handler.setLevel(self.effective_config["log_level"].value)
        
        formatter = self._create_formatter(format_type)
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        
        self.logger.info(f"已为日志器 {logger_name} 添加文件处理器: {file_path}")
    
    def get_performance_metrics(self, time_window: float = 300.0) -> List[PerformanceMetric]:
        """
        获取性能指标
        
        Args:
            time_window: 时间窗口（秒）
            
        Returns:
            性能指标列表
        """
        current_time = time.time()
        window_start = current_time - time_window
        
        return [metric for metric in self.performance_metrics 
                if metric.timestamp >= window_start]
    
    def get_performance_summary(self, time_window: float = 300.0) -> Dict[str, Any]:
        """
        获取性能摘要
        
        Args:
            time_window: 时间窗口（秒）
            
        Returns:
            性能摘要
        """
        metrics = self.get_performance_metrics(time_window)
        
        # 按指标名称分组
        metric_groups = {}
        for metric in metrics:
            if metric.name not in metric_groups:
                metric_groups[metric.name] = []
            metric_groups[metric.name].append(metric.value)
        
        # 计算统计信息
        summary = {}
        for name, values in metric_groups.items():
            if values:
                summary[name] = {
                    "count": len(values),
                    "min": min(values),
                    "max": max(values),
                    "avg": sum(values) / len(values),
                    "total": sum(values)
                }
        
        return summary
    
    def clear_performance_metrics(self):
        """清空性能指标"""
        self.performance_metrics.clear()
        self.logger.info("性能指标已清空")
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            "total_loggers": len(self.loggers),
            "logger_names": list(self.loggers.keys()),
            "performance_metrics_count": len(self.performance_metrics),
            "log_level": self.effective_config["log_level"].name,
            "log_format": self.effective_config["log_format"].value,
            "log_directory": self.effective_config["log_dir"],
            "console_enabled": self.effective_config["enable_console"],
            "file_enabled": self.effective_config["enable_file"],
            "performance_enabled": self.performance_enabled
        }
        
        # 添加日志文件信息
        log_dir = Path(self.effective_config["log_dir"])
        if log_dir.exists():
            log_files = list(log_dir.glob("*.log*"))
            stats["log_files"] = [str(f) for f in log_files]
            stats["total_log_files"] = len(log_files)
            
            # 计算日志文件总大小
            total_size = sum(f.stat().st_size for f in log_files if f.is_file())
            stats["total_log_size_bytes"] = total_size
            stats["total_log_size_mb"] = total_size / (1024 * 1024)
        
        return stats
    
    def cleanup_old_logs(self, days: int = 30):
        """
        清理旧日志文件
        
        Args:
            days: 保留天数
        """
        log_dir = Path(self.effective_config["log_dir"])
        if not log_dir.exists():
            return
        
        cutoff_time = time.time() - (days * 24 * 60 * 60)
        deleted_count = 0
        
        for log_file in log_dir.glob("*.log*"):
            if log_file.is_file() and log_file.stat().st_mtime < cutoff_time:
                try:
                    log_file.unlink()
                    deleted_count += 1
                except Exception as e:
                    self.logger.error(f"删除日志文件失败: {log_file}, 错误: {str(e)}")
        
        self.logger.info(f"清理完成，删除了 {deleted_count} 个旧日志文件")
    
    def export_logs(self, output_file: str, time_window: float = 3600.0,
                   format_type: LogFormat = LogFormat.JSON):
        """
        导出日志
        
        Args:
            output_file: 输出文件路径
            time_window: 时间窗口（秒）
            format_type: 导出格式
        """
        # 这里可以实现日志导出功能
        # 由于日志已经写入文件，这里主要是格式转换和筛选
        self.logger.info(f"日志导出功能待实现: {output_file}")
    
    def shutdown(self):
        """关闭日志管理器"""
        # 关闭所有处理器
        for logger in self.loggers.values():
            for handler in logger.handlers[:]:
                handler.close()
                logger.removeHandler(handler)
        
        # 关闭根日志器的处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            handler.close()
            root_logger.removeHandler(handler)
        
        self.logger.info("日志管理器已关闭")