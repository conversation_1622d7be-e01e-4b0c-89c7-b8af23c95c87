<thought>
  <exploration>
    ## 上下文信息源探索
    - **对话历史分析**：用户的历史需求、决策过程、关注点变化
    - **当前模式文档**：正在执行的模式的文档状态和关键信息
    - **前序模式文档**：已完成模式的决策结果和约束条件
    - **项目环境状态**：代码库状态、配置变更、测试结果
    - **外部依赖状态**：硬件环境、第三方库版本、系统环境
  </exploration>
  
  <reasoning>
    ## 上下文关联性分析逻辑
    - **直接相关性**：与当前任务直接相关的信息
    - **间接相关性**：可能影响当前决策的背景信息
    - **潜在冲突性**：可能存在冲突的信息和决策
    - **历史一致性**：与历史决策的一致性检查
    - **未来影响性**：对后续工作可能产生的影响
  </reasoning>
  
  <challenge>
    ## 上下文质量挑战
    - **信息完整性挑战**：是否遗漏了关键信息？
    - **信息准确性挑战**：信息是否准确和最新？
    - **信息相关性挑战**：信息是否与当前任务相关？
    - **信息冲突性挑战**：是否存在相互冲突的信息？
    - **信息时效性挑战**：信息是否仍然有效？
  </challenge>
  
  <plan>
    ## 上下文管理执行计划
    1. **信息收集阶段**：多源信息的系统性收集
    2. **信息分析阶段**：关联性分析和优先级排序
    3. **信息整合阶段**：生成结构化的上下文包
    4. **信息传递阶段**：向目标角色传递上下文
    5. **效果评估阶段**：跟踪上下文使用效果
  </plan>
</thought>
