@echo off
REM DataStudio 项目打包脚本
REM 使用PyInstaller打包DataStudio数据采集系统
REM 
REM 作者: AI Assistant
REM 创建时间: 2025-08-08
REM 平台: Windows PowerShell

echo ========================================
echo DataStudio 项目打包工具
echo ========================================
echo.

REM 检查Python环境
echo [1/6] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python环境
    echo 请确保Python已安装并添加到PATH环境变量
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

REM 检查PyInstaller
echo.
echo [2/6] 检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  PyInstaller未安装，正在安装...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ❌ 错误: PyInstaller安装失败
        pause
        exit /b 1
    )
    echo ✅ PyInstaller安装成功
) else (
    echo ✅ PyInstaller已安装
)

REM 检查项目依赖
echo.
echo [3/6] 检查项目依赖...
python -c "import serial, jsonschema" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  项目依赖不完整，正在安装...
    pip install pyserial>=3.5 jsonschema>=3.2.0
    if %errorlevel% neq 0 (
        echo ❌ 错误: 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 项目依赖安装成功
) else (
    echo ✅ 项目依赖检查通过
)

REM 清理旧的构建文件
echo.
echo [4/6] 清理旧的构建文件...
if exist "build" (
    rmdir /s /q "build"
    echo ✅ 清理build目录
)
if exist "dist" (
    rmdir /s /q "dist"
    echo ✅ 清理dist目录
)
if exist "__pycache__" (
    rmdir /s /q "__pycache__"
    echo ✅ 清理__pycache__目录
)

REM 递归清理所有__pycache__目录
for /d /r . %%d in (__pycache__) do @if exist "%%d" rmdir /s /q "%%d"
echo ✅ 清理所有缓存文件

REM 执行打包
echo.
echo [5/6] 开始打包...
echo 使用配置文件: DataStudio.spec
echo.
pyinstaller DataStudio.spec --clean --noconfirm
if %errorlevel% neq 0 (
    echo ❌ 错误: 打包失败
    pause
    exit /b 1
)

REM 验证打包结果
echo.
echo [6/6] 验证打包结果...
if exist "dist\DataStudio\DataStudio.exe" (
    echo ✅ 打包成功！
    echo.
    echo 📁 输出目录: dist\DataStudio\
    echo 🚀 可执行文件: dist\DataStudio\DataStudio.exe
    echo 📋 配置文件: dist\DataStudio\config\
    echo.
    echo ========================================
    echo 打包完成！
    echo ========================================
    echo.
    echo 💡 使用说明:
    echo 1. 进入 dist\DataStudio\ 目录
    echo 2. 运行 DataStudio.exe
    echo 3. 选择配置文件进行测试
    echo.
) else (
    echo ❌ 错误: 打包文件未找到
    echo 请检查打包过程中的错误信息
    pause
    exit /b 1
)

echo 按任意键退出...
pause >nul
