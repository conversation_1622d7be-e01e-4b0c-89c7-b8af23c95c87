#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通信抽象层演示脚本
展示如何使用通信抽象层的各个组件

演示内容：
1. 基本的串口管理器使用
2. 缓冲区管理器的数据处理
3. 连接池的连接复用
4. 完整的通信流程演示

作者: LD (Lead Developer)
创建时间: 2025-08-07
版本: 1.0
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.serial_config_manager import SerialConfig
from utils.logging_config import LoggingConfig
from communication import (
    SerialManager, BufferManager, ConnectionPool,
    create_serial_manager, create_buffer_manager, create_connection_pool,
    get_module_info
)


def setup_logging():
    """设置日志系统"""
    logging_config = LoggingConfig()
    logging_config.setup_logging()
    print("日志系统初始化完成")


def demo_module_info():
    """演示模块信息"""
    print("\n" + "=" * 60)
    print("通信抽象层模块信息")
    print("=" * 60)
    
    info = get_module_info()
    print(f"模块名称: {info['name']}")
    print(f"版本: {info['version']}")
    print(f"作者: {info['author']}")
    print(f"描述: {info['description']}")
    
    print("\n组件列表:")
    for component, description in info['components'].items():
        print(f"  • {component}: {description}")
    
    print("\n核心特性:")
    for feature in info['features']:
        print(f"  • {feature}")


def demo_serial_manager():
    """演示串口管理器"""
    print("\n" + "=" * 60)
    print("串口管理器演示")
    print("=" * 60)
    
    # 创建串口配置
    config = SerialConfig(
        port="COM2",
        baudrate=9600,
        databits=8,
        parity="none",
        stopbits=1,
        timeout=1.0
    )
    
    # 使用便捷函数创建串口管理器
    manager = create_serial_manager(config, "DemoSerial")
    
    try:
        # 连接串口
        if manager.connect():
            print("✅ 串口连接成功")
            
            # 发送测试数据
            test_data = b"Hello Communication Layer!"
            bytes_written = manager.write_data(test_data)
            print(f"发送数据: {test_data.decode()} ({bytes_written} 字节)")
            
            # 尝试读取数据
            time.sleep(0.1)
            received_data = manager.read_available_data()
            if received_data:
                print(f"接收数据: {received_data} ({len(received_data)} 字节)")
            else:
                print("未接收到数据（虚拟串口特性）")
            
            # 显示统计信息
            stats = manager.get_stats()
            print(f"统计信息: 发送={stats['bytes_sent']}, 接收={stats['bytes_received']}")
            
        else:
            print("⚠️  串口连接失败")
            
    except Exception as e:
        print(f"❌ 串口管理器演示异常: {e}")
    finally:
        manager.disconnect()
        print("串口已断开")


def demo_buffer_manager():
    """演示缓冲区管理器"""
    print("\n" + "=" * 60)
    print("缓冲区管理器演示")
    print("=" * 60)
    
    # 使用便捷函数创建缓冲区管理器
    buffer = create_buffer_manager(size=1024, warning_threshold=0.8, name="DemoBuffer")
    
    try:
        print(f"缓冲区初始状态: {buffer}")
        
        # 写入一些测试数据
        test_messages = [
            b"Message 1: Buffer Demo",
            b"Message 2: Thread Safe",
            b"Message 3: High Performance"
        ]
        
        for i, message in enumerate(test_messages):
            bytes_written = buffer.write(message)
            print(f"写入消息 {i+1}: {message.decode()} ({bytes_written} 字节)")
            print(f"  缓冲区状态: {buffer.available_data()} 字节数据, 使用率 {buffer.get_usage_rate():.1%}")
        
        # 预览数据
        preview_data = buffer.peek(20)
        print(f"预览数据: {preview_data}")
        
        # 读取所有数据
        print("\n开始读取数据:")
        read_count = 0
        max_reads = 10  # 防止无限循环
        while not buffer.is_empty() and read_count < max_reads:
            available = buffer.available_data()
            read_size = min(25, available)  # 确保不超过可用数据
            if read_size > 0:
                data = buffer.read(read_size)
                if data:
                    print(f"读取: {data}")
                    read_count += 1
                else:
                    break  # 没有数据时退出
            else:
                break  # 没有可用数据时退出

        print(f"缓冲区最终状态: {buffer}")
        
        # 显示统计信息
        stats = buffer.get_stats()
        print(f"统计信息: 写入={stats['total_written']}, 读取={stats['total_read']}, 最大使用率={stats['max_usage']:.1%}")
        
    except Exception as e:
        print(f"❌ 缓冲区管理器演示异常: {e}")


def demo_connection_pool():
    """演示连接池管理器"""
    print("\n" + "=" * 60)
    print("连接池管理器演示")
    print("=" * 60)
    
    # 使用便捷函数创建连接池
    pool = create_connection_pool(max_connections=2, name="DemoPool")
    
    try:
        print(f"连接池初始状态: {pool}")
        
        # 创建多个串口配置
        configs = [
            SerialConfig(port="COM2", baudrate=9600, databits=8, parity="none", stopbits=1, timeout=1.0),
            SerialConfig(port="COM3", baudrate=9600, databits=8, parity="none", stopbits=1, timeout=1.0)
        ]
        
        managers = []
        
        # 从连接池获取连接
        for i, config in enumerate(configs):
            try:
                manager = pool.get_connection(config)
                managers.append(manager)
                print(f"✅ 获取连接 {i+1}: {config.port}")
                
                # 发送测试数据
                test_data = f"Pool Test {i+1}".encode()
                bytes_written = manager.write_data(test_data)
                print(f"  发送数据: {bytes_written} 字节")
                
            except Exception as e:
                print(f"⚠️  获取连接 {i+1} 失败: {e}")
        
        # 显示连接池统计
        stats = pool.get_pool_stats()
        print(f"\n连接池统计:")
        print(f"  当前连接数: {stats['current_connections']}")
        print(f"  活跃连接数: {stats['active_connections']}")
        print(f"  总请求数: {stats['total_requests']}")
        print(f"  缓存命中: {stats['cache_hits']}")
        print(f"  缓存未命中: {stats['cache_misses']}")
        
        # 执行健康检查
        health_results = pool.health_check()
        print(f"\n健康检查结果: {health_results}")
        
        # 释放连接
        for i, config in enumerate(configs):
            pool.release_connection(config.port)
            print(f"释放连接 {i+1}: {config.port}")
        
    except Exception as e:
        print(f"❌ 连接池管理器演示异常: {e}")
    finally:
        pool.shutdown()
        print("连接池已关闭")


def demo_integrated_workflow():
    """演示完整的集成工作流"""
    print("\n" + "=" * 60)
    print("完整集成工作流演示")
    print("=" * 60)
    
    # 创建所有组件
    pool = create_connection_pool(max_connections=1, name="WorkflowPool")
    buffer = create_buffer_manager(size=2048, name="WorkflowBuffer")
    
    try:
        config = SerialConfig(
            port="COM2",
            baudrate=9600,
            databits=8,
            parity="none",
            stopbits=1,
            timeout=1.0
        )
        
        # 从连接池获取串口管理器
        serial_manager = pool.get_connection(config)
        
        if serial_manager.is_connected():
            print("✅ 完整工作流连接成功")
            
            # 模拟数据处理流程
            workflow_data = [
                b"Workflow Step 1: Data Collection",
                b"Workflow Step 2: Data Processing", 
                b"Workflow Step 3: Data Analysis"
            ]
            
            for step, data in enumerate(workflow_data, 1):
                print(f"\n--- 工作流步骤 {step} ---")
                
                # 1. 发送数据到串口
                bytes_written = serial_manager.write_data(data)
                print(f"1. 串口发送: {bytes_written} 字节")
                
                # 2. 模拟接收数据并写入缓冲区
                buffer.write(data)  # 模拟接收到的数据
                print(f"2. 缓冲区写入: {len(data)} 字节")
                
                # 3. 从缓冲区读取并处理数据
                processed_data = buffer.read(len(data))
                print(f"3. 数据处理: {processed_data.decode()}")
                
                # 4. 显示当前状态
                print(f"4. 串口状态: 发送={serial_manager.get_stats()['bytes_sent']} 字节")
                print(f"   缓冲区状态: 使用率={buffer.get_usage_rate():.1%}")
                print(f"   连接池状态: {pool.get_pool_stats()['active_connections']} 个活跃连接")
            
            print("\n✅ 完整工作流演示完成")
        else:
            print("⚠️  工作流连接失败")
            
    except Exception as e:
        print(f"❌ 完整工作流演示异常: {e}")
    finally:
        pool.shutdown()


def main():
    """主函数"""
    print("通信抽象层演示程序")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    
    # 运行所有演示
    demos = [
        demo_module_info,
        demo_serial_manager,
        demo_buffer_manager,
        demo_connection_pool,
        demo_integrated_workflow
    ]
    
    for demo in demos:
        try:
            demo()
        except Exception as e:
            print(f"❌ 演示 {demo.__name__} 失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 通信抽象层演示完成！")
    print("✅ 所有组件工作正常，可以为上层提供稳定的通信服务")
    print("=" * 60)


if __name__ == "__main__":
    main()
