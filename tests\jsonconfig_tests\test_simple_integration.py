#!/usr/bin/env python3
"""
简化的数据处理层集成测试

测试基本的集成功能，避免复杂的并发测试。

运行方式：
python test_simple_integration.py

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import sys
import os
import unittest
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

print("开始导入模块...")

try:
    from data_processing.frame_detector import FrameDetector, FrameDetectionConfig
    print("✓ FrameDetector 导入成功")
except Exception as e:
    print(f"✗ FrameDetector 导入失败: {e}")
    sys.exit(1)

try:
    from data_processing.data_parser import DataParser
    print("✓ DataParser 导入成功")
except Exception as e:
    print(f"✗ DataParser 导入失败: {e}")
    sys.exit(1)

try:
    from data_processing.response_validator import ResponseValidator, ValidationConfig
    print("✓ ResponseValidator 导入成功")
except Exception as e:
    print(f"✗ ResponseValidator 导入失败: {e}")
    sys.exit(1)

try:
    from data_processing.queue_manager import QueueManager, QueueConfig
    print("✓ QueueManager 导入成功")
except Exception as e:
    print(f"✗ QueueManager 导入失败: {e}")
    sys.exit(1)

try:
    from utils.helper_utils import hex_to_bytes, bytes_to_hex
    print("✓ helper_utils 导入成功")
except Exception as e:
    print(f"✗ helper_utils 导入失败: {e}")
    sys.exit(1)

print("所有模块导入成功！")


class TestSimpleIntegration(unittest.TestCase):
    """简化的集成测试"""
    
    def setUp(self):
        """测试前准备"""
        print("设置测试环境...")
        
        # 加载IMU948配置
        config_path = project_root / "config" / "protocols" / "imu948_example.json"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            self.protocol_config = json.load(f)
        
        print("配置文件加载成功")
    
    def test_frame_detection_basic(self):
        """测试基本帧检测功能"""
        print("测试帧检测...")
        
        # 创建帧检测器
        frame_config_data = self.protocol_config["continuous_data"]["frame_detection"]
        frame_config = FrameDetectionConfig.from_json_config(frame_config_data)
        detector = FrameDetector(frame_config)
        
        # 测试数据
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        
        # 检测帧
        frames = detector.process_data(frame_data)
        
        self.assertEqual(len(frames), 1)
        self.assertEqual(frames[0], frame_data)
        print("✓ 帧检测测试通过")
    
    def test_data_parsing_basic(self):
        """测试基本数据解析功能"""
        print("测试数据解析...")
        
        # 创建数据解析器
        parsing_config = self.protocol_config["continuous_data"]["data_parsing"]
        parser = DataParser(parsing_config)
        
        # 测试数据
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        
        # 解析数据
        results = parser.parse_frame(frame_data)
        
        self.assertEqual(len(results), 6)
        
        # 验证字段名
        field_names = [result.field_name for result in results]
        expected_names = [
            "Roll_滚转角", "Pitch_俯仰角", "Yaw_偏航角",
            "Position_X", "Position_Y", "Position_Z"
        ]
        self.assertEqual(field_names, expected_names)
        print("✓ 数据解析测试通过")
    
    def test_response_validation_basic(self):
        """测试基本应答验证功能"""
        print("测试应答验证...")
        
        # 创建应答验证器
        validator = ResponseValidator()
        
        # 测试配置
        config = ValidationConfig(
            validation_type="exact",
            pattern="49 00 01 18 19 4D",
            timeout=1.0,
            retry_count=1
        )
        
        # 测试正确应答
        correct_response = hex_to_bytes("49 00 01 18 19 4D")
        result = validator.validate_response(correct_response, config)
        
        self.assertTrue(result.matched)
        print("✓ 应答验证测试通过")
    
    def test_queue_management_basic(self):
        """测试基本队列管理功能"""
        print("测试队列管理...")
        
        # 创建队列管理器
        config = QueueConfig(
            queue_size=10,
            warning_threshold=0.8,
            batch_size=5
        )
        queue_manager = QueueManager(config)
        
        # 测试添加和获取
        test_data = "test_data"
        success = queue_manager.put(test_data)
        self.assertTrue(success)
        
        item = queue_manager.get(block=False)
        self.assertIsNotNone(item)
        self.assertEqual(item.data, test_data)
        print("✓ 队列管理测试通过")
    
    def test_end_to_end_processing(self):
        """测试端到端处理"""
        print("测试端到端处理...")
        
        # 创建所有组件
        frame_config_data = self.protocol_config["continuous_data"]["frame_detection"]
        frame_config = FrameDetectionConfig.from_json_config(frame_config_data)
        detector = FrameDetector(frame_config)
        
        parsing_config = self.protocol_config["continuous_data"]["data_parsing"]
        parser = DataParser(parsing_config)
        
        queue_config = QueueConfig(queue_size=20, warning_threshold=0.8, batch_size=10)
        queue_manager = QueueManager(queue_config)
        
        # 测试数据
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        
        # 端到端处理
        # 1. 帧检测
        frames = detector.process_data(frame_data)
        self.assertEqual(len(frames), 1)
        
        # 2. 数据解析
        parsed_data = parser.parse_frame(frames[0])
        self.assertEqual(len(parsed_data), 6)
        
        # 3. 队列管理
        for data_item in parsed_data:
            queue_manager.put(data_item)
        
        # 4. 批量获取
        items = queue_manager.get_batch()
        self.assertEqual(len(items), 6)
        
        print("✓ 端到端处理测试通过")


def run_simple_tests():
    """运行简化测试"""
    print("=" * 60)
    print("数据处理层简化集成测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    tests = unittest.TestLoader().loadTestsFromTestCase(TestSimpleIntegration)
    test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("简化集成测试结果汇总")
    print("=" * 60)
    print(f"运行测试数量: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n测试通过率: {success_rate:.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_simple_tests()
    sys.exit(0 if success else 1)
