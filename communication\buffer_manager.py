#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓冲区管理器 - 线程安全的循环缓冲区实现
提供固定大小的循环缓冲区，支持多线程环境下的安全读写操作

核心特性:
- 固定大小循环缓冲区: 4096字节默认大小，避免内存无限增长
- 线程安全: 使用RLock和条件变量确保多线程安全
- 状态监控: 实时监控缓冲区使用率和状态
- 高性能: 优化的读写算法，支持批量操作
- 异常处理: 完善的溢出和下溢处理机制

作者: LD (Lead Developer)
创建时间: 2025-08-07
版本: 1.0
"""

import threading
import time
from typing import Optional, Dict, Any, Tuple
from enum import Enum
import logging

from utils.exceptions import BufferOverflowError, BufferUnderflowError
from utils.constants import ConfigLimits


class BufferState(Enum):
    """缓冲区状态枚举"""
    EMPTY = "empty"
    NORMAL = "normal"
    WARNING = "warning"  # 使用率超过警告阈值
    FULL = "full"


class BufferManager:
    """
    循环缓冲区管理器 - 线程安全的固定大小循环缓冲区
    
    提供高效的字节数据存储和检索，支持多线程环境下的安全操作。
    使用循环缓冲区算法，避免频繁的内存分配和释放。
    """
    
    def __init__(self, size: int = 4096, warning_threshold: float = 0.8, 
                 name: str = "BufferManager"):
        """
        初始化缓冲区管理器
        
        Args:
            size: 缓冲区大小（字节）
            warning_threshold: 警告阈值（0.0-1.0）
            name: 管理器名称，用于日志标识
        """
        # 验证参数
        if size < ConfigLimits.MIN_BUFFER_SIZE or size > ConfigLimits.MAX_BUFFER_SIZE:
            raise ValueError(f"缓冲区大小必须在 {ConfigLimits.MIN_BUFFER_SIZE} - {ConfigLimits.MAX_BUFFER_SIZE} 之间")
        
        if not 0.0 <= warning_threshold <= 1.0:
            raise ValueError("警告阈值必须在 0.0 - 1.0 之间")
        
        self._size = size
        self._warning_threshold = warning_threshold
        self._name = name
        self._logger = logging.getLogger(f"datastudio.communication.{name}")
        
        # 缓冲区数据
        self._buffer = bytearray(size)
        self._read_pos = 0  # 读取位置
        self._write_pos = 0  # 写入位置
        self._data_size = 0  # 当前数据大小
        
        # 线程同步
        self._lock = threading.RLock()
        self._not_empty = threading.Condition(self._lock)
        self._not_full = threading.Condition(self._lock)
        
        # 状态管理
        self._state = BufferState.EMPTY
        self._last_warning_time = 0.0
        self._warning_interval = 5.0  # 警告间隔（秒）
        
        # 统计信息
        self._stats = {
            'total_written': 0,
            'total_read': 0,
            'write_operations': 0,
            'read_operations': 0,
            'overflow_count': 0,
            'underflow_count': 0,
            'max_usage': 0,
            'last_activity': None
        }
        
        self._logger.info(f"缓冲区管理器初始化完成: {self._name} (大小: {size} 字节)")
    
    def write(self, data: bytes, block: bool = True, timeout: Optional[float] = None) -> int:
        """
        写入数据到缓冲区
        
        Args:
            data: 要写入的字节数据
            block: 是否阻塞等待空间
            timeout: 超时时间（秒），None表示无限等待
            
        Returns:
            实际写入的字节数
            
        Raises:
            BufferOverflowError: 缓冲区溢出（非阻塞模式）
        """
        if not data:
            return 0
        
        data_len = len(data)
        
        with self._lock:
            # 检查是否有足够空间
            available_space = self._size - self._data_size
            
            if data_len > available_space:
                if not block:
                    self._stats['overflow_count'] += 1
                    raise BufferOverflowError(self._size, data_len)
                
                # 阻塞等待空间
                if not self._not_full.wait_for(
                    lambda: self._size - self._data_size >= data_len, 
                    timeout=timeout
                ):
                    self._stats['overflow_count'] += 1
                    raise BufferOverflowError(self._size, data_len)
            
            # 写入数据
            bytes_written = self._write_data_internal(data)
            
            # 更新统计
            self._stats['total_written'] += bytes_written
            self._stats['write_operations'] += 1
            self._stats['last_activity'] = time.time()
            
            # 更新状态
            self._update_state()
            
            # 通知等待读取的线程
            self._not_empty.notify_all()
            
            self._logger.debug(f"写入数据: {bytes_written} 字节")
            return bytes_written
    
    def read(self, size: int, block: bool = True, timeout: Optional[float] = None) -> bytes:
        """
        从缓冲区读取数据
        
        Args:
            size: 要读取的字节数
            block: 是否阻塞等待数据
            timeout: 超时时间（秒），None表示无限等待
            
        Returns:
            读取到的字节数据
            
        Raises:
            BufferUnderflowError: 缓冲区下溢（非阻塞模式且数据不足）
        """
        if size <= 0:
            return b''
        
        with self._lock:
            # 检查是否有足够数据
            if size > self._data_size:
                if not block:
                    if self._data_size == 0:
                        return b''
                    # 返回所有可用数据
                    size = self._data_size
                else:
                    # 阻塞等待数据
                    if not self._not_empty.wait_for(
                        lambda: self._data_size >= size,
                        timeout=timeout
                    ):
                        # 超时，返回所有可用数据
                        if self._data_size == 0:
                            return b''
                        size = self._data_size
            
            # 读取数据
            data = self._read_data_internal(size)
            
            # 更新统计
            self._stats['total_read'] += len(data)
            self._stats['read_operations'] += 1
            self._stats['last_activity'] = time.time()
            
            # 更新状态
            self._update_state()
            
            # 通知等待写入的线程
            self._not_full.notify_all()
            
            self._logger.debug(f"读取数据: {len(data)} 字节")
            return data
    
    def peek(self, size: int) -> bytes:
        """
        查看数据但不移除（预览）
        
        Args:
            size: 要查看的字节数
            
        Returns:
            查看到的字节数据
        """
        if size <= 0:
            return b''
        
        with self._lock:
            actual_size = min(size, self._data_size)
            if actual_size == 0:
                return b''
            
            # 预览数据但不移动读取位置
            data = bytearray()
            pos = self._read_pos
            remaining = actual_size
            
            while remaining > 0:
                # 计算连续可读取的字节数
                if pos >= self._write_pos and self._data_size < self._size:
                    # 数据不连续，先读到缓冲区末尾
                    chunk_size = min(remaining, self._size - pos)
                else:
                    # 数据连续或缓冲区已满
                    chunk_size = min(remaining, self._size - pos)
                
                data.extend(self._buffer[pos:pos + chunk_size])
                pos = (pos + chunk_size) % self._size
                remaining -= chunk_size
            
            return bytes(data)
    
    def available_data(self) -> int:
        """
        获取可用数据大小
        
        Returns:
            可用数据字节数
        """
        with self._lock:
            return self._data_size
    
    def available_space(self) -> int:
        """
        获取可用空间大小
        
        Returns:
            可用空间字节数
        """
        with self._lock:
            return self._size - self._data_size
    
    def is_empty(self) -> bool:
        """检查缓冲区是否为空"""
        with self._lock:
            return self._data_size == 0
    
    def is_full(self) -> bool:
        """检查缓冲区是否已满"""
        with self._lock:
            return self._data_size == self._size
    
    def get_usage_rate(self) -> float:
        """
        获取缓冲区使用率
        
        Returns:
            使用率（0.0-1.0）
        """
        with self._lock:
            return self._data_size / self._size if self._size > 0 else 0.0
    
    def get_state(self) -> BufferState:
        """
        获取缓冲区状态

        Returns:
            当前缓冲区状态
        """
        with self._lock:
            return self._state

    def clear(self) -> None:
        """清空缓冲区"""
        with self._lock:
            self._read_pos = 0
            self._write_pos = 0
            self._data_size = 0
            self._state = BufferState.EMPTY

            # 通知等待的线程
            self._not_full.notify_all()

            self._logger.debug("缓冲区已清空")

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息字典
        """
        with self._lock:
            stats = self._stats.copy()
            stats.update({
                'current_size': self._data_size,
                'buffer_size': self._size,
                'usage_rate': self.get_usage_rate(),
                'state': self._state.value,
                'warning_threshold': self._warning_threshold
            })
            return stats

    def reset_stats(self) -> None:
        """重置统计信息"""
        with self._lock:
            self._stats = {
                'total_written': 0,
                'total_read': 0,
                'write_operations': 0,
                'read_operations': 0,
                'overflow_count': 0,
                'underflow_count': 0,
                'max_usage': 0,
                'last_activity': None
            }
            self._logger.info("统计信息已重置")

    def _write_data_internal(self, data: bytes) -> int:
        """
        内部写入数据方法

        Args:
            data: 要写入的数据

        Returns:
            实际写入的字节数
        """
        data_len = len(data)
        bytes_written = 0
        data_pos = 0

        while data_pos < data_len and self._data_size < self._size:
            # 计算连续可写入的字节数
            if self._write_pos >= self._read_pos or self._data_size == 0:
                # 写入位置在读取位置之后，或缓冲区为空
                chunk_size = min(
                    data_len - data_pos,
                    self._size - self._write_pos,
                    self._size - self._data_size
                )
            else:
                # 写入位置在读取位置之前（环绕情况）
                chunk_size = min(
                    data_len - data_pos,
                    self._read_pos - self._write_pos
                )

            if chunk_size <= 0:
                break

            # 写入数据块
            end_pos = self._write_pos + chunk_size
            self._buffer[self._write_pos:end_pos] = data[data_pos:data_pos + chunk_size]

            # 更新位置
            self._write_pos = end_pos % self._size
            self._data_size += chunk_size
            bytes_written += chunk_size
            data_pos += chunk_size

        return bytes_written

    def _read_data_internal(self, size: int) -> bytes:
        """
        内部读取数据方法

        Args:
            size: 要读取的字节数

        Returns:
            读取到的数据
        """
        actual_size = min(size, self._data_size)
        if actual_size == 0:
            return b''

        data = bytearray()
        remaining = actual_size

        while remaining > 0:
            # 计算连续可读取的字节数
            if self._data_size == self._size:
                # 缓冲区已满，从读取位置开始读到缓冲区末尾
                chunk_size = min(remaining, self._size - self._read_pos)
            elif self._read_pos < self._write_pos:
                # 读取位置在写入位置之前，数据连续
                chunk_size = min(remaining, self._write_pos - self._read_pos)
            else:
                # 读取位置在写入位置之后（环绕情况），读到缓冲区末尾
                chunk_size = min(remaining, self._size - self._read_pos)

            if chunk_size <= 0:
                break

            # 读取数据块
            end_pos = self._read_pos + chunk_size
            data.extend(self._buffer[self._read_pos:end_pos])

            # 更新位置
            self._read_pos = end_pos % self._size
            self._data_size -= chunk_size
            remaining -= chunk_size

        return bytes(data)

    def _update_state(self) -> None:
        """更新缓冲区状态"""
        usage_rate = self.get_usage_rate()

        # 更新最大使用率
        if usage_rate > self._stats['max_usage']:
            self._stats['max_usage'] = usage_rate

        # 确定新状态
        if self._data_size == 0:
            new_state = BufferState.EMPTY
        elif self._data_size == self._size:
            new_state = BufferState.FULL
        elif usage_rate >= self._warning_threshold:
            new_state = BufferState.WARNING
        else:
            new_state = BufferState.NORMAL

        # 状态变化处理
        if new_state != self._state:
            old_state = self._state
            self._state = new_state
            self._logger.debug(f"缓冲区状态变化: {old_state.value} -> {new_state.value}")

            # 警告状态处理
            if new_state == BufferState.WARNING:
                current_time = time.time()
                if current_time - self._last_warning_time > self._warning_interval:
                    self._logger.warning(f"缓冲区使用率过高: {usage_rate:.1%}")
                    self._last_warning_time = current_time

    def __len__(self) -> int:
        """返回当前数据大小"""
        return self.available_data()

    def __bool__(self) -> bool:
        """返回缓冲区是否非空"""
        return not self.is_empty()

    def __str__(self) -> str:
        """字符串表示"""
        return f"BufferManager(size={self._size}, data={self._data_size}, usage={self.get_usage_rate():.1%})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"BufferManager(name={self._name}, size={self._size}, "
                f"data={self._data_size}, state={self._state.value})")
