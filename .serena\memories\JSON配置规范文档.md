# JSON配置规范文档 - 数据采集系统

| 版本 | 最后更新时间 | 更新者 | 变更摘要 |
| :--- | :--- | :--- | :--- |
| 1.0  | 2025-08-05 09:08:59 +08:00 | BA | 初始创建，整合两种JSON配置规范 |
| 1.1  | 2025-08-05 10:09:29 +08:00 | BA | 增加串口定义，简化协议流程类型，优化帧检测 |
| 1.2  | 2025-08-05 10:59:44 +08:00 | BA | 增加IMU948传感器真实配置示例 |
| 1.3  | 2025-08-05 14:18:47 +08:00 | BA | 修正IMU948传感器数据偏移计算错误 |

---

## 1. 配置文件概述

数据采集系统使用两种类型的JSON配置文件：

1. **自由串口协议配置文件** - 用户运行时动态加载的协议定义文件
2. **系统运行参数配置文件** - 系统级运行参数和性能配置文件

## 2. 自由串口协议配置文件

### 2.1 配置文件用途
- 定义具体的串口协议规则和流程
- 用户在程序运行时通过交互方式选择加载
- 支持任意自定义的串口协议，无需硬编码

### 2.2 完整配置文件结构

```json
{
  "protocol_info": {
    "name": "自定义协议名称",
    "description": "协议描述",
    "version": "1.0"
  },
  "serial_config": {
    "port": "COM2",
    "baudrate": 9600,
    "databits": 8,
    "parity": "none",
    "stopbits": 1,
    "timeout": 1.0
  },
  "protocol_flow": {
    "steps": [
      {
        "name": "初始化配置",
        "type": "single_command",
        "commands": ["init_cmd1", "init_cmd2"]
      },
      {
        "name": "查询温度",
        "type": "single_command",
        "commands": ["query_cmd1"]
      },
      {
        "name": "启动连续监控",
        "type": "continuous_command",
        "commands": ["start_continuous"],
        "auto_start": false
      }
    ]
  },
  "commands": {
    "single": [
      {
        "id": "init_cmd1",
        "name": "初始化命令",
        "send": "01 03 00 00 00 01 84 0A",
        "response_validation": {
          "type": "exact",
          "pattern": "01 03 02 00 01 79 84",
          "timeout": 2.0,
          "retry_count": 3
        }
      },
      {
        "id": "query_cmd1",
        "name": "查询温度",
        "send": "01 03 00 01 00 01 D5 CA",
        "response_validation": {
          "type": "regex",
          "pattern": "01 03 02 [0-9A-F]{4} [0-9A-F]{4}",
          "timeout": 1.0,
          "retry_count": 2
        }
      }
    ],
    "continuous": [
      {
        "id": "start_continuous",
        "name": "启动连续监控",
        "send": "01 04 00 00 00 02 71 CB",
        "response_validation": {
          "type": "exact",
          "pattern": "01 04 04",
          "timeout": 1.0,
          "retry_count": 1
        }
      }
    ]
  },
  "continuous_data": {
    "frame_detection": {
      "header": "AA BB",
      "tail": "CC DD",
      "min_length": 8,
      "max_length": 256
    },
    "data_parsing": [
      {
        "name": "温度",
        "offset": 4,
        "length": 2,
        "data_type": "int16",
        "endian": "big",
        "scale_factor": 0.1,
        "unit": "°C"
      },
      {
        "name": "湿度",
        "offset": 6,
        "length": 2,
        "data_type": "int16",
        "endian": "big",
        "scale_factor": 0.01,
        "unit": "%RH"
      }
    ]
  }
}
```

### 2.3 配置字段详细说明

#### 2.3.1 protocol_info（协议信息）
| 字段 | 类型 | 必需 | 说明 |
| :--- | :--- | :--- | :--- |
| name | string | 是 | 协议名称，用于标识协议 |
| description | string | 否 | 协议描述信息 |
| version | string | 否 | 协议版本号 |

#### 2.3.2 serial_config（串口配置）
| 字段 | 类型 | 必需 | 说明 | 可选值 |
| :--- | :--- | :--- | :--- | :--- |
| port | string | 是 | 串口端口号 | "COM1", "COM2", "/dev/ttyUSB0"等 |
| baudrate | integer | 是 | 波特率 | 9600, 19200, 38400, 57600, 115200等 |
| databits | integer | 是 | 数据位 | 5, 6, 7, 8 |
| parity | string | 是 | 校验位 | "none", "even", "odd", "mark", "space" |
| stopbits | integer | 是 | 停止位 | 1, 1.5, 2 |
| timeout | float | 是 | 超时时间（秒） | >0 |

#### 2.3.3 protocol_flow（协议流程）
| 字段 | 类型 | 必需 | 说明 |
| :--- | :--- | :--- | :--- |
| steps | array | 是 | 协议执行步骤数组 |

**steps数组元素：**
| 字段 | 类型 | 必需 | 说明 | 可选值 |
| :--- | :--- | :--- | :--- | :--- |
| name | string | 是 | 步骤名称 | - |
| type | string | 是 | 步骤类型 | "single_command", "continuous_command" |
| commands | array | 是 | 该步骤要执行的命令ID列表 | - |
| auto_start | boolean | 否 | 是否自动开始（仅continuous_command类型有效） | true, false |

**步骤类型说明：**
- **single_command：** 主机发送单条指令，从机返回单条应答（一问一答模式）
- **continuous_command：** 主机发送指令后，从机连续返回报文
  - `auto_start: false`（默认）：需要发送指令触发连续返回
  - `auto_start: true`：串口连接后自动开始连续返回，无需发送指令

#### 2.3.4 commands（指令定义）
包含两种类型的指令：single（单条指令）、continuous（连续指令）

**指令对象结构：**
| 字段 | 类型 | 必需 | 说明 |
| :--- | :--- | :--- | :--- |
| id | string | 是 | 指令唯一标识符 |
| name | string | 是 | 指令名称 |
| send | string | 是 | 发送的十六进制指令（空格分隔） |
| response_validation | object | 是 | 应答验证配置 |

**response_validation对象：**
| 字段 | 类型 | 必需 | 说明 | 可选值 |
| :--- | :--- | :--- | :--- | :--- |
| type | string | 是 | 验证类型 | "exact", "regex" |
| pattern | string | 是 | 验证模式（精确匹配或正则表达式） | - |
| timeout | float | 是 | 超时时间（秒） | >0 |
| retry_count | integer | 是 | 重试次数 | ≥0 |

#### 2.3.5 continuous_data（连续数据配置）

**frame_detection（帧检测配置）：**
| 字段 | 类型 | 必需 | 说明 |
| :--- | :--- | :--- | :--- |
| header | string | 是 | 帧头（十六进制，空格分隔） |
| tail | string | 是 | 帧尾（十六进制，空格分隔） |
| min_length | integer | 是 | 最小帧长度 |
| max_length | integer | 是 | 最大帧长度 |

**帧检测逻辑：**
- **帧头匹配：** 数据必须以指定的帧头开始
- **帧尾匹配：** 数据必须以指定的帧尾结束
- **长度校验：** 帧长度必须在min_length和max_length之间
- **三者同时满足：** 帧头、帧尾、长度三个条件都满足才认为是有效帧

**data_parsing（数据解析配置）：**
| 字段 | 类型 | 必需 | 说明 | 可选值 |
| :--- | :--- | :--- | :--- | :--- |
| name | string | 是 | 数据字段名称 | - |
| offset | integer | 是 | 数据在帧中的偏移 | ≥0 |
| length | integer | 是 | 数据字段长度（字节） | >0 |
| data_type | string | 是 | 数据类型 | "int8", "int16", "int32", "float32", "float64" |
| endian | string | 是 | 字节序 | "big", "little" |
| scale_factor | float | 是 | 缩放系数 | - |
| unit | string | 否 | 数据单位 | - |

## 3. 系统运行参数配置文件

### 3.1 配置文件用途
- 定义系统级运行参数和性能配置
- 控制错误处理、队列管理、性能优化等系统行为
- 通常在程序启动时加载，运行期间不变

### 3.2 完整配置文件结构

```json
{
  "error_handling": {
    "continuous_mode": {
      "max_consecutive_errors": 5,
      "pause_on_max_errors": true,
      "recovery_strategy": "manual",
      "error_types": {
        "frame_error": {"weight": 1},
        "parse_error": {"weight": 1},
        "comm_error": {"weight": 2}
      }
    }
  },
  "queue_config": {
    "response_queue_size": 100,
    "queue_warning_threshold": 0.8,
    "batch_processing_size": 10
  },
  "performance": {
    "buffer_size": 4096,
    "processing_timeout": 0.1,
    "max_processing_threads": 2
  },
  "logging": {
    "level": "INFO",
    "file_max_size": "10MB",
    "file_backup_count": 30,
    "format": "[%(asctime)s] [%(levelname)s] [%(name)s] - %(message)s"
  },
  "monitoring": {
    "enable_performance_monitoring": true,
    "monitoring_interval": 1.0,
    "alert_thresholds": {
      "cpu_usage": 80,
      "memory_usage": 80,
      "queue_usage": 90
    }
  }
}
```

### 3.3 系统配置字段详细说明

#### 3.3.1 error_handling（错误处理配置）

**continuous_mode（连续模式错误处理）：**
| 字段 | 类型 | 必需 | 说明 | 默认值 |
| :--- | :--- | :--- | :--- | :--- |
| max_consecutive_errors | integer | 是 | 最大连续错误次数 | 5 |
| pause_on_max_errors | boolean | 是 | 达到最大错误时是否暂停 | true |
| recovery_strategy | string | 是 | 恢复策略 | "manual", "auto_retry" |
| error_types | object | 否 | 错误类型权重配置 | - |

**error_types对象：**
| 字段 | 类型 | 说明 | 权重建议 |
| :--- | :--- | :--- | :--- |
| frame_error | object | 帧检测错误 | weight: 1 |
| parse_error | object | 数据解析错误 | weight: 1 |
| comm_error | object | 通信错误 | weight: 2 |

#### 3.3.2 queue_config（队列配置）
| 字段 | 类型 | 必需 | 说明 | 默认值 |
| :--- | :--- | :--- | :--- | :--- |
| response_queue_size | integer | 是 | 应答队列大小 | 100 |
| queue_warning_threshold | float | 是 | 队列警告阈值（0-1） | 0.8 |
| batch_processing_size | integer | 是 | 批处理大小 | 10 |

#### 3.3.3 performance（性能配置）
| 字段 | 类型 | 必需 | 说明 | 默认值 |
| :--- | :--- | :--- | :--- | :--- |
| buffer_size | integer | 是 | 缓冲区大小（字节） | 4096 |
| processing_timeout | float | 是 | 处理超时时间（秒） | 0.1 |
| max_processing_threads | integer | 是 | 最大处理线程数 | 2 |

#### 3.3.4 logging（日志配置）
| 字段 | 类型 | 必需 | 说明 | 可选值 |
| :--- | :--- | :--- | :--- | :--- |
| level | string | 是 | 日志级别 | "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL" |
| file_max_size | string | 是 | 日志文件最大大小 | "10MB", "50MB"等 |
| file_backup_count | integer | 是 | 备份文件数量 | >0 |
| format | string | 是 | 日志格式 | - |

#### 3.3.5 monitoring（监控配置）
| 字段 | 类型 | 必需 | 说明 | 默认值 |
| :--- | :--- | :--- | :--- | :--- |
| enable_performance_monitoring | boolean | 是 | 是否启用性能监控 | true |
| monitoring_interval | float | 是 | 监控间隔（秒） | 1.0 |
| alert_thresholds | object | 是 | 告警阈值配置 | - |

**alert_thresholds对象：**
| 字段 | 类型 | 说明 | 单位 |
| :--- | :--- | :--- | :--- |
| cpu_usage | integer | CPU使用率告警阈值 | 百分比 |
| memory_usage | integer | 内存使用率告警阈值 | 百分比 |
| queue_usage | integer | 队列使用率告警阈值 | 百分比 |

## 4. 配置文件使用说明

### 4.1 自由串口协议配置文件
- **存放位置：** `config/protocols/` 目录下
- **命名规范：** `{协议名称}.json`
- **加载方式：** 程序启动时用户交互选择
- **验证要求：** 启动时进行完整性和格式验证

### 4.2 系统运行参数配置文件
- **存放位置：** `config/` 目录下
- **文件名：** `system_config.json`
- **加载方式：** 程序启动时自动加载
- **默认处理：** 缺失字段使用默认值

### 4.3 配置文件验证规则

#### 4.3.1 必需字段验证
- 所有标记为"必需"的字段都必须存在
- 字段类型必须与规范一致
- 数值字段必须在合理范围内

#### 4.3.2 逻辑一致性验证
- protocol_flow中引用的command ID必须在commands中存在
- 数据解析的offset和length不能超出帧长度范围
- 串口参数必须是硬件支持的值
- continuous_command类型的步骤必须引用continuous类型的指令

#### 4.3.3 格式验证
- 十六进制字符串必须是有效格式（如："01 03 00 00"）
- 正则表达式必须是有效的Python正则表达式
- 串口端口号必须是有效格式
- 文件大小格式必须正确（如："10MB"）

## 5. 协议流程类型详细说明

### 5.1 single_command（单条指令模式）
**工作流程：**
```
主机发送指令 → 从机返回应答 → 验证应答 → 完成
```

**适用场景：**
- 设备初始化配置
- 参数查询
- 状态检查
- 单次数据获取

**配置示例：**
```json
{
  "name": "查询设备状态",
  "type": "single_command",
  "commands": ["status_query"]
}
```

### 5.2 continuous_command（连续指令模式）
**工作流程：**

**触发模式（auto_start: false）：**
```
主机发送指令 → 从机确认 → 从机连续发送数据 → 持续接收解析
```

**自动模式（auto_start: true）：**
```
串口连接成功 → 从机自动连续发送数据 → 持续接收解析
```

**适用场景：**
- 实时数据监控
- 传感器数据采集
- 状态持续上报
- 自动数据推送

**配置示例：**
```json
{
  "name": "启动温度监控",
  "type": "continuous_command",
  "commands": ["start_temp_monitor"],
  "auto_start": false
}
```

```json
{
  "name": "自动数据上报",
  "type": "continuous_command", 
  "commands": [],
  "auto_start": true
}
```

## 6. 帧检测简化说明

### 6.1 简化后的帧检测逻辑
**检测条件：**
1. **帧头匹配：** 数据流中找到指定的帧头序列
2. **帧尾匹配：** 在合理位置找到指定的帧尾序列
3. **长度校验：** 完整帧长度在指定范围内

**检测流程：**
```
接收数据流 → 搜索帧头 → 计算帧长度 → 验证帧尾 → 长度校验 → 提取完整帧
```

### 6.2 帧检测配置简化
**移除的复杂配置：**
- length_field（长度字段配置）
- includes_header（长度包含帧头标志）

**保留的核心配置：**
- header（帧头）
- tail（帧尾）
- min_length（最小长度）
- max_length（最大长度）

### 6.3 帧检测优势
- **配置简单：** 只需要配置帧头、帧尾和长度范围
- **逻辑清晰：** 三个条件同时满足即为有效帧
- **适应性强：** 适用于大多数自由串口协议
- **性能优化：** 简化的逻辑提高检测效率

## 7. 配置示例和模板

### 7.1 温湿度传感器协议示例（单条指令模式）
```json
{
  "protocol_info": {
    "name": "温湿度传感器协议",
    "description": "标准温湿度传感器查询协议",
    "version": "1.0"
  },
  "serial_config": {
    "port": "COM3",
    "baudrate": 9600,
    "databits": 8,
    "parity": "none",
    "stopbits": 1,
    "timeout": 1.0
  },
  "protocol_flow": {
    "steps": [
      {
        "name": "初始化传感器",
        "type": "single_command",
        "commands": ["init_sensor"]
      },
      {
        "name": "查询温湿度",
        "type": "single_command", 
        "commands": ["query_temp_humidity"]
      }
    ]
  },
  "commands": {
    "single": [
      {
        "id": "init_sensor",
        "name": "初始化传感器",
        "send": "01 06 00 00 00 01 48 0A",
        "response_validation": {
          "type": "exact",
          "pattern": "01 06 00 00 00 01 48 0A",
          "timeout": 2.0,
          "retry_count": 3
        }
      },
      {
        "id": "query_temp_humidity",
        "name": "查询温湿度",
        "send": "01 03 00 00 00 02 C4 0B",
        "response_validation": {
          "type": "regex",
          "pattern": "01 03 04 [0-9A-F]{8} [0-9A-F]{4}",
          "timeout": 1.0,
          "retry_count": 2
        }
      }
    ],
    "continuous": []
  }
}
```

### 7.2 实时监控协议示例（连续指令模式）
```json
{
  "protocol_info": {
    "name": "实时监控协议",
    "description": "设备实时数据监控协议",
    "version": "1.0"
  },
  "serial_config": {
    "port": "COM2",
    "baudrate": 115200,
    "databits": 8,
    "parity": "none",
    "stopbits": 1,
    "timeout": 0.5
  },
  "protocol_flow": {
    "steps": [
      {
        "name": "启动实时监控",
        "type": "continuous_command",
        "commands": ["start_monitor"],
        "auto_start": false
      }
    ]
  },
  "commands": {
    "single": [],
    "continuous": [
      {
        "id": "start_monitor",
        "name": "启动实时监控",
        "send": "AA 55 01 00 BB",
        "response_validation": {
          "type": "exact",
          "pattern": "AA 55 81 00 BB",
          "timeout": 1.0,
          "retry_count": 1
        }
      }
    ]
  },
  "continuous_data": {
    "frame_detection": {
      "header": "AA 55",
      "tail": "BB CC",
      "min_length": 10,
      "max_length": 50
    },
    "data_parsing": [
      {
        "name": "设备ID",
        "offset": 2,
        "length": 1,
        "data_type": "int8",
        "endian": "big",
        "scale_factor": 1.0,
        "unit": ""
      },
      {
        "name": "温度",
        "offset": 3,
        "length": 2,
        "data_type": "int16",
        "endian": "big",
        "scale_factor": 0.1,
        "unit": "°C"
      },
      {
        "name": "湿度",
        "offset": 5,
        "length": 2,
        "data_type": "int16",
        "endian": "big",
        "scale_factor": 0.01,
        "unit": "%RH"
      }
    ]
  }
}
```

### 7.3 自动上报协议示例（自动连续模式）
```json
{
  "protocol_info": {
    "name": "自动上报协议",
    "description": "设备自动数据上报协议",
    "version": "1.0"
  },
  "serial_config": {
    "port": "COM1",
    "baudrate": 38400,
    "databits": 8,
    "parity": "none",
    "stopbits": 1,
    "timeout": 0.5
  },
  "protocol_flow": {
    "steps": [
      {
        "name": "自动数据上报",
        "type": "continuous_command",
        "commands": [],
        "auto_start": true
      }
    ]
  },
  "commands": {
    "single": [],
    "continuous": []
  },
  "continuous_data": {
    "frame_detection": {
      "header": "FF FE",
      "tail": "FE FF",
      "min_length": 8,
      "max_length": 32
    },
    "data_parsing": [
      {
        "name": "时间戳",
        "offset": 2,
        "length": 4,
        "data_type": "int32",
        "endian": "big",
        "scale_factor": 1.0,
        "unit": "s"
      },
      {
        "name": "数据值",
        "offset": 6,
        "length": 4,
        "data_type": "float32",
        "endian": "big",
        "scale_factor": 1.0,
        "unit": "V"
      }
    ]
  }
}
```

### 7.4 IMU948传感器协议示例（真实设备配置）
```json
{
  "protocol_info": {
    "name": "IMU948传感器协议",
    "description": "IMU948九轴传感器数据采集协议",
    "version": "1.0"
  },
  "serial_config": {
    "port": "COM4",
    "baudrate": 115200,
    "databits": 8,
    "parity": "none",
    "stopbits": 1,
    "timeout": 1.0
  },
  "protocol_flow": {
    "steps": [
      {
        "name": "关闭传感器主动上报",
        "type": "single_command",
        "commands": ["disable_auto_report"]
      },
      {
        "name": "设置传感器参数",
        "type": "single_command",
        "commands": ["set_sensor_params"]
      },
      {
        "name": "开启传感器主动上报",
        "type": "continuous_command",
        "commands": ["enable_auto_report"],
        "auto_start": false
      }
    ]
  },
  "commands": {
    "single": [
      {
        "id": "disable_auto_report",
        "name": "关闭传感器主动上报",
        "send": "49 00 01 18 19 4D",
        "response_validation": {
          "type": "exact",
          "pattern": "49 00 01 18 19 4D",
          "timeout": 2.0,
          "retry_count": 3
        }
      },
      {
        "id": "set_sensor_params",
        "name": "设置传感器参数",
        "send": "49 00 0B 12 05 FF 00 04 1E 01 03 05 C0 00 0C 4D",
        "response_validation": {
          "type": "exact",
          "pattern": "49 00 01 12 13 4D",
          "timeout": 2.0,
          "retry_count": 3
        }
      }
    ],
    "continuous": [
      {
        "id": "enable_auto_report",
        "name": "开启传感器主动上报",
        "send": "49 00 01 19 1A 4D",
        "response_validation": {
          "type": "exact",
          "pattern": "49 00 01 19 1A 4D",
          "timeout": 2.0,
          "retry_count": 3
        }
      }
    ]
  },
  "continuous_data": {
    "frame_detection": {
      "header": "49",
      "tail": "4D",
      "min_length": 24,
      "max_length": 24
    },
    "data_parsing": [
      {
        "name": "Roll_滚转角",
        "offset": 10,
        "length": 2,
        "data_type": "int16",
        "endian": "little",
        "scale_factor": 0.0054931640625,
        "unit": "°"
      },
      {
        "name": "Pitch_俯仰角",
        "offset": 12,
        "length": 2,
        "data_type": "int16",
        "endian": "little",
        "scale_factor": 0.0054931640625,
        "unit": "°"
      },
      {
        "name": "Yaw_偏航角",
        "offset": 14,
        "length": 2,
        "data_type": "int16",
        "endian": "little",
        "scale_factor": 0.0054931640625,
        "unit": "°"
      },
      {
        "name": "Position_X",
        "offset": 16,
        "length": 2,
        "data_type": "int16",
        "endian": "little",
        "scale_factor": 0.001,
        "unit": "m"
      },
      {
        "name": "Position_Y",
        "offset": 18,
        "length": 2,
        "data_type": "int16",
        "endian": "little",
        "scale_factor": 0.001,
        "unit": "m"
      },
      {
        "name": "Position_Z",
        "offset": 20,
        "length": 2,
        "data_type": "int16",
        "endian": "little",
        "scale_factor": 0.001,
        "unit": "m"
      }
    ]
  }
}
```

**IMU948协议说明：**

**协议流程：**
1. **关闭主动上报：** 确保传感器处于可配置状态
2. **设置参数：** 配置传感器的工作参数和输出格式
3. **开启主动上报：** 启动传感器连续数据输出

**数据帧格式：**
- **示例报文：** `49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D`
- **帧头：** `49`（偏移0）
- **帧尾：** `4D`（偏移23）
- **帧长度：** 24字节

**数据偏移计算（修正版）：**

**报文字节位置分析：**
```
位置: 0  1  2  3  4  5  6  7  8  9  10 11 12 13 14 15 16 17 18 19 20 21 22 23
数据: 49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D
```

**欧拉角数据（偏移10-15，6字节）：**
- **Roll（滚转角）：** 偏移10，`C6 F7` → 0xF7C6（小端）= -2106 → -2106 × 0.005493... ≈ -11.57°
- **Pitch（俯仰角）：** 偏移12，`08 FF` → 0xFF08（小端）= -248 → -248 × 0.005493... ≈ -1.36°
- **Yaw（偏航角）：** 偏移14，`FE B8` → 0xB8FE（小端）= -18178 → -18178 × 0.005493... ≈ -99.87°

**位置数据（偏移16-21，6字节）：**
- **Position_X：** 偏移16，`00 00` → 0x0000（小端）= 0 → 0 × 0.001 = 0.000m
- **Position_Y：** 偏移18，`00 00` → 0x0000（小端）= 0 → 0 × 0.001 = 0.000m
- **Position_Z：** 偏移20，`00 00` → 0x0000（小端）= 0 → 0 × 0.001 = 0.000m

**重要说明：**
- **数据类型：** int16表示有符号16位整数（S16），范围-32768到+32767
- **字节序处理：** 小端模式下，低字节在前，高字节在后
- **符号位处理：** 对于有符号整数，最高位为符号位，需要正确处理负数
- **缩放系数：** 欧拉角使用0.0054931640625（360/65536），位置数据使用0.001
- **偏移计算：** 从帧头开始计算，C6位于第11个字节（偏移10）

## 8. 配置文件最佳实践

### 8.1 协议配置最佳实践
1. **命名规范：** 使用描述性的协议名称和指令名称
2. **串口配置：** 确保串口端口号和参数与硬件匹配
3. **超时设置：** 根据设备响应特性合理设置超时时间
4. **重试策略：** 根据通信可靠性要求设置重试次数
5. **数据解析：** 确保偏移量和长度的准确性
6. **帧检测：** 选择合适的帧头、帧尾和长度范围

### 8.2 协议流程设计最佳实践
1. **步骤顺序：** 按照设备要求的顺序安排协议步骤
2. **类型选择：** 根据通信模式选择合适的步骤类型
3. **自动启动：** 仅在确认设备支持时使用auto_start
4. **错误处理：** 为关键步骤设置合适的重试机制

### 8.3 系统配置最佳实践
1. **性能调优：** 根据硬件性能调整缓冲区和线程数
2. **错误处理：** 根据应用场景设置合适的错误阈值
3. **监控配置：** 启用性能监控便于问题诊断
4. **日志管理：** 合理设置日志级别和轮转策略

## 9. 故障排除

### 9.1 常见配置错误
1. **JSON格式错误：** 检查括号、引号、逗号是否正确
2. **字段类型错误：** 确保数值字段不使用字符串
3. **引用错误：** 检查command ID引用是否存在
4. **串口配置错误：** 确认串口端口号和参数正确
5. **帧检测配置错误：** 确认帧头、帧尾格式正确
6. **偏移计算错误：** 仔细计算数据在帧中的实际位置
7. **范围错误：** 确保数值在合理范围内

### 9.2 协议流程故障排除
1. **步骤类型错误：** 确认使用正确的步骤类型
2. **指令引用错误：** 确认引用的指令ID存在于对应类型中
3. **auto_start配置错误：** 确认设备是否支持自动启动
4. **超时设置不当：** 根据设备响应时间调整超时值

### 9.3 配置验证工具
系统提供配置验证功能，启动时会自动检查：
- JSON格式正确性
- 必需字段完整性
- 字段类型一致性
- 逻辑关系正确性
- 串口配置有效性
- 帧检测配置合理性
- 数据偏移和长度的有效性

---

**文档维护说明：** 本文档随系统功能更新而更新，请确保使用最新版本的配置规范。