#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 serial_config_manager.py 模块
验证自由串口协议配置管理器的功能

作者: LD (Lead Developer)
创建时间: 2025-08-06
版本: 1.0
"""

import os
import sys
import json
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.serial_config_manager import (
    SerialConfigManager, get_serial_config_manager,
    ProtocolInfo, SerialConfig, ResponseValidation, Command,
    ProtocolStep, ProtocolFlow, FrameDetection, DataField,
    ContinuousData, SerialProtocolConfig
)
from utils.exceptions import ConfigFileNotFoundError, ConfigParsingError


def test_singleton_pattern():
    """测试单例模式"""
    print("=" * 60)
    print("测试单例模式")
    print("=" * 60)
    
    try:
        # 获取多个实例
        manager1 = SerialConfigManager()
        manager2 = SerialConfigManager()
        manager3 = get_serial_config_manager()
        
        # 验证是否为同一实例
        if manager1 is manager2 is manager3:
            print("✅ 单例模式工作正常")
            return True
        else:
            print("❌ 单例模式失败")
            return False
            
    except Exception as e:
        print(f"❌ 单例模式测试失败: {e}")
        return False


def test_load_imu948_config():
    """测试加载IMU948配置文件"""
    print("\n" + "=" * 60)
    print("测试加载IMU948配置文件")
    print("=" * 60)
    
    try:
        # 使用项目中的IMU948示例配置文件
        config_file = project_root / "config" / "protocols" / "imu948_example.json"
        
        if not config_file.exists():
            print(f"❌ IMU948配置文件不存在: {config_file}")
            return False
        
        manager = get_serial_config_manager()
        config = manager.load_config(str(config_file))
        
        print("✅ IMU948配置文件加载成功")
        
        # 验证协议信息
        protocol_info = config.protocol_info
        print(f"   - 协议名称: {protocol_info.name}")
        print(f"   - 协议描述: {protocol_info.description}")
        print(f"   - 协议版本: {protocol_info.version}")
        
        if protocol_info.name == "IMU948传感器协议":
            print("✅ 协议信息解析正确")
        else:
            print("❌ 协议信息解析不正确")
            return False
        
        # 验证串口配置
        serial_config = config.serial_config
        print(f"   - 串口端口: {serial_config.port}")
        print(f"   - 波特率: {serial_config.baudrate}")
        print(f"   - 数据位: {serial_config.databits}")
        print(f"   - 校验位: {serial_config.parity}")
        print(f"   - 停止位: {serial_config.stopbits}")
        print(f"   - 超时时间: {serial_config.timeout}")
        
        if serial_config.baudrate == 115200:
            print("✅ 串口配置解析正确")
        else:
            print("❌ 串口配置解析不正确")
            return False
        
        # 验证协议流程
        protocol_flow = config.protocol_flow
        print(f"   - 协议步骤数量: {len(protocol_flow.steps)}")
        
        if len(protocol_flow.steps) == 3:
            print("✅ 协议流程解析正确")
        else:
            print("❌ 协议流程解析不正确")
            return False
        
        # 验证指令
        print(f"   - 单条指令数量: {len(config.single_commands)}")
        print(f"   - 连续指令数量: {len(config.continuous_commands)}")
        
        if len(config.single_commands) == 2 and len(config.continuous_commands) == 1:
            print("✅ 指令解析正确")
        else:
            print("❌ 指令解析不正确")
            return False
        
        # 验证连续数据配置
        if config.continuous_data:
            frame_detection = config.continuous_data.frame_detection
            data_parsing = config.continuous_data.data_parsing
            
            print(f"   - 帧头: {frame_detection.header}")
            print(f"   - 帧尾: {frame_detection.tail}")
            print(f"   - 数据字段数量: {len(data_parsing)}")
            
            if frame_detection.header == "49" and frame_detection.tail == "4D":
                print("✅ 帧检测配置解析正确")
            else:
                print("❌ 帧检测配置解析不正确")
                return False
            
            if len(data_parsing) == 6:
                print("✅ 数据解析配置解析正确")
            else:
                print("❌ 数据解析配置解析不正确")
                return False
        else:
            print("❌ 连续数据配置未解析")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 加载IMU948配置文件失败: {e}")
        return False


def test_config_getters():
    """测试配置获取方法"""
    print("\n" + "=" * 60)
    print("测试配置获取方法")
    print("=" * 60)
    
    try:
        manager = get_serial_config_manager()
        
        # 先加载配置
        config_file = project_root / "config" / "protocols" / "imu948_example.json"
        manager.load_config(str(config_file))
        
        # 测试各种获取方法
        protocol_info = manager.get_protocol_info()
        serial_config = manager.get_serial_config()
        protocol_flow = manager.get_protocol_flow()
        single_commands = manager.get_single_commands()
        continuous_commands = manager.get_continuous_commands()
        continuous_data = manager.get_continuous_data_config()
        frame_detection = manager.get_frame_detection_config()
        data_parsing = manager.get_data_parsing_config()
        
        print("✅ 所有配置获取方法正常工作")
        print(f"   - 协议信息: {protocol_info.name if protocol_info else None}")
        print(f"   - 串口端口: {serial_config.port if serial_config else None}")
        print(f"   - 协议步骤数: {len(protocol_flow.steps) if protocol_flow else 0}")
        print(f"   - 单条指令数: {len(single_commands)}")
        print(f"   - 连续指令数: {len(continuous_commands)}")
        print(f"   - 数据字段数: {len(data_parsing)}")
        
        # 测试根据ID获取指令
        cmd = manager.get_command_by_id("disable_auto_report")
        if cmd and cmd.name == "关闭传感器主动上报":
            print("✅ 根据ID获取指令功能正常")
        else:
            print("❌ 根据ID获取指令功能异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置获取方法测试失败: {e}")
        return False


def test_validate_protocol_flow_references():
    """测试协议流程引用验证"""
    print("\n" + "=" * 60)
    print("测试协议流程引用验证")
    print("=" * 60)
    
    try:
        manager = get_serial_config_manager()
        
        # 先加载配置
        config_file = project_root / "config" / "protocols" / "imu948_example.json"
        manager.load_config(str(config_file))
        
        # 验证协议流程引用
        errors = manager.validate_protocol_flow_references()
        
        if len(errors) == 0:
            print("✅ 协议流程引用验证通过")
            return True
        else:
            print("❌ 协议流程引用验证失败:")
            for error in errors:
                print(f"   - {error}")
            return False
        
    except Exception as e:
        print(f"❌ 协议流程引用验证测试失败: {e}")
        return False


def test_config_file_not_found():
    """测试配置文件不存在的情况"""
    print("\n" + "=" * 60)
    print("测试配置文件不存在的情况")
    print("=" * 60)
    
    try:
        manager = get_serial_config_manager()
        
        # 尝试加载不存在的文件
        try:
            manager.load_config("non_existent_protocol.json")
            print("❌ 应该抛出ConfigFileNotFoundError异常")
            return False
        except ConfigFileNotFoundError:
            print("✅ 正确抛出ConfigFileNotFoundError异常")
            return True
        except Exception as e:
            print(f"❌ 抛出了错误的异常类型: {type(e).__name__}")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件不存在测试失败: {e}")
        return False


def test_invalid_json_config():
    """测试无效JSON配置文件"""
    print("\n" + "=" * 60)
    print("测试无效JSON配置文件")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # 创建无效的JSON文件
            invalid_config_file = Path(temp_dir) / "invalid_protocol.json"
            with open(invalid_config_file, 'w', encoding='utf-8') as f:
                f.write('{"invalid": json, "syntax"}')  # 无效的JSON语法
            
            manager = get_serial_config_manager()
            
            # 尝试加载无效的JSON文件
            try:
                manager.load_config(str(invalid_config_file))
                print("❌ 应该抛出ConfigParsingError异常")
                return False
            except ConfigParsingError:
                print("✅ 正确抛出ConfigParsingError异常")
                return True
            except Exception as e:
                print(f"❌ 抛出了错误的异常类型: {type(e).__name__}")
                return False
                
        except Exception as e:
            print(f"❌ 无效JSON配置测试失败: {e}")
            return False


def test_config_status_methods():
    """测试配置状态方法"""
    print("\n" + "=" * 60)
    print("测试配置状态方法")
    print("=" * 60)

    try:
        manager = get_serial_config_manager()

        # 重置配置状态以确保测试独立性
        manager._reset_for_testing()
        print("✅ 配置状态已重置，确保测试独立性")

        # 测试未加载配置时的状态
        if not manager.is_config_loaded():
            print("✅ 未加载配置时状态正确")
        else:
            print("❌ 未加载配置时状态不正确")
            return False
        
        # 加载配置
        config_file = project_root / "config" / "protocols" / "imu948_example.json"
        manager.load_config(str(config_file))
        
        # 测试已加载配置时的状态
        if manager.is_config_loaded():
            print("✅ 已加载配置时状态正确")
        else:
            print("❌ 已加载配置时状态不正确")
            return False
        
        # 测试获取配置文件路径
        config_path = manager.get_config_file_path()
        if config_path and config_path.name == "imu948_example.json":
            print("✅ 配置文件路径获取正确")
        else:
            print("❌ 配置文件路径获取不正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置状态方法测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试 serial_config_manager.py 模块")
    print("=" * 80)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("单例模式", test_singleton_pattern()))
    test_results.append(("加载IMU948配置", test_load_imu948_config()))
    test_results.append(("配置获取方法", test_config_getters()))
    test_results.append(("协议流程引用验证", test_validate_protocol_flow_references()))
    test_results.append(("配置文件不存在", test_config_file_not_found()))
    test_results.append(("无效JSON配置", test_invalid_json_config()))
    test_results.append(("配置状态方法", test_config_status_methods()))
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {len(test_results)} 个测试")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    
    if failed == 0:
        print("\n🎉 所有测试通过！")
        return True
    else:
        print(f"\n⚠️  有 {failed} 个测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
