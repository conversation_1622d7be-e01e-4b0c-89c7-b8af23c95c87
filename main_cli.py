#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据采集系统主程序入口
基于五层架构的动态配置数据采集系统
支持用户交互式选择自由串口协议配置文件

作者: LD (Lead Developer)
创建时间: 2025-08-05
更新时间: 2025-08-08
版本: 2.0
"""

import sys
import signal
import time
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from business_logic.protocol_flow_controller import ProtocolFlowController
from business_logic.logger_manager import LoggerManager, LogLevel, LogFormat
from utils.exceptions import (
    ConfigFileNotFoundError, ConfigParsingError, ConfigValidationError,
    SerialConnectionError, DataProcessingError
)


class DataStudioCLI:
    """数据采集系统命令行界面"""

    def __init__(self):
        """初始化CLI"""
        self.logger_manager = None
        self.logger = None
        self.flow_controller = None
        self.running = False

        # 数据统计
        self.data_count = 0
        self.start_time = None

        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, _):
        """信号处理器"""
        print(f"\n\n🛑 接收到停止信号 ({signum})，正在优雅停止...")
        self.running = False
        if self.flow_controller:
            try:
                self.flow_controller.stop_continuous_mode()
                self.flow_controller.disconnect()
                print("✅ 设备已断开连接")
            except Exception as e:
                print(f"⚠️ 断开连接时出现异常: {e}")

        if self.logger_manager:
            try:
                self.logger_manager.shutdown()
                print("✅ 日志系统已关闭")
            except Exception as e:
                print(f"⚠️ 关闭日志系统时出现异常: {e}")

        print("👋 程序已退出")
        sys.exit(0)

    def print_header(self):
        """打印程序头部信息"""
        print("🚀 DataStudio - 自由串口数据采集系统")
        print("=" * 60)
        print("📋 功能特性:")
        print("  • 🔧 支持自定义JSON协议配置")
        print("  • 🔗 动态串口连接和管理")
        print("  • ⚙️  自动执行协议初始化流程")
        print("  • 📊 实时数据采集和解析")
        print("  • 📈 连续数据模式支持")
        print("  • 🛑 优雅停止和资源清理")
        print("  • 📝 完整的日志记录和错误处理")
        print("=" * 60)

    def initialize_logging(self) -> bool:
        """初始化日志系统"""
        try:
            log_config = {
                "log_level": LogLevel.INFO,
                "log_format": LogFormat.DETAILED,
                "log_dir": "logs",
                "enable_console": True,
                "enable_file": True,
                "enable_performance": True
            }

            self.logger_manager = LoggerManager(log_config)
            self.logger = self.logger_manager.get_logger("DataStudioCLI")

            print("✅ 日志系统初始化成功")
            self.logger.info("DataStudio CLI 启动")
            return True

        except Exception as e:
            print(f"❌ 日志系统初始化失败: {e}")
            return False

    def get_protocol_config_path(self) -> Optional[str]:
        """获取用户输入的协议配置文件路径"""
        print("\n📁 协议配置文件选择")
        print("-" * 30)

        # 显示示例配置文件
        protocols_dir = Path("config/protocols")
        if protocols_dir.exists():
            print("💡 可用的示例配置文件:")
            example_files = list(protocols_dir.glob("*.json"))
            if example_files:
                for i, file_path in enumerate(example_files, 1):
                    print(f"  {i}. {file_path}")
            else:
                print("  (未找到示例配置文件)")

        print("\n📝 请选择输入方式:")
        print("  1. 输入配置文件的完整路径")
        print("  2. 输入配置文件名(在config/protocols/目录下)")
        print("  3. 使用示例配置文件")
        print("  0. 退出程序")

        while True:
            try:
                choice = input("\n👉 请选择 (0-3): ").strip()

                if choice == "0":
                    print("👋 用户选择退出")
                    return None

                elif choice == "1":
                    # 完整路径输入
                    path_input = input("📂 请输入配置文件的完整路径: ").strip()
                    if not path_input:
                        print("❌ 路径不能为空，请重新输入")
                        continue

                    config_path = Path(path_input)
                    if not config_path.exists():
                        print(f"❌ 文件不存在: {config_path}")
                        continue

                    if not config_path.suffix.lower() == '.json':
                        print("❌ 文件必须是JSON格式(.json)")
                        continue

                    return str(config_path)

                elif choice == "2":
                    # 文件名输入
                    filename = input("📄 请输入配置文件名(如: my_protocol.json): ").strip()
                    if not filename:
                        print("❌ 文件名不能为空，请重新输入")
                        continue

                    if not filename.endswith('.json'):
                        filename += '.json'

                    config_path = protocols_dir / filename
                    if not config_path.exists():
                        print(f"❌ 文件不存在: {config_path}")
                        continue

                    return str(config_path)

                elif choice == "3":
                    # 使用示例配置文件
                    if not example_files:
                        print("❌ 没有可用的示例配置文件")
                        continue

                    if len(example_files) == 1:
                        return str(example_files[0])

                    print("\n📋 请选择示例配置文件:")
                    for i, file_path in enumerate(example_files, 1):
                        print(f"  {i}. {file_path.name}")

                    try:
                        file_choice = int(input("👉 请选择文件编号: ").strip())
                        if 1 <= file_choice <= len(example_files):
                            return str(example_files[file_choice - 1])
                        else:
                            print("❌ 无效的文件编号")
                            continue
                    except ValueError:
                        print("❌ 请输入有效的数字")
                        continue

                else:
                    print("❌ 无效的选择，请输入0-3之间的数字")
                    continue

            except KeyboardInterrupt:
                print("\n\n👋 用户取消操作")
                return None
            except Exception as e:
                print(f"❌ 输入处理异常: {e}")
                continue

    def load_protocol_config(self, config_path: str) -> bool:
        """加载协议配置文件"""
        try:
            print(f"\n🔧 正在加载协议配置: {config_path}")

            # 初始化协议流程控制器
            self.flow_controller = ProtocolFlowController(config_path)

            # 获取协议信息
            protocol_info = self.flow_controller.get_protocol_info()

            print("✅ 协议配置加载成功")
            print(f"📋 协议信息:")
            print(f"   名称: {protocol_info['name']}")
            print(f"   描述: {protocol_info['description']}")
            print(f"   版本: {protocol_info['version']}")
            print(f"   串口: {protocol_info['serial_config']['port']}")
            print(f"   波特率: {protocol_info['serial_config']['baudrate']}")
            print(f"   单次指令数: {protocol_info['single_commands']}")
            print(f"   连续指令数: {protocol_info['continuous_commands']}")

            self.logger.info(f"协议配置加载成功: {config_path}")
            return True

        except ConfigFileNotFoundError as e:
            print(f"❌ 配置文件未找到: {e}")
            self.logger.error(f"配置文件未找到: {e}")
            return False
        except ConfigParsingError as e:
            print(f"❌ 配置文件解析失败: {e}")
            self.logger.error(f"配置文件解析失败: {e}")
            return False
        except ConfigValidationError as e:
            print(f"❌ 配置文件验证失败: {e}")
            self.logger.error(f"配置文件验证失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 加载协议配置异常: {e}")
            self.logger.error(f"加载协议配置异常: {e}")
            return False

    def connect_device(self) -> bool:
        """连接设备"""
        try:
            print("\n🔌 正在连接设备...")

            if not self.flow_controller.connect():
                print("❌ 设备连接失败")
                return False

            print("✅ 设备连接成功")
            self.logger.info("设备连接成功")
            return True

        except SerialConnectionError as e:
            print(f"❌ 串口连接错误: {e}")
            self.logger.error(f"串口连接错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 设备连接异常: {e}")
            self.logger.error(f"设备连接异常: {e}")
            return False

    def execute_protocol_flow(self) -> bool:
        """执行协议流程"""
        try:
            print("\n⚙️ 正在执行协议初始化流程...")

            # 执行协议流程
            results = self.flow_controller.execute_protocol_flow()

            success_count = 0
            total_count = len(results)

            for result in results:
                if result.success:
                    success_count += 1
                    print(f"✅ 步骤 '{result.step_name}' 执行成功 (耗时: {result.execution_time:.3f}s)")
                    self.logger.info(f"步骤 '{result.step_name}' 执行成功")
                else:
                    print(f"❌ 步骤 '{result.step_name}' 执行失败: {result.error_message}")
                    self.logger.error(f"步骤 '{result.step_name}' 执行失败: {result.error_message}")

            if success_count == total_count:
                print(f"🎉 初始化流程完成，所有 {total_count} 个步骤执行成功")
                self.logger.info("协议初始化流程执行成功")
                return True
            else:
                print(f"⚠️ 初始化流程部分成功: {success_count}/{total_count}")
                self.logger.warning(f"初始化流程部分成功: {success_count}/{total_count}")
                return success_count > 0  # 部分成功也允许继续

        except DataProcessingError as e:
            print(f"❌ 数据处理错误: {e}")
            self.logger.error(f"数据处理错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 协议流程执行异常: {e}")
            self.logger.error(f"协议流程执行异常: {e}")
            return False

    def start_continuous_mode(self) -> bool:
        """启动连续数据模式"""
        try:
            print("\n📊 检查是否支持连续数据模式...")

            protocol_info = self.flow_controller.get_protocol_info()
            if protocol_info['continuous_commands'] == 0:
                print("ℹ️ 当前协议不支持连续数据模式")
                return True

            print("🚀 启动连续数据采集模式...")

            # 启动连续模式（直接传递回调函数）
            self.flow_controller.start_continuous_mode(self._data_callback)

            # 记录开始时间
            self.start_time = time.time()
            self.data_count = 0

            print("✅ 连续数据采集已启动")
            print("📊 实时数据显示:")
            print("   🧭 欧拉角: Roll(滚转), Pitch(俯仰), Yaw(偏航)")
            print("   📍 位置: X, Y, Z坐标")
            print("   � 数据率: 每秒帧数")
            print("   按 Ctrl+C 停止数据采集")
            print("-" * 60)
            print()  # 空行分隔

            self.running = True
            self.logger.info("连续数据模式启动成功")
            return True

        except Exception as e:
            print(f"❌ 启动连续模式异常: {e}")
            self.logger.error(f"启动连续模式异常: {e}")
            return False

    def _data_callback(self, data_result):
        """数据回调函数"""
        try:
            if not self.running:
                return

            self.data_count += 1

            # 解析欧拉角和位置信息
            euler_angles = {}
            position_info = {}

            for field in data_result.parsed_fields:
                # 使用 field_name 和 scaled_value 属性
                if "Roll" in field.field_name or "滚转角" in field.field_name:
                    euler_angles["Roll"] = f"{field.scaled_value:.2f}{field.unit}"
                elif "Pitch" in field.field_name or "俯仰角" in field.field_name:
                    euler_angles["Pitch"] = f"{field.scaled_value:.2f}{field.unit}"
                elif "Yaw" in field.field_name or "偏航角" in field.field_name:
                    euler_angles["Yaw"] = f"{field.scaled_value:.2f}{field.unit}"
                elif "X" in field.field_name and ("位置" in field.field_name or "Position" in field.field_name):
                    position_info["X"] = f"{field.scaled_value:.3f}{field.unit}"
                elif "Y" in field.field_name and ("位置" in field.field_name or "Position" in field.field_name):
                    position_info["Y"] = f"{field.scaled_value:.3f}{field.unit}"
                elif "Z" in field.field_name and ("位置" in field.field_name or "Position" in field.field_name):
                    position_info["Z"] = f"{field.scaled_value:.3f}{field.unit}"

            # 计算数据率
            if self.start_time:
                elapsed_time = time.time() - self.start_time
                data_rate = self.data_count / elapsed_time
            else:
                data_rate = 0.0

            # 输出数据（使用回车覆盖显示）
            print(f"\r📊 帧#{data_result.frame_number:06d} | ", end="")

            if euler_angles:
                print(f"🧭 欧拉角: ", end="")
                for name, value in euler_angles.items():
                    print(f"{name}={value} ", end="")
                print("| ", end="")

            if position_info:
                print(f"📍 位置: ", end="")
                for name, value in position_info.items():
                    print(f"{name}={value} ", end="")
                print("| ", end="")

            print(f"📈 数据率: {data_rate:.1f}Hz", end="", flush=True)

            # 每100帧输出一次详细信息
            if self.data_count % 100 == 0:
                print()  # 换行
                self.logger.info(f"已处理 {self.data_count} 帧数据，数据率: {data_rate:.1f}Hz")

                # 输出统计信息
                try:
                    stats = self.flow_controller.get_statistics()
                    self.logger.info(f"统计信息: 帧数={stats['frames_processed']}, 字段数={stats['fields_parsed']}")
                except Exception:
                    pass  # 忽略统计信息获取错误

        except Exception as e:
            if self.logger:
                self.logger.error(f"数据回调异常: {e}")

    def wait_for_user_stop(self):
        """等待用户停止"""
        try:
            while self.running:
                time.sleep(0.1)
        except KeyboardInterrupt:
            self.running = False

    def stop_data_collection(self):
        """停止数据采集"""
        try:
            print("\n")  # 换行
            print("🛑 正在停止数据采集...")

            # 停止连续模式
            if self.flow_controller:
                self.flow_controller.stop_continuous_mode()

            # 输出最终统计
            if self.start_time and self.data_count > 0:
                total_time = time.time() - self.start_time
                avg_rate = self.data_count / total_time

                print("� 数据采集统计:")
                print(f"   总帧数: {self.data_count}")
                print(f"   总时间: {total_time:.1f}秒")
                print(f"   平均数据率: {avg_rate:.1f}Hz")

                if self.logger:
                    self.logger.info(f"数据采集完成: 总帧数={self.data_count}, 平均数据率={avg_rate:.1f}Hz")

            # 输出系统统计
            if self.flow_controller:
                try:
                    stats = self.flow_controller.get_statistics()
                    print("🔧 系统统计:")
                    print(f"   流程执行: {stats['flows_executed']} (成功: {stats['flows_successful']})")
                    print(f"   指令执行: {stats['commands_executed']} (成功: {stats['commands_successful']})")
                    print(f"   帧处理: {stats['frames_processed']}")
                    print(f"   字段解析: {stats['fields_parsed']}")

                    if self.logger:
                        self.logger.info("系统统计信息已输出")
                except Exception:
                    pass  # 忽略统计信息获取错误

            print("✅ 数据采集已停止")

        except Exception as e:
            print(f"⚠️ 停止数据采集时出现异常: {e}")
            if self.logger:
                self.logger.error(f"停止数据采集异常: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            print("🧹 正在清理资源...")

            # 断开设备连接
            if self.flow_controller:
                self.flow_controller.disconnect()
                print("✅ 设备已断开连接")

            # 关闭日志管理器
            if self.logger_manager:
                if self.logger:
                    self.logger.info("✅ 资源清理完成")
                self.logger_manager.shutdown()
                print("✅ 日志系统已关闭")

        except Exception as e:
            print(f"⚠️ 清理资源时出现异常: {e}")

    def run(self):
        """运行主程序"""
        try:
            # 打印头部信息
            self.print_header()

            # 初始化日志系统
            if not self.initialize_logging():
                return 1

            # 获取协议配置文件路径
            config_path = self.get_protocol_config_path()
            if not config_path:
                return 0

            # 加载协议配置
            if not self.load_protocol_config(config_path):
                return 1

            # 连接设备
            if not self.connect_device():
                return 1

            # 执行协议流程
            if not self.execute_protocol_flow():
                return 1

            # 启动连续数据模式
            if not self.start_continuous_mode():
                return 1

            # 等待用户停止
            if self.running:
                self.wait_for_user_stop()

            # 停止数据采集
            self.stop_data_collection()

            return 0

        except KeyboardInterrupt:
            print("\n\n👋 用户中断程序")
            return 0
        except Exception as e:
            print(f"\n❌ 程序运行异常: {e}")
            if self.logger:
                self.logger.error(f"程序运行异常: {e}")
            return 1
        finally:
            self.cleanup()


def main():
    """主程序入口"""
    cli = DataStudioCLI()
    exit_code = cli.run()
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
