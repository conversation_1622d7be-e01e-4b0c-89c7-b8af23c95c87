#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口管理器测试
测试SerialManager的所有核心功能，包括连接、断开、读写、状态管理等

使用虚拟串口COM2和COM3进行测试

作者: LD (Lead Developer)
创建时间: 2025-08-07
版本: 1.0
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.serial_config_manager import SerialConfig
from utils.logging_config import LoggingConfig
from communication.serial_manager import SerialManager, ConnectionState
from utils.exceptions import SerialConnectionError, SerialWriteError, SerialReadError


def setup_logging():
    """设置日志系统"""
    logging_config = LoggingConfig()
    logging_config.setup_logging()
    print("日志系统初始化完成")


def create_test_config(port: str) -> SerialConfig:
    """创建测试用的串口配置"""
    return SerialConfig(
        port=port,
        baudrate=9600,
        databits=8,
        parity="none",
        stopbits=1,
        timeout=1.0
    )


def test_serial_manager_initialization():
    """测试串口管理器初始化"""
    print("\n=== 测试串口管理器初始化 ===")
    
    config = create_test_config("COM2")
    manager = SerialManager(config, "TestManager")
    
    # 验证初始状态
    assert manager.get_connection_state() == ConnectionState.DISCONNECTED
    assert not manager.is_connected()
    assert manager.get_config().port == "COM2"
    
    stats = manager.get_stats()
    assert stats['bytes_sent'] == 0
    assert stats['bytes_received'] == 0
    assert stats['connection_count'] == 0
    
    print("✅ 串口管理器初始化测试通过")


def test_serial_connection():
    """测试串口连接和断开"""
    print("\n=== 测试串口连接和断开 ===")
    
    config = create_test_config("COM2")
    manager = SerialManager(config, "ConnectionTest")
    
    try:
        # 测试连接
        print("正在测试连接...")
        success = manager.connect()
        
        if success:
            print("✅ 串口连接成功")
            assert manager.is_connected()
            assert manager.get_connection_state() == ConnectionState.CONNECTED
            
            stats = manager.get_stats()
            assert stats['connection_count'] == 1
            
            # 测试断开
            print("正在测试断开...")
            success = manager.disconnect()
            assert success
            assert not manager.is_connected()
            assert manager.get_connection_state() == ConnectionState.DISCONNECTED
            
            print("✅ 串口断开成功")
        else:
            print("⚠️  串口连接失败，可能是虚拟串口未创建")
            
    except SerialConnectionError as e:
        print(f"⚠️  串口连接异常（预期）: {e}")
    except Exception as e:
        print(f"❌ 意外异常: {e}")
        raise


def test_data_write_read():
    """测试数据读写"""
    print("\n=== 测试数据读写 ===")
    
    config = create_test_config("COM2")
    manager = SerialManager(config, "DataTest")
    
    try:
        if manager.connect():
            print("✅ 串口连接成功，开始测试数据读写")
            
            # 测试写入数据
            test_data = b"Hello Serial"
            bytes_written = manager.write_data(test_data)
            print(f"写入数据: {test_data} ({bytes_written} 字节)")
            
            assert bytes_written == len(test_data)
            
            # 检查统计信息
            stats = manager.get_stats()
            assert stats['bytes_sent'] == len(test_data)
            assert stats['last_activity'] is not None
            
            # 测试读取数据（可能没有数据返回）
            time.sleep(0.1)  # 等待一下
            received_data = manager.read_available_data()
            print(f"读取数据: {received_data} ({len(received_data)} 字节)")
            
            print("✅ 数据读写测试完成")
            
        else:
            print("⚠️  无法连接串口，跳过数据读写测试")
            
    except Exception as e:
        print(f"⚠️  数据读写测试异常: {e}")
    finally:
        manager.disconnect()


def test_state_callbacks():
    """测试状态回调"""
    print("\n=== 测试状态回调 ===")
    
    config = create_test_config("COM2")
    manager = SerialManager(config, "CallbackTest")
    
    # 状态变化记录
    state_changes = []
    
    def state_callback(state: ConnectionState):
        state_changes.append(state)
        print(f"状态回调触发: {state.value}")
    
    # 添加回调
    manager.add_state_callback("test_callback", state_callback)
    
    try:
        # 触发状态变化
        if manager.connect():
            time.sleep(0.1)  # 等待回调执行
            manager.disconnect()
            time.sleep(0.1)  # 等待回调执行
            
            # 验证回调被触发
            assert len(state_changes) >= 2  # 至少有连接和断开两个状态
            print(f"✅ 状态回调测试通过，记录到 {len(state_changes)} 个状态变化")
        else:
            print("⚠️  无法连接串口，跳过状态回调测试")
            
    except Exception as e:
        print(f"⚠️  状态回调测试异常: {e}")
    finally:
        manager.remove_state_callback("test_callback")


def test_context_manager():
    """测试上下文管理器"""
    print("\n=== 测试上下文管理器 ===")
    
    config = create_test_config("COM2")
    
    try:
        with SerialManager(config, "ContextTest") as manager:
            print("进入上下文管理器")
            if manager.is_connected():
                print("✅ 上下文管理器自动连接成功")
                
                # 在上下文中进行一些操作
                stats = manager.get_stats()
                print(f"连接次数: {stats['connection_count']}")
            else:
                print("⚠️  上下文管理器连接失败")
        
        print("✅ 上下文管理器测试完成（自动断开）")
        
    except Exception as e:
        print(f"⚠️  上下文管理器测试异常: {e}")


def test_reconnection():
    """测试重连功能"""
    print("\n=== 测试重连功能 ===")
    
    config = create_test_config("COM2")
    manager = SerialManager(config, "ReconnectTest")
    
    try:
        # 尝试重连
        print("测试重连功能...")
        success = manager.reconnect(max_attempts=2, delay=0.5)
        
        if success:
            print("✅ 重连成功")
            assert manager.is_connected()
            manager.disconnect()
        else:
            print("⚠️  重连失败（可能是虚拟串口问题）")
            
    except Exception as e:
        print(f"⚠️  重连测试异常: {e}")


def test_statistics():
    """测试统计功能"""
    print("\n=== 测试统计功能 ===")
    
    config = create_test_config("COM2")
    manager = SerialManager(config, "StatsTest")
    
    try:
        if manager.connect():
            # 进行一些操作
            manager.write_data(b"test data")
            manager.read_available_data()
            
            # 检查统计信息
            stats = manager.get_stats()
            print(f"统计信息: {stats}")
            
            assert stats['bytes_sent'] > 0
            assert stats['connection_count'] > 0
            
            # 重置统计
            manager.reset_stats()
            stats_after_reset = manager.get_stats()
            assert stats_after_reset['bytes_sent'] == 0
            assert stats_after_reset['bytes_received'] == 0
            
            print("✅ 统计功能测试通过")
        else:
            print("⚠️  无法连接串口，跳过统计测试")
            
    except Exception as e:
        print(f"⚠️  统计测试异常: {e}")
    finally:
        manager.disconnect()


def run_all_tests():
    """运行所有测试"""
    print("开始串口管理器测试")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    
    # 运行测试
    test_functions = [
        test_serial_manager_initialization,
        test_serial_connection,
        test_data_write_read,
        test_state_callbacks,
        test_context_manager,
        test_reconnection,
        test_statistics
    ]
    
    passed = 0
    failed = 0
    
    for test_func in test_functions:
        try:
            test_func()
            passed += 1
        except Exception as e:
            print(f"❌ 测试失败: {test_func.__name__} - {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查虚拟串口配置")


if __name__ == "__main__":
    run_all_tests()
