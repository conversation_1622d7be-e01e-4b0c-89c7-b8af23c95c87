#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行所有utils模块的单元测试
统一测试入口和结果汇总

作者: LD (Lead Developer)
创建时间: 2025-08-06
版本: 1.0
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


def run_test_file(test_file_path):
    """运行单个测试文件"""
    try:
        print(f"\n{'='*80}")
        print(f"运行测试文件: {test_file_path.name}")
        print(f"{'='*80}")
        
        # 使用subprocess运行测试文件
        result = subprocess.run(
            [sys.executable, str(test_file_path)],
            cwd=str(project_root),
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        # 输出测试结果
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 运行测试文件失败: {e}")
        return False


def main():
    """主函数"""
    print("DataStudio Utils模块单元测试套件")
    print("=" * 80)
    print("开始运行所有utils模块的单元测试...")
    
    # 获取测试文件目录
    test_dir = Path(__file__).parent
    
    # 定义测试文件列表（按执行顺序）
    test_files = [
        "test_logging_config.py",
        "test_helper_utils.py", 
        "test_system_config_manager.py",
        "test_serial_config_manager.py",
        "test_validators.py"
    ]
    
    # 验证测试文件是否存在
    missing_files = []
    for test_file in test_files:
        test_file_path = test_dir / test_file
        if not test_file_path.exists():
            missing_files.append(test_file)
    
    if missing_files:
        print(f"❌ 以下测试文件不存在:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    # 运行所有测试
    test_results = []
    
    for test_file in test_files:
        test_file_path = test_dir / test_file
        success = run_test_file(test_file_path)
        test_results.append((test_file, success))
    
    # 输出总体测试结果
    print(f"\n{'='*80}")
    print("总体测试结果汇总")
    print(f"{'='*80}")
    
    passed = 0
    failed = 0
    
    for test_file, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_file:<35} {status}")
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\n{'='*80}")
    print(f"测试文件总计: {len(test_results)} 个")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    
    if failed == 0:
        print("\n🎉 所有测试文件都通过了！")
        print("✅ Utils模块功能验证完成，所有组件工作正常")
        return True
    else:
        print(f"\n⚠️  有 {failed} 个测试文件失败")
        print("❌ 请检查失败的测试并修复相关问题")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
