#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义异常类定义
定义数据采集系统中使用的所有自定义异常

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

from typing import Optional, Any, Dict


class DataStudioException(Exception):
    """
    数据采集系统基础异常类
    所有自定义异常的基类
    """
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 details: Optional[Dict[str, Any]] = None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 错误详细信息
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self) -> str:
        """返回异常的字符串表示"""
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def to_dict(self) -> Dict[str, Any]:
        """将异常转换为字典格式"""
        return {
            "type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details
        }


# ============================================================================
# 配置相关异常
# ============================================================================

class ConfigurationError(DataStudioException):
    """配置相关异常基类"""
    pass


class ConfigFileNotFoundError(ConfigurationError):
    """配置文件未找到异常"""
    
    def __init__(self, file_path: str):
        super().__init__(
            f"配置文件未找到: {file_path}",
            error_code="CONFIG_FILE_NOT_FOUND",
            details={"file_path": file_path}
        )


class ConfigValidationError(ConfigurationError):
    """配置验证异常"""

    def __init__(self, message: str, field_path: Optional[str] = None,
                 validation_errors: Optional[list] = None):
        super().__init__(
            message,
            error_code="CONFIG_VALIDATION_ERROR",
            details={
                "field_path": field_path,
                "validation_errors": validation_errors or []
            }
        )

    def __str__(self) -> str:
        """
        返回包含字段路径信息的异常字符串

        如果存在字段路径信息，会在基础异常信息后添加字段路径，
        使异常信息更加完整和有用，便于调试和错误定位。

        Returns:
            包含字段路径信息的完整异常字符串
        """
        base_message = super().__str__()
        field_path = self.details.get("field_path")
        if field_path:
            return f"{base_message} (字段: {field_path})"
        return base_message


class ConfigParsingError(ConfigurationError):
    """配置解析异常"""
    
    def __init__(self, message: str, file_path: Optional[str] = None):
        super().__init__(
            message,
            error_code="CONFIG_PARSING_ERROR",
            details={"file_path": file_path}
        )


# ============================================================================
# 串口通信相关异常
# ============================================================================

class CommunicationError(DataStudioException):
    """串口通信异常基类"""
    pass


class SerialConnectionError(CommunicationError):
    """串口连接异常"""
    
    def __init__(self, port: str, message: str = "串口连接失败"):
        super().__init__(
            f"{message}: {port}",
            error_code="SERIAL_CONNECTION_ERROR",
            details={"port": port}
        )


class SerialTimeoutError(CommunicationError):
    """串口超时异常"""
    
    def __init__(self, operation: str, timeout: float):
        super().__init__(
            f"串口操作超时: {operation} (超时时间: {timeout}s)",
            error_code="SERIAL_TIMEOUT_ERROR",
            details={"operation": operation, "timeout": timeout}
        )


class SerialWriteError(CommunicationError):
    """串口写入异常"""
    
    def __init__(self, data: str, message: str = "串口写入失败"):
        super().__init__(
            f"{message}: {data}",
            error_code="SERIAL_WRITE_ERROR",
            details={"data": data}
        )


class SerialReadError(CommunicationError):
    """串口读取异常"""
    
    def __init__(self, message: str = "串口读取失败"):
        super().__init__(
            message,
            error_code="SERIAL_READ_ERROR"
        )


# ============================================================================
# 数据处理相关异常
# ============================================================================

class DataProcessingError(DataStudioException):
    """数据处理异常基类"""
    pass


class FrameDetectionError(DataProcessingError):
    """帧检测异常"""
    
    def __init__(self, message: str, raw_data: Optional[bytes] = None):
        super().__init__(
            f"帧检测失败: {message}",
            error_code="FRAME_DETECTION_ERROR",
            details={"raw_data": raw_data.hex() if raw_data else None}
        )


class DataParsingError(DataProcessingError):
    """数据解析异常"""
    
    def __init__(self, message: str, field_name: Optional[str] = None, 
                 raw_data: Optional[bytes] = None):
        super().__init__(
            f"数据解析失败: {message}",
            error_code="DATA_PARSING_ERROR",
            details={
                "field_name": field_name,
                "raw_data": raw_data.hex() if raw_data else None
            }
        )


class ValidationError(DataProcessingError):
    """数据验证异常"""
    
    def __init__(self, message: str, expected: Optional[str] = None, 
                 actual: Optional[str] = None):
        super().__init__(
            f"数据验证失败: {message}",
            error_code="VALIDATION_ERROR",
            details={"expected": expected, "actual": actual}
        )


class QueueFullError(DataProcessingError):
    """队列满异常"""
    
    def __init__(self, queue_name: str, max_size: int):
        super().__init__(
            f"队列已满: {queue_name} (最大大小: {max_size})",
            error_code="QUEUE_FULL_ERROR",
            details={"queue_name": queue_name, "max_size": max_size}
        )


# ============================================================================
# 缓冲区相关异常
# ============================================================================

class BufferError(DataStudioException):
    """缓冲区异常基类"""
    pass


class BufferOverflowError(BufferError):
    """缓冲区溢出异常"""
    
    def __init__(self, buffer_size: int, data_size: int):
        super().__init__(
            f"缓冲区溢出: 数据大小 {data_size} 超过缓冲区大小 {buffer_size}",
            error_code="BUFFER_OVERFLOW_ERROR",
            details={"buffer_size": buffer_size, "data_size": data_size}
        )


class BufferUnderflowError(BufferError):
    """缓冲区下溢异常"""
    
    def __init__(self, requested_size: int, available_size: int):
        super().__init__(
            f"缓冲区下溢: 请求大小 {requested_size} 超过可用大小 {available_size}",
            error_code="BUFFER_UNDERFLOW_ERROR",
            details={"requested_size": requested_size, "available_size": available_size}
        )


# ============================================================================
# 业务逻辑相关异常
# ============================================================================

class BusinessLogicError(DataStudioException):
    """业务逻辑异常基类"""
    pass


class ProtocolFlowError(BusinessLogicError):
    """协议流程异常"""
    
    def __init__(self, message: str, current_step: Optional[str] = None):
        super().__init__(
            f"协议流程错误: {message}",
            error_code="PROTOCOL_FLOW_ERROR",
            details={"current_step": current_step}
        )


class CommandExecutionError(BusinessLogicError):
    """指令执行异常"""
    
    def __init__(self, command: str, message: str = "指令执行失败"):
        super().__init__(
            f"{message}: {command}",
            error_code="COMMAND_EXECUTION_ERROR",
            details={"command": command}
        )


class MaxRetriesExceededError(BusinessLogicError):
    """最大重试次数超限异常"""
    
    def __init__(self, operation: str, max_retries: int):
        super().__init__(
            f"操作重试次数超限: {operation} (最大重试次数: {max_retries})",
            error_code="MAX_RETRIES_EXCEEDED_ERROR",
            details={"operation": operation, "max_retries": max_retries}
        )


class ContinuousErrorThresholdExceededError(BusinessLogicError):
    """连续错误阈值超限异常"""
    
    def __init__(self, error_count: int, threshold: int):
        super().__init__(
            f"连续错误次数超限: {error_count} (阈值: {threshold})",
            error_code="CONTINUOUS_ERROR_THRESHOLD_EXCEEDED",
            details={"error_count": error_count, "threshold": threshold}
        )


# ============================================================================
# 系统相关异常
# ============================================================================

class SystemError(DataStudioException):
    """系统异常基类"""
    pass


class InitializationError(SystemError):
    """系统初始化异常"""
    
    def __init__(self, component: str, message: str = "组件初始化失败"):
        super().__init__(
            f"{message}: {component}",
            error_code="INITIALIZATION_ERROR",
            details={"component": component}
        )


class ResourceNotAvailableError(SystemError):
    """资源不可用异常"""
    
    def __init__(self, resource: str, message: str = "资源不可用"):
        super().__init__(
            f"{message}: {resource}",
            error_code="RESOURCE_NOT_AVAILABLE_ERROR",
            details={"resource": resource}
        )


class ThreadError(SystemError):
    """线程异常"""
    
    def __init__(self, thread_name: str, message: str = "线程错误"):
        super().__init__(
            f"{message}: {thread_name}",
            error_code="THREAD_ERROR",
            details={"thread_name": thread_name}
        )


# ============================================================================
# 用户界面相关异常
# ============================================================================

class UIError(DataStudioException):
    """用户界面异常基类"""
    pass


class InvalidUserInputError(UIError):
    """无效用户输入异常"""
    
    def __init__(self, input_value: str, expected_format: str):
        super().__init__(
            f"无效的用户输入: {input_value} (期望格式: {expected_format})",
            error_code="INVALID_USER_INPUT_ERROR",
            details={"input_value": input_value, "expected_format": expected_format}
        )


class DisplayError(UIError):
    """显示异常"""
    
    def __init__(self, message: str, component: Optional[str] = None):
        super().__init__(
            f"显示错误: {message}",
            error_code="DISPLAY_ERROR",
            details={"component": component}
        )


# ============================================================================
# 异常工具函数
# ============================================================================

def format_exception_info(exception: Exception) -> Dict[str, Any]:
    """
    格式化异常信息为字典
    
    Args:
        exception: 异常对象
        
    Returns:
        格式化的异常信息字典
    """
    if isinstance(exception, DataStudioException):
        return exception.to_dict()
    
    return {
        "type": exception.__class__.__name__,
        "message": str(exception),
        "error_code": None,
        "details": {}
    }


def is_recoverable_error(exception: Exception) -> bool:
    """
    判断异常是否可恢复
    
    Args:
        exception: 异常对象
        
    Returns:
        是否可恢复
    """
    # 可恢复的异常类型
    recoverable_types = (
        SerialTimeoutError,
        SerialReadError,
        SerialWriteError,
        FrameDetectionError,
        ValidationError,
        QueueFullError
    )
    
    return isinstance(exception, recoverable_types)


def get_error_severity(exception: Exception) -> str:
    """
    获取异常严重程度
    
    Args:
        exception: 异常对象
        
    Returns:
        严重程度: "low", "medium", "high", "critical"
    """
    if isinstance(exception, (
        SerialTimeoutError, 
        FrameDetectionError, 
        ValidationError
    )):
        return "low"
    
    elif isinstance(exception, (
        DataParsingError,
        CommandExecutionError,
        QueueFullError
    )):
        return "medium"
    
    elif isinstance(exception, (
        SerialConnectionError,
        ConfigValidationError,
        ProtocolFlowError
    )):
        return "high"
    
    elif isinstance(exception, (
        ConfigFileNotFoundError,
        InitializationError,
        ContinuousErrorThresholdExceededError
    )):
        return "critical"
    
    return "medium"  # 默认中等严重程度
