"""
错误处理器 - 业务逻辑层组件

该模块实现了智能错误处理和自动恢复机制，负责：
- 分级错误处理和自动恢复策略
- 连续错误计数和暂停机制
- 错误类型分类和统计分析
- 错误恢复和重试策略

核心特性：
- 智能错误分类：根据错误类型采用不同处理策略
- 自动恢复机制：支持多种错误恢复策略
- 连续错误监控：防止错误累积导致系统不稳定
- 统计分析：详细的错误统计和趋势分析

作者: DataStudio开发团队
创建时间: 2025-08-08
"""

import logging
import time
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from collections import deque


class ErrorType(Enum):
    """错误类型枚举"""
    COMMUNICATION_ERROR = "communication"    # 通信错误
    PROTOCOL_ERROR = "protocol"             # 协议错误
    DATA_PARSING_ERROR = "data_parsing"     # 数据解析错误
    VALIDATION_ERROR = "validation"         # 验证错误
    TIMEOUT_ERROR = "timeout"               # 超时错误
    HARDWARE_ERROR = "hardware"             # 硬件错误
    CONFIGURATION_ERROR = "configuration"   # 配置错误
    SYSTEM_ERROR = "system"                 # 系统错误


class ErrorSeverity(Enum):
    """错误严重程度枚举"""
    LOW = 1      # 低级错误，可以忽略
    MEDIUM = 2   # 中级错误，需要记录
    HIGH = 3     # 高级错误，需要处理
    CRITICAL = 4 # 严重错误，需要立即处理


class RecoveryAction(Enum):
    """恢复动作枚举"""
    IGNORE = "ignore"                    # 忽略错误
    RETRY = "retry"                      # 重试操作
    RESET_CONNECTION = "reset_connection" # 重置连接
    RESTART_COMPONENT = "restart_component" # 重启组件
    PAUSE_OPERATION = "pause_operation"   # 暂停操作
    STOP_OPERATION = "stop_operation"     # 停止操作


@dataclass
class ErrorRecord:
    """错误记录"""
    timestamp: float                     # 错误时间戳
    error_type: ErrorType               # 错误类型
    severity: ErrorSeverity             # 严重程度
    message: str                        # 错误消息
    details: Dict[str, Any]             # 错误详情
    component: str                      # 出错组件
    recovery_action: Optional[RecoveryAction] = None  # 恢复动作
    recovery_success: Optional[bool] = None           # 恢复是否成功


@dataclass
class ErrorThreshold:
    """错误阈值配置"""
    error_type: ErrorType               # 错误类型
    max_count: int                      # 最大错误次数
    time_window: float                  # 时间窗口（秒）
    recovery_action: RecoveryAction     # 恢复动作


class ErrorHandler:
    """
    错误处理器
    
    核心功能：
    1. 智能错误处理和分类
    2. 自动恢复策略执行
    3. 连续错误监控和阈值管理
    4. 错误统计和趋势分析
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化错误处理器
        
        Args:
            config: 错误处理配置
        """
        self.logger = logging.getLogger(f"{__name__}.ErrorHandler")
        
        # 错误记录
        self.error_history = deque(maxlen=1000)  # 保留最近1000条错误记录
        self.recent_errors = deque(maxlen=100)   # 保留最近100条错误用于快速分析
        
        # 错误阈值配置
        self.error_thresholds = self._initialize_thresholds(config)
        
        # 恢复策略回调
        self.recovery_callbacks: Dict[RecoveryAction, Callable] = {}
        
        # 统计信息
        self.stats = {
            "total_errors": 0,
            "errors_by_type": {error_type.value: 0 for error_type in ErrorType},
            "errors_by_severity": {severity.value: 0 for severity in ErrorSeverity},
            "recovery_attempts": 0,
            "recovery_successes": 0,
            "recovery_failures": 0,
            "consecutive_errors": 0,
            "max_consecutive_errors": 0,
            "last_error_time": None,
            "error_rate_per_minute": 0.0
        }
        
        # 连续错误监控
        self.consecutive_error_count = 0
        self.last_error_time = None
        self.is_paused = False
        
        self.logger.info("错误处理器初始化完成")
    
    def _initialize_thresholds(self, config: Dict[str, Any] = None) -> List[ErrorThreshold]:
        """初始化错误阈值配置"""
        default_thresholds = [
            ErrorThreshold(ErrorType.COMMUNICATION_ERROR, 5, 60.0, RecoveryAction.RESET_CONNECTION),
            ErrorThreshold(ErrorType.PROTOCOL_ERROR, 3, 30.0, RecoveryAction.RETRY),
            ErrorThreshold(ErrorType.DATA_PARSING_ERROR, 10, 60.0, RecoveryAction.PAUSE_OPERATION),
            ErrorThreshold(ErrorType.VALIDATION_ERROR, 5, 30.0, RecoveryAction.RETRY),
            ErrorThreshold(ErrorType.TIMEOUT_ERROR, 3, 30.0, RecoveryAction.RESET_CONNECTION),
            ErrorThreshold(ErrorType.HARDWARE_ERROR, 1, 60.0, RecoveryAction.STOP_OPERATION),
            ErrorThreshold(ErrorType.CONFIGURATION_ERROR, 1, 60.0, RecoveryAction.STOP_OPERATION),
            ErrorThreshold(ErrorType.SYSTEM_ERROR, 2, 60.0, RecoveryAction.RESTART_COMPONENT)
        ]
        
        if config and "error_thresholds" in config:
            # 从配置中加载自定义阈值
            custom_thresholds = []
            for threshold_config in config["error_thresholds"]:
                threshold = ErrorThreshold(
                    error_type=ErrorType(threshold_config["error_type"]),
                    max_count=threshold_config["max_count"],
                    time_window=threshold_config["time_window"],
                    recovery_action=RecoveryAction(threshold_config["recovery_action"])
                )
                custom_thresholds.append(threshold)
            return custom_thresholds
        
        return default_thresholds
    
    def handle_error(self, error_type: ErrorType, message: str, 
                    component: str = "unknown", details: Dict[str, Any] = None,
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM) -> bool:
        """
        处理错误
        
        Args:
            error_type: 错误类型
            message: 错误消息
            component: 出错组件
            details: 错误详情
            severity: 错误严重程度
            
        Returns:
            是否需要停止当前操作
        """
        current_time = time.time()
        
        # 创建错误记录
        error_record = ErrorRecord(
            timestamp=current_time,
            error_type=error_type,
            severity=severity,
            message=message,
            details=details or {},
            component=component
        )
        
        # 记录错误
        self.error_history.append(error_record)
        self.recent_errors.append(error_record)
        
        # 更新统计信息
        self._update_stats(error_record)
        
        # 更新连续错误计数
        self._update_consecutive_errors(current_time)
        
        # 记录日志
        self._log_error(error_record)
        
        # 检查是否需要恢复动作
        recovery_action = self._determine_recovery_action(error_type, current_time)
        if recovery_action:
            error_record.recovery_action = recovery_action
            recovery_success = self._execute_recovery_action(recovery_action, error_record)
            error_record.recovery_success = recovery_success
            
            if recovery_success:
                self.consecutive_error_count = 0  # 恢复成功，重置连续错误计数
        
        # 检查是否需要暂停操作
        should_pause = self._should_pause_operation(error_type, severity)
        if should_pause and not self.is_paused:
            self.is_paused = True
            self.logger.warning(f"由于错误频繁，暂停操作: {error_type.value}")
        
        return should_pause
    
    def _update_stats(self, error_record: ErrorRecord):
        """更新统计信息"""
        self.stats["total_errors"] += 1
        self.stats["errors_by_type"][error_record.error_type.value] += 1
        self.stats["errors_by_severity"][error_record.severity.value] += 1
        self.stats["last_error_time"] = error_record.timestamp
        
        # 计算错误率（每分钟）
        current_time = time.time()
        one_minute_ago = current_time - 60.0
        recent_error_count = sum(1 for error in self.recent_errors 
                               if error.timestamp >= one_minute_ago)
        self.stats["error_rate_per_minute"] = recent_error_count
    
    def _update_consecutive_errors(self, current_time: float):
        """更新连续错误计数"""
        if self.last_error_time is None or (current_time - self.last_error_time) > 30.0:
            # 如果距离上次错误超过30秒，重置连续错误计数
            self.consecutive_error_count = 1
        else:
            self.consecutive_error_count += 1
        
        self.last_error_time = current_time
        self.stats["consecutive_errors"] = self.consecutive_error_count
        
        if self.consecutive_error_count > self.stats["max_consecutive_errors"]:
            self.stats["max_consecutive_errors"] = self.consecutive_error_count
    
    def _log_error(self, error_record: ErrorRecord):
        """记录错误日志"""
        log_message = f"[{error_record.component}] {error_record.error_type.value}: {error_record.message}"
        
        if error_record.severity == ErrorSeverity.LOW:
            self.logger.debug(log_message)
        elif error_record.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        elif error_record.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message)
        elif error_record.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message)
        
        # 记录详细信息
        if error_record.details:
            self.logger.debug(f"错误详情: {error_record.details}")
    
    def _determine_recovery_action(self, error_type: ErrorType, current_time: float) -> Optional[RecoveryAction]:
        """确定恢复动作"""
        # 查找匹配的阈值配置
        for threshold in self.error_thresholds:
            if threshold.error_type == error_type:
                # 计算时间窗口内的错误次数
                window_start = current_time - threshold.time_window
                error_count = sum(1 for error in self.recent_errors 
                                if error.error_type == error_type and error.timestamp >= window_start)
                
                if error_count >= threshold.max_count:
                    return threshold.recovery_action
        
        return None
    
    def _execute_recovery_action(self, action: RecoveryAction, error_record: ErrorRecord) -> bool:
        """执行恢复动作"""
        self.stats["recovery_attempts"] += 1
        
        try:
            self.logger.info(f"执行恢复动作: {action.value}")
            
            if action in self.recovery_callbacks:
                # 调用注册的恢复回调
                success = self.recovery_callbacks[action](error_record)
                
                if success:
                    self.stats["recovery_successes"] += 1
                    self.logger.info(f"恢复动作执行成功: {action.value}")
                else:
                    self.stats["recovery_failures"] += 1
                    self.logger.warning(f"恢复动作执行失败: {action.value}")
                
                return success
            else:
                # 默认恢复动作
                success = self._default_recovery_action(action, error_record)
                
                if success:
                    self.stats["recovery_successes"] += 1
                else:
                    self.stats["recovery_failures"] += 1
                
                return success
                
        except Exception as e:
            self.stats["recovery_failures"] += 1
            self.logger.error(f"恢复动作执行异常: {action.value}, 错误: {str(e)}")
            return False
    
    def _default_recovery_action(self, action: RecoveryAction, error_record: ErrorRecord) -> bool:
        """默认恢复动作"""
        if action == RecoveryAction.IGNORE:
            return True
        elif action == RecoveryAction.RETRY:
            # 默认重试策略：等待一段时间
            time.sleep(1.0)
            return True
        elif action == RecoveryAction.PAUSE_OPERATION:
            self.is_paused = True
            return True
        else:
            # 其他动作需要外部实现
            self.logger.warning(f"未实现的恢复动作: {action.value}")
            return False
    
    def _should_pause_operation(self, error_type: ErrorType, severity: ErrorSeverity) -> bool:
        """判断是否应该暂停操作"""
        # 严重错误立即暂停
        if severity == ErrorSeverity.CRITICAL:
            return True
        
        # 连续错误过多时暂停
        if self.consecutive_error_count >= 10:
            return True
        
        # 特定错误类型的暂停策略
        if error_type == ErrorType.HARDWARE_ERROR:
            return True
        
        return False
    
    def register_recovery_callback(self, action: RecoveryAction, callback: Callable):
        """
        注册恢复动作回调函数
        
        Args:
            action: 恢复动作
            callback: 回调函数，接收ErrorRecord参数，返回bool表示是否成功
        """
        self.recovery_callbacks[action] = callback
        self.logger.info(f"已注册恢复动作回调: {action.value}")
    
    def clear_consecutive_errors(self):
        """清除连续错误计数"""
        self.consecutive_error_count = 0
        self.stats["consecutive_errors"] = 0
        self.logger.info("连续错误计数已清除")
    
    def resume_operation(self):
        """恢复操作"""
        if self.is_paused:
            self.is_paused = False
            self.consecutive_error_count = 0
            self.logger.info("操作已恢复")
    
    def get_error_summary(self, time_window: float = 300.0) -> Dict[str, Any]:
        """
        获取错误摘要
        
        Args:
            time_window: 时间窗口（秒）
            
        Returns:
            错误摘要信息
        """
        current_time = time.time()
        window_start = current_time - time_window
        
        # 筛选时间窗口内的错误
        recent_errors = [error for error in self.recent_errors 
                        if error.timestamp >= window_start]
        
        # 统计各类错误
        error_counts = {}
        severity_counts = {}
        component_counts = {}
        
        for error in recent_errors:
            error_counts[error.error_type.value] = error_counts.get(error.error_type.value, 0) + 1
            severity_counts[error.severity.value] = severity_counts.get(error.severity.value, 0) + 1
            component_counts[error.component] = component_counts.get(error.component, 0) + 1
        
        return {
            "time_window": time_window,
            "total_errors": len(recent_errors),
            "error_counts_by_type": error_counts,
            "error_counts_by_severity": severity_counts,
            "error_counts_by_component": component_counts,
            "consecutive_errors": self.consecutive_error_count,
            "is_paused": self.is_paused,
            "error_rate": len(recent_errors) / (time_window / 60.0)  # 每分钟错误率
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 计算恢复成功率
        if stats["recovery_attempts"] > 0:
            stats["recovery_success_rate"] = stats["recovery_successes"] / stats["recovery_attempts"] * 100
        else:
            stats["recovery_success_rate"] = 0.0
        
        # 添加状态信息
        stats["is_paused"] = self.is_paused
        stats["total_error_records"] = len(self.error_history)
        stats["recent_error_records"] = len(self.recent_errors)
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            "total_errors": 0,
            "errors_by_type": {error_type.value: 0 for error_type in ErrorType},
            "errors_by_severity": {severity.value: 0 for severity in ErrorSeverity},
            "recovery_attempts": 0,
            "recovery_successes": 0,
            "recovery_failures": 0,
            "consecutive_errors": 0,
            "max_consecutive_errors": 0,
            "last_error_time": None,
            "error_rate_per_minute": 0.0
        }
        
        self.consecutive_error_count = 0
        self.last_error_time = None
        self.is_paused = False
        
        self.logger.info("错误处理器统计信息已重置")
    
    def clear_error_history(self):
        """清空错误历史"""
        self.error_history.clear()
        self.recent_errors.clear()
        self.logger.info("错误历史已清空")
    
    def get_recent_errors(self, count: int = 10) -> List[ErrorRecord]:
        """
        获取最近的错误记录
        
        Args:
            count: 返回的错误记录数量
            
        Returns:
            最近的错误记录列表
        """
        return list(self.recent_errors)[-count:]
    
    def is_operation_paused(self) -> bool:
        """检查操作是否被暂停"""
        return self.is_paused