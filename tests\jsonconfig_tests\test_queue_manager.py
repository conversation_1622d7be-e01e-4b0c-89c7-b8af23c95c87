#!/usr/bin/env python3
"""
队列管理器测试模块

测试QueueManager的所有功能，包括：
1. 基于JSON配置的动态队列管理
2. 高性能队列操作
3. 队列状态监控
4. 批量处理支持
5. 线程安全操作

运行方式：
python test_queue_manager.py

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import sys
import os
import unittest
import json
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from data_processing.queue_manager import QueueManager, QueueConfig, QueueItem, QueueStatus
from utils.exceptions import DataProcessingError


class TestQueueConfig(unittest.TestCase):
    """测试队列配置类"""
    
    def test_valid_config_creation(self):
        """测试有效配置创建"""
        config = QueueConfig(
            queue_size=100,
            warning_threshold=0.8,
            batch_size=10
        )
        
        self.assertEqual(config.queue_size, 100)
        self.assertEqual(config.warning_threshold, 0.8)
        self.assertEqual(config.batch_size, 10)
    
    def test_invalid_queue_size(self):
        """测试无效队列大小"""
        with self.assertRaises(ValueError):
            QueueConfig(
                queue_size=0,
                warning_threshold=0.8,
                batch_size=10
            )
    
    def test_invalid_warning_threshold(self):
        """测试无效警告阈值"""
        with self.assertRaises(ValueError):
            QueueConfig(
                queue_size=100,
                warning_threshold=1.5,  # 超过1
                batch_size=10
            )
    
    def test_invalid_batch_size(self):
        """测试无效批处理大小"""
        with self.assertRaises(ValueError):
            QueueConfig(
                queue_size=100,
                warning_threshold=0.8,
                batch_size=0
            )
    
    def test_batch_size_larger_than_queue_size(self):
        """测试批处理大小大于队列大小"""
        with self.assertRaises(ValueError):
            QueueConfig(
                queue_size=10,
                warning_threshold=0.8,
                batch_size=20
            )


class TestQueueManager(unittest.TestCase):
    """测试队列管理器类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = QueueConfig(
            queue_size=10,
            warning_threshold=0.8,
            batch_size=5
        )
        self.queue_manager = QueueManager(self.config)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.queue_manager.config.queue_size, 10)
        self.assertEqual(self.queue_manager.config.warning_threshold, 0.8)
        self.assertEqual(self.queue_manager.config.batch_size, 5)
        
        stats = self.queue_manager.get_statistics()
        self.assertEqual(stats["items_added"], 0)
        self.assertEqual(stats["items_removed"], 0)
        
        usage_info = self.queue_manager.get_usage_info()
        self.assertEqual(usage_info["current_size"], 0)
        self.assertEqual(usage_info["status"], "empty")
    
    def test_put_and_get_single_item(self):
        """测试单个项目的添加和获取"""
        test_data = "test_data"
        
        # 添加数据
        success = self.queue_manager.put(test_data, "test_type", {"key": "value"})
        self.assertTrue(success)
        
        # 获取数据
        item = self.queue_manager.get(block=False)
        self.assertIsNotNone(item)
        self.assertEqual(item.data, test_data)
        self.assertEqual(item.item_type, "test_type")
        self.assertEqual(item.metadata["key"], "value")
        self.assertIsInstance(item.timestamp, float)
    
    def test_queue_status_changes(self):
        """测试队列状态变化"""
        # 初始状态应该是空
        self.assertEqual(self.queue_manager.get_status(), QueueStatus.EMPTY)
        
        # 添加一些数据，状态应该变为正常
        for i in range(5):
            self.queue_manager.put(f"data_{i}")
        self.assertEqual(self.queue_manager.get_status(), QueueStatus.NORMAL)
        
        # 添加更多数据，状态应该变为警告（8个项目，阈值0.8 * 10 = 8）
        for i in range(3):
            self.queue_manager.put(f"data_{i+5}")
        self.assertEqual(self.queue_manager.get_status(), QueueStatus.WARNING)
        
        # 填满队列，状态应该变为满
        for i in range(2):
            self.queue_manager.put(f"data_{i+8}")
        self.assertEqual(self.queue_manager.get_status(), QueueStatus.FULL)
    
    def test_queue_full_handling(self):
        """测试队列满时的处理"""
        # 填满队列
        for i in range(10):
            success = self.queue_manager.put(f"data_{i}")
            self.assertTrue(success)
        
        # 尝试添加更多数据（非阻塞）
        success = self.queue_manager.put("overflow_data", block=False)
        self.assertFalse(success)
        
        stats = self.queue_manager.get_statistics()
        self.assertEqual(stats["items_dropped"], 1)
        self.assertEqual(stats["queue_full_events"], 1)
    
    def test_batch_operations(self):
        """测试批量操作"""
        # 批量添加数据
        test_data = [f"batch_data_{i}" for i in range(8)]
        added_count = self.queue_manager.put_batch(test_data, "batch_type")
        self.assertEqual(added_count, 8)
        
        # 批量获取数据
        items = self.queue_manager.get_batch(max_items=5)
        self.assertEqual(len(items), 5)
        
        for i, item in enumerate(items):
            self.assertEqual(item.data, f"batch_data_{i}")
            self.assertEqual(item.item_type, "batch_type")
        
        # 获取剩余数据
        remaining_items = self.queue_manager.get_batch()
        self.assertEqual(len(remaining_items), 3)
    
    def test_batch_add_overflow(self):
        """测试批量添加时的溢出处理"""
        # 先添加一些数据
        for i in range(7):
            self.queue_manager.put(f"existing_data_{i}")
        
        # 尝试批量添加更多数据（会溢出）
        overflow_data = [f"overflow_data_{i}" for i in range(5)]
        added_count = self.queue_manager.put_batch(overflow_data)
        
        # 只能添加3个（队列大小10 - 已有7个 = 3个空位）
        self.assertEqual(added_count, 3)
        
        # 验证队列已满
        self.assertEqual(self.queue_manager.get_status(), QueueStatus.FULL)
    
    def test_clear_queue(self):
        """测试清空队列"""
        # 添加一些数据
        for i in range(5):
            self.queue_manager.put(f"data_{i}")
        
        # 清空队列
        cleared_count = self.queue_manager.clear()
        self.assertEqual(cleared_count, 5)
        
        # 验证队列为空
        self.assertEqual(self.queue_manager.get_status(), QueueStatus.EMPTY)
        
        usage_info = self.queue_manager.get_usage_info()
        self.assertEqual(usage_info["current_size"], 0)
    
    def test_get_empty_queue(self):
        """测试从空队列获取数据"""
        # 非阻塞获取
        item = self.queue_manager.get(block=False)
        self.assertIsNone(item)
        
        # 批量获取
        items = self.queue_manager.get_batch(timeout=0.1)
        self.assertEqual(len(items), 0)
    
    def test_usage_info(self):
        """测试使用信息获取"""
        # 添加一些数据
        for i in range(6):
            self.queue_manager.put(f"data_{i}")
        
        usage_info = self.queue_manager.get_usage_info()
        
        self.assertEqual(usage_info["current_size"], 6)
        self.assertEqual(usage_info["max_size"], 10)
        self.assertEqual(usage_info["usage_rate"], 0.6)
        self.assertEqual(usage_info["usage_percentage"], 60.0)
        self.assertEqual(usage_info["available_space"], 4)
        self.assertEqual(usage_info["status"], "normal")
        self.assertFalse(usage_info["is_empty"])
        self.assertFalse(usage_info["is_full"])
        self.assertFalse(usage_info["is_warning"])
    
    def test_statistics_tracking(self):
        """测试统计信息跟踪"""
        # 执行一些操作
        for i in range(5):
            self.queue_manager.put(f"data_{i}")
        
        # 批量操作
        self.queue_manager.put_batch([f"batch_data_{i}" for i in range(3)])
        
        # 获取一些数据
        for i in range(3):
            self.queue_manager.get()
        
        stats = self.queue_manager.get_statistics()
        
        self.assertEqual(stats["items_added"], 8)  # 5个单独 + 3个批量
        self.assertEqual(stats["items_removed"], 3)
        self.assertEqual(stats["batch_operations"], 1)  # 1次批量添加
        self.assertEqual(stats["items_dropped"], 0)
    
    def test_statistics_reset(self):
        """测试统计信息重置"""
        # 执行一些操作
        self.queue_manager.put("test_data")
        self.queue_manager.get()
        
        # 重置统计
        self.queue_manager.reset_statistics()
        
        stats = self.queue_manager.get_statistics()
        self.assertEqual(stats["items_added"], 0)
        self.assertEqual(stats["items_removed"], 0)
    
    def test_status_change_callback(self):
        """测试状态变化回调"""
        status_changes = []
        
        def status_callback(old_status, new_status):
            status_changes.append((old_status, new_status))
        
        # 添加回调
        self.queue_manager.add_status_change_callback(status_callback)
        
        # 触发状态变化
        self.queue_manager.put("data1")  # EMPTY -> NORMAL
        
        for i in range(7):
            self.queue_manager.put(f"data_{i+2}")  # NORMAL -> WARNING
        
        for i in range(2):
            self.queue_manager.put(f"data_{i+9}")  # WARNING -> FULL
        
        # 验证回调被调用
        self.assertEqual(len(status_changes), 3)
        self.assertEqual(status_changes[0], (QueueStatus.EMPTY, QueueStatus.NORMAL))
        self.assertEqual(status_changes[1], (QueueStatus.NORMAL, QueueStatus.WARNING))
        self.assertEqual(status_changes[2], (QueueStatus.WARNING, QueueStatus.FULL))
        
        # 移除回调
        self.queue_manager.remove_status_change_callback(status_callback)
    
    def test_thread_safety(self):
        """测试线程安全"""
        results = []
        
        def producer():
            for i in range(50):
                success = self.queue_manager.put(f"thread_data_{i}")
                results.append(("put", success))
                time.sleep(0.001)  # 小延迟
        
        def consumer():
            for i in range(50):
                item = self.queue_manager.get(timeout=1.0)
                results.append(("get", item is not None))
                time.sleep(0.001)  # 小延迟
        
        # 创建线程
        producer_thread = threading.Thread(target=producer)
        consumer_thread = threading.Thread(target=consumer)
        
        # 启动线程
        producer_thread.start()
        consumer_thread.start()
        
        # 等待完成
        producer_thread.join()
        consumer_thread.join()
        
        # 验证结果
        put_results = [r[1] for r in results if r[0] == "put"]
        get_results = [r[1] for r in results if r[0] == "get"]
        
        # 大部分操作应该成功（可能有一些因为队列满而失败）
        self.assertGreater(sum(put_results), 40)
        self.assertGreater(sum(get_results), 40)


class TestQueueManagerFromConfig(unittest.TestCase):
    """测试从配置创建队列管理器"""
    
    def test_create_from_config_dict(self):
        """测试从配置字典创建"""
        config_dict = {
            "response_queue_size": 50,
            "queue_warning_threshold": 0.75,
            "batch_processing_size": 8
        }
        
        queue_manager = QueueManager.create_from_config(config_dict)
        
        self.assertEqual(queue_manager.config.queue_size, 50)
        self.assertEqual(queue_manager.config.warning_threshold, 0.75)
        self.assertEqual(queue_manager.config.batch_size, 8)
    
    def test_create_from_invalid_config(self):
        """测试从无效配置创建"""
        invalid_config = {
            "response_queue_size": -1,  # 无效值
            "queue_warning_threshold": 0.75,
            "batch_processing_size": 8
        }
        
        with self.assertRaises(DataProcessingError):
            QueueManager.create_from_config(invalid_config)
    
    def test_create_from_system_config(self):
        """测试从系统配置文件创建"""
        # 模拟系统配置
        system_config = {
            "queue_config": {
                "response_queue_size": 100,
                "queue_warning_threshold": 0.8,
                "batch_processing_size": 10
            }
        }
        
        queue_manager = QueueManager.create_from_config(system_config["queue_config"])
        
        self.assertEqual(queue_manager.config.queue_size, 100)
        self.assertEqual(queue_manager.config.warning_threshold, 0.8)
        self.assertEqual(queue_manager.config.batch_size, 10)


def run_comprehensive_tests():
    """运行全面测试"""
    print("=" * 60)
    print("队列管理器全面测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加所有测试类
    test_classes = [
        TestQueueConfig,
        TestQueueManager,
        TestQueueManagerFromConfig
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"运行测试数量: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n测试通过率: {success_rate:.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
