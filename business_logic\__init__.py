"""
业务逻辑层 - 数据采集系统

该层负责协议流程控制和业务逻辑管理，包含以下核心组件：

1. ProtocolFlowController - 协议流程控制器
   - 组合串口通信和协议解析功能
   - 基于JSON配置的动态协议流程执行
   - 三种工作模式的统一管理
   - 业务流程状态管理

2. CommandExecutor - 指令执行管理器
   - 异步指令执行和超时重试机制
   - 指令队列管理和优先级处理
   - 指令执行结果的统计和监控

3. ErrorHandler - 错误处理器
   - 分级错误处理和自动恢复策略
   - 连续错误计数和暂停机制
   - 错误类型分类和统计分析

4. LoggerManager - 日志管理器
   - 结构化日志记录和性能监控
   - 日志轮转和存储管理
   - 日志级别控制和过滤

核心特性：
- 动态配置驱动：所有业务逻辑完全由JSON配置决定
- 协议无关：完全不依赖具体协议实现
- 状态管理：完整的业务流程状态跟踪
- 串口集成：协调串口通信和数据处理

作者: DataStudio开发团队
创建时间: 2025-08-08
"""

from .protocol_flow_controller import (
    ProtocolFlowController,
    WorkMode,
    FlowState,
    FlowResult,
    ContinuousDataResult
)

from .command_executor import (
    CommandExecutor,
    CommandResult,
    CommandRequest
)

from .error_handler import (
    ErrorHandler,
    ErrorType,
    ErrorSeverity,
    RecoveryAction,
    ErrorRecord,
    ErrorThreshold
)

from .logger_manager import (
    LoggerManager,
    LogLevel,
    LogFormat,
    LogEntry,
    PerformanceMetric
)

__version__ = "1.0.0"
__author__ = "DataStudio开发团队"

__all__ = [
    # 协议流程控制器
    'ProtocolFlowController',
    'WorkMode',
    'FlowState', 
    'FlowResult',
    'ContinuousDataResult',
    
    # 指令执行管理器
    'CommandExecutor',
    'CommandResult',
    'CommandRequest',
    
    # 错误处理器
    'ErrorHandler',
    'ErrorType',
    'ErrorSeverity',
    'RecoveryAction',
    'ErrorRecord',
    'ErrorThreshold',
    
    # 日志管理器
    'LoggerManager',
    'LogLevel',
    'LogFormat',
    'LogEntry',
    'PerformanceMetric'
]
