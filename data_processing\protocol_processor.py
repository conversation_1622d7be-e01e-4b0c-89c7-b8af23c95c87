"""
完整协议处理器 - 基于JSON配置的自由串口协议处理引擎

该模块实现了完整的自由串口协议处理功能，支持：
- 协议配置管理
- 指令发送和应答验证
- 连续数据接收和解析
- 协议流程管理
- 统一的协议处理接口

核心特性：
- 动态配置驱动：所有协议逻辑完全由JSON配置决定，零硬编码
- 完整协议支持：支持指令、应答、连续数据的完整处理
- 协议无关：完全不依赖具体协议实现
- 高性能处理：优化的数据处理和队列管理
- 异常处理完善：完整的错误恢复和用户友好的错误信息

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import logging
import time
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum

from utils.serial_config_manager import SerialConfigManager
from utils.helper_utils import hex_to_bytes, bytes_to_hex
from utils.exceptions import DataProcessingError

from .frame_detector import FrameDetector, FrameDetectionConfig
from .data_parser import DataParser, ParsedData
from .response_validator import ResponseValidator, ValidationConfig, ValidationResult
from .queue_manager import QueueManager, QueueConfig


class ProcessingMode(Enum):
    """处理模式枚举"""
    IDLE = "idle"                    # 空闲状态
    COMMAND_PROCESSING = "command"   # 指令处理模式
    CONTINUOUS_DATA = "continuous"   # 连续数据模式


@dataclass
class CommandResult:
    """指令执行结果"""
    command_id: str          # 指令ID
    command_name: str        # 指令名称
    send_data: bytes         # 发送的数据
    response_data: bytes     # 接收的应答数据
    validation_result: ValidationResult  # 验证结果
    execution_time: float    # 执行耗时
    success: bool           # 是否成功


@dataclass
class ContinuousDataResult:
    """连续数据处理结果"""
    frame_data: bytes           # 原始帧数据
    parsed_fields: List[ParsedData]  # 解析后的字段数据
    timestamp: float            # 时间戳
    frame_number: int          # 帧序号


class ProtocolProcessor:
    """
    完整协议处理器
    
    核心功能：
    1. 协议配置管理
    2. 指令发送和应答验证
    3. 连续数据接收和解析
    4. 协议流程管理
    5. 统一的处理接口
    """
    
    def __init__(self, config_path: str):
        """
        初始化协议处理器
        
        Args:
            config_path: 协议配置文件路径
        """
        self.logger = logging.getLogger(f"{__name__}.ProtocolProcessor")
        
        # 加载协议配置
        self.config_manager = SerialConfigManager()
        self.protocol_config = self.config_manager.load_config(config_path)
        
        # 初始化组件
        self._initialize_components()
        
        # 状态管理
        self.current_mode = ProcessingMode.IDLE
        self.frame_counter = 0
        
        # 统计信息
        self.stats = {
            "commands_executed": 0,
            "commands_successful": 0,
            "commands_failed": 0,
            "frames_processed": 0,
            "fields_parsed": 0,
            "total_processing_time": 0.0
        }
        
        self.logger.info(f"协议处理器初始化完成 - 协议: {self.protocol_config.protocol_info.name}")
    
    def _initialize_components(self):
        """初始化所有组件"""
        # 初始化应答验证器
        self.response_validator = ResponseValidator()
        
        # 初始化连续数据处理组件（如果配置存在）
        if self.protocol_config.continuous_data:
            # 初始化帧检测器
            frame_config = FrameDetectionConfig.from_json_config({
                "header": self.protocol_config.continuous_data.frame_detection.header,
                "tail": self.protocol_config.continuous_data.frame_detection.tail,
                "min_length": self.protocol_config.continuous_data.frame_detection.min_length,
                "max_length": self.protocol_config.continuous_data.frame_detection.max_length
            })
            self.frame_detector = FrameDetector(frame_config)
            
            # 初始化数据解析器
            parsing_config = []
            for field in self.protocol_config.continuous_data.data_parsing:
                parsing_config.append({
                    "name": field.name,
                    "offset": field.offset,
                    "length": field.length,
                    "data_type": field.data_type,
                    "endian": field.endian,
                    "scale_factor": field.scale_factor,
                    "unit": field.unit
                })
            self.data_parser = DataParser(parsing_config)
            
            # 初始化队列管理器
            queue_config = QueueConfig(
                queue_size=1000,
                warning_threshold=0.8,
                batch_size=50
            )
            self.queue_manager = QueueManager(queue_config)
        else:
            self.frame_detector = None
            self.data_parser = None
            self.queue_manager = None
    
    def execute_command(self, command_id: str, send_callback=None, receive_callback=None) -> CommandResult:
        """
        执行单个指令
        
        Args:
            command_id: 指令ID
            send_callback: 发送数据回调函数 (data: bytes) -> bool
            receive_callback: 接收数据回调函数 () -> bytes
            
        Returns:
            指令执行结果
        """
        start_time = time.time()
        
        # 查找指令
        command = self.config_manager.get_command_by_id(command_id)
        if not command:
            raise DataProcessingError(
                f"指令不存在: {command_id}",
                error_code="COMMAND_NOT_FOUND",
                details={"command_id": command_id}
            )
        
        try:
            # 准备发送数据
            send_data = hex_to_bytes(command.send)
            
            # 发送指令
            if send_callback:
                send_success = send_callback(send_data)
                if not send_success:
                    raise DataProcessingError(f"指令发送失败: {command_id}")
            
            # 接收应答
            response_data = b''
            if receive_callback:
                response_data = receive_callback()
            
            # 验证应答
            validation_config = ValidationConfig(
                validation_type=command.response_validation.type,
                pattern=command.response_validation.pattern,
                timeout=command.response_validation.timeout,
                retry_count=command.response_validation.retry_count
            )
            
            validation_result = self.response_validator.validate_response(response_data, validation_config)
            
            # 计算执行时间
            execution_time = time.time() - start_time
            
            # 更新统计信息
            self.stats["commands_executed"] += 1
            self.stats["total_processing_time"] += execution_time
            
            if validation_result.matched:
                self.stats["commands_successful"] += 1
                success = True
            else:
                self.stats["commands_failed"] += 1
                success = False
            
            result = CommandResult(
                command_id=command.id,
                command_name=command.name,
                send_data=send_data,
                response_data=response_data,
                validation_result=validation_result,
                execution_time=execution_time,
                success=success
            )
            
            self.logger.info(f"指令执行完成: {command_id}, 成功: {success}, 耗时: {execution_time:.3f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.stats["commands_executed"] += 1
            self.stats["commands_failed"] += 1
            self.stats["total_processing_time"] += execution_time
            
            self.logger.error(f"指令执行失败: {command_id}, 错误: {str(e)}")
            raise DataProcessingError(
                f"指令执行失败: {command_id}, 错误: {str(e)}",
                error_code="COMMAND_EXECUTION_ERROR",
                details={"command_id": command_id, "error": str(e)}
            )
    
    def process_continuous_data(self, raw_data: bytes) -> List[ContinuousDataResult]:
        """
        处理连续数据
        
        Args:
            raw_data: 原始数据
            
        Returns:
            连续数据处理结果列表
        """
        if not self.frame_detector or not self.data_parser:
            raise DataProcessingError(
                "连续数据处理组件未初始化，请检查协议配置",
                error_code="CONTINUOUS_DATA_NOT_CONFIGURED"
            )
        
        results = []
        
        try:
            # 帧检测
            frames = self.frame_detector.process_data(raw_data)
            
            # 处理每个检测到的帧
            for frame_data in frames:
                self.frame_counter += 1
                
                # 数据解析
                parsed_fields = self.data_parser.parse_frame(frame_data)
                
                # 创建结果
                result = ContinuousDataResult(
                    frame_data=frame_data,
                    parsed_fields=parsed_fields,
                    timestamp=time.time(),
                    frame_number=self.frame_counter
                )
                
                results.append(result)
                
                # 将结果放入队列
                if self.queue_manager:
                    self.queue_manager.put(result, "continuous_data")
                
                # 更新统计信息
                self.stats["frames_processed"] += 1
                self.stats["fields_parsed"] += len(parsed_fields)
            
            return results
            
        except Exception as e:
            self.logger.error(f"连续数据处理失败: {str(e)}")
            raise DataProcessingError(
                f"连续数据处理失败: {str(e)}",
                error_code="CONTINUOUS_DATA_PROCESSING_ERROR",
                details={"error": str(e)}
            )
    
    def execute_protocol_flow(self, step_name: str = None, send_callback=None, receive_callback=None) -> List[CommandResult]:
        """
        执行协议流程
        
        Args:
            step_name: 步骤名称，None表示执行所有步骤
            send_callback: 发送数据回调函数
            receive_callback: 接收数据回调函数
            
        Returns:
            指令执行结果列表
        """
        results = []
        
        # 获取要执行的步骤
        steps_to_execute = []
        if step_name:
            # 执行指定步骤
            for step in self.protocol_config.protocol_flow.steps:
                if step.name == step_name:
                    steps_to_execute.append(step)
                    break
        else:
            # 执行所有步骤
            steps_to_execute = self.protocol_config.protocol_flow.steps
        
        # 执行步骤
        for step in steps_to_execute:
            self.logger.info(f"执行协议步骤: {step.name}")
            
            for command_id in step.commands:
                try:
                    result = self.execute_command(command_id, send_callback, receive_callback)
                    results.append(result)
                    
                    if not result.success:
                        self.logger.warning(f"步骤 {step.name} 中的指令 {command_id} 执行失败")
                        
                except Exception as e:
                    self.logger.error(f"步骤 {step.name} 中的指令 {command_id} 执行异常: {str(e)}")
                    # 继续执行其他指令
                    continue
        
        return results
    
    def get_protocol_info(self) -> Dict[str, Any]:
        """获取协议信息"""
        return {
            "name": self.protocol_config.protocol_info.name,
            "description": self.protocol_config.protocol_info.description,
            "version": self.protocol_config.protocol_info.version,
            "serial_config": {
                "port": self.protocol_config.serial_config.port,
                "baudrate": self.protocol_config.serial_config.baudrate,
                "databits": self.protocol_config.serial_config.databits,
                "parity": self.protocol_config.serial_config.parity,
                "stopbits": self.protocol_config.serial_config.stopbits,
                "timeout": self.protocol_config.serial_config.timeout
            },
            "single_commands": len(self.protocol_config.single_commands),
            "continuous_commands": len(self.protocol_config.continuous_commands),
            "protocol_steps": len(self.protocol_config.protocol_flow.steps),
            "continuous_data_configured": self.protocol_config.continuous_data is not None
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 添加组件统计信息
        if self.frame_detector:
            stats["frame_detector_stats"] = self.frame_detector.get_statistics()
        if self.data_parser:
            stats["data_parser_stats"] = self.data_parser.get_statistics()
        if self.queue_manager:
            stats["queue_manager_stats"] = self.queue_manager.get_statistics()
        
        stats["response_validator_stats"] = self.response_validator.get_statistics()
        
        # 计算成功率
        if stats["commands_executed"] > 0:
            stats["command_success_rate"] = stats["commands_successful"] / stats["commands_executed"] * 100
        else:
            stats["command_success_rate"] = 0.0
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            "commands_executed": 0,
            "commands_successful": 0,
            "commands_failed": 0,
            "frames_processed": 0,
            "fields_parsed": 0,
            "total_processing_time": 0.0
        }
        
        # 重置组件统计信息
        if self.frame_detector:
            self.frame_detector.reset_statistics()
        if self.data_parser:
            self.data_parser.reset_statistics()
        if self.queue_manager:
            self.queue_manager.reset_statistics()
        
        self.response_validator.reset_statistics()
        self.frame_counter = 0
        
        self.logger.info("统计信息已重置")
    
    def get_queued_data(self, max_items: int = None) -> List[ContinuousDataResult]:
        """获取队列中的连续数据"""
        if not self.queue_manager:
            return []
        
        items = self.queue_manager.get_batch(max_items)
        return [item.data for item in items if item.item_type == "continuous_data"]
