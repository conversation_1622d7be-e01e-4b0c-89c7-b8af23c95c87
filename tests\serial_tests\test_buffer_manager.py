#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓冲区管理器测试
测试BufferManager的所有核心功能，包括读写、线程安全、状态管理等

作者: LD (Lead Developer)
创建时间: 2025-08-07
版本: 1.0
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logging_config import LoggingConfig
from communication.buffer_manager import BufferManager, BufferState
from utils.exceptions import BufferOverflowError, BufferUnderflowError


def setup_logging():
    """设置日志系统"""
    logging_config = LoggingConfig()
    logging_config.setup_logging()
    print("日志系统初始化完成")


def test_buffer_initialization():
    """测试缓冲区初始化"""
    print("\n=== 测试缓冲区初始化 ===")
    
    # 测试默认参数
    buffer = BufferManager()
    assert buffer.available_data() == 0
    assert buffer.available_space() == 4096
    assert buffer.is_empty()
    assert not buffer.is_full()
    assert buffer.get_state() == BufferState.EMPTY
    
    # 测试自定义参数
    buffer2 = BufferManager(size=1024, warning_threshold=0.7, name="TestBuffer")
    assert buffer2.available_space() == 1024
    
    print("✅ 缓冲区初始化测试通过")


def test_basic_write_read():
    """测试基本读写功能"""
    print("\n=== 测试基本读写功能 ===")

    buffer = BufferManager(size=512, name="BasicTest")
    
    # 测试写入
    test_data = b"Hello Buffer"
    bytes_written = buffer.write(test_data)
    assert bytes_written == len(test_data)
    assert buffer.available_data() == len(test_data)
    assert not buffer.is_empty()
    
    # 测试读取
    read_data = buffer.read(len(test_data))
    assert read_data == test_data
    assert buffer.is_empty()
    assert buffer.available_data() == 0
    
    print("✅ 基本读写功能测试通过")


def test_circular_buffer():
    """测试循环缓冲区功能"""
    print("\n=== 测试循环缓冲区功能 ===")

    buffer = BufferManager(size=256, name="CircularTest")
    
    # 填满缓冲区
    data1 = b"A" * 256  # 256字节数据
    buffer.write(data1)
    assert buffer.is_full()

    # 读取部分数据
    partial_data = buffer.read(100)
    assert len(partial_data) == 100
    assert buffer.available_data() == 156

    # 写入新数据（测试环绕）
    data2 = b"B" * 100
    buffer.write(data2)
    assert buffer.is_full()

    # 读取所有数据
    remaining_data = buffer.read(256)
    assert len(remaining_data) == 256
    assert buffer.is_empty()
    
    print("✅ 循环缓冲区功能测试通过")


def test_peek_functionality():
    """测试预览功能"""
    print("\n=== 测试预览功能 ===")

    buffer = BufferManager(size=512, name="PeekTest")
    
    # 写入测试数据
    test_data = b"Preview Test Data"
    buffer.write(test_data)
    
    # 测试预览
    peeked_data = buffer.peek(7)
    assert peeked_data == b"Preview"
    assert buffer.available_data() == len(test_data)  # 数据应该还在
    
    # 预览全部数据
    all_peeked = buffer.peek(len(test_data))
    assert all_peeked == test_data
    
    # 预览超出范围
    over_peeked = buffer.peek(100)
    assert over_peeked == test_data
    
    # 读取数据验证预览没有影响
    read_data = buffer.read(len(test_data))
    assert read_data == test_data
    
    print("✅ 预览功能测试通过")


def test_buffer_states():
    """测试缓冲区状态"""
    print("\n=== 测试缓冲区状态 ===")

    buffer = BufferManager(size=256, warning_threshold=0.7, name="StateTest")
    
    # 空状态
    assert buffer.get_state() == BufferState.EMPTY
    
    # 正常状态
    buffer.write(b"A" * 100)  # 100字节，约39%使用率
    assert buffer.get_state() == BufferState.NORMAL

    # 警告状态
    buffer.write(b"B" * 80)  # 总共180字节，约70%使用率
    assert buffer.get_state() == BufferState.WARNING

    # 满状态
    buffer.write(b"C" * 76)  # 总共256字节，100%使用率
    assert buffer.get_state() == BufferState.FULL
    assert buffer.is_full()
    
    print("✅ 缓冲区状态测试通过")


def test_overflow_handling():
    """测试溢出处理"""
    print("\n=== 测试溢出处理 ===")

    buffer = BufferManager(size=256, name="OverflowTest")
    
    # 填满缓冲区
    buffer.write(b"A" * 256)
    assert buffer.is_full()

    # 测试非阻塞溢出
    try:
        buffer.write(b"X", block=False)
        assert False, "应该抛出溢出异常"
    except BufferOverflowError:
        print("✅ 正确捕获溢出异常")

    # 测试阻塞写入（超时）
    start_time = time.time()
    try:
        buffer.write(b"Y", block=True, timeout=0.1)
        assert False, "应该超时"
    except BufferOverflowError:
        elapsed = time.time() - start_time
        assert elapsed >= 0.1, "应该等待超时时间"
        print("✅ 阻塞写入超时测试通过")
    
    print("✅ 溢出处理测试通过")


def test_thread_safety():
    """测试线程安全"""
    print("\n=== 测试线程安全 ===")
    
    buffer = BufferManager(size=1000, name="ThreadTest")
    results = {"write_count": 0, "read_count": 0, "errors": []}
    
    def writer_thread():
        """写入线程"""
        try:
            for i in range(50):
                data = f"data{i:02d}".encode()
                buffer.write(data)
                results["write_count"] += 1
                time.sleep(0.001)  # 短暂延迟
        except Exception as e:
            results["errors"].append(f"Writer error: {e}")
    
    def reader_thread():
        """读取线程"""
        try:
            while results["read_count"] < 50:
                if not buffer.is_empty():
                    data = buffer.read(6)  # 读取6字节
                    if data:
                        results["read_count"] += 1
                time.sleep(0.001)  # 短暂延迟
        except Exception as e:
            results["errors"].append(f"Reader error: {e}")
    
    # 启动线程
    writer = threading.Thread(target=writer_thread)
    reader = threading.Thread(target=reader_thread)
    
    writer.start()
    reader.start()
    
    # 等待完成
    writer.join(timeout=5)
    reader.join(timeout=5)
    
    # 验证结果
    assert len(results["errors"]) == 0, f"线程错误: {results['errors']}"
    assert results["write_count"] == 50, f"写入次数不正确: {results['write_count']}"
    assert results["read_count"] == 50, f"读取次数不正确: {results['read_count']}"
    
    print("✅ 线程安全测试通过")


def test_statistics():
    """测试统计功能"""
    print("\n=== 测试统计功能 ===")

    buffer = BufferManager(size=512, name="StatsTest")
    
    # 进行一些操作
    buffer.write(b"test1")
    buffer.write(b"test2")
    buffer.read(5)
    
    # 检查统计信息
    stats = buffer.get_stats()
    assert stats['total_written'] == 10
    assert stats['total_read'] == 5
    assert stats['write_operations'] == 2
    assert stats['read_operations'] == 1
    assert stats['current_size'] == 5
    
    # 重置统计
    buffer.reset_stats()
    stats_after_reset = buffer.get_stats()
    assert stats_after_reset['total_written'] == 0
    assert stats_after_reset['total_read'] == 0
    
    print("✅ 统计功能测试通过")


def test_clear_functionality():
    """测试清空功能"""
    print("\n=== 测试清空功能 ===")

    buffer = BufferManager(size=512, name="ClearTest")
    
    # 写入一些数据
    buffer.write(b"some data to clear")
    assert not buffer.is_empty()
    
    # 清空缓冲区
    buffer.clear()
    assert buffer.is_empty()
    assert buffer.available_data() == 0
    assert buffer.available_space() == 512
    assert buffer.get_state() == BufferState.EMPTY
    
    print("✅ 清空功能测试通过")


def run_all_tests():
    """运行所有测试"""
    print("开始缓冲区管理器测试")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    
    # 运行测试
    test_functions = [
        test_buffer_initialization,
        test_basic_write_read,
        test_circular_buffer,
        test_peek_functionality,
        test_buffer_states,
        test_overflow_handling,
        test_thread_safety,
        test_statistics,
        test_clear_functionality
    ]
    
    passed = 0
    failed = 0
    
    for test_func in test_functions:
        try:
            test_func()
            passed += 1
        except Exception as e:
            print(f"❌ 测试失败: {test_func.__name__} - {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败")


if __name__ == "__main__":
    run_all_tests()
