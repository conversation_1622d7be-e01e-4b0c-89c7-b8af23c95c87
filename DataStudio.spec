# -*- mode: python ; coding: utf-8 -*-
"""
DataStudio PyInstaller 配置文件
用于打包DataStudio数据采集系统

作者: AI Assistant
创建时间: 2025-08-08
"""

import os
import sys
from pathlib import Path

# 项目根目录
project_root = Path.cwd()
sys.path.insert(0, str(project_root))

# 分析主程序
a = Analysis(
    ['main_cli.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=[
        # 配置文件目录
        ('config', 'config'),
        # 文档目录（可选）
        ('docs', 'docs'),
    ],
    hiddenimports=[
        # 核心业务逻辑模块
        'business_logic',
        'business_logic.protocol_flow_controller',
        'business_logic.command_executor', 
        'business_logic.error_handler',
        'business_logic.logger_manager',
        
        # 通信层模块
        'communication',
        'communication.serial_manager',
        'communication.buffer_manager',
        'communication.connection_pool',
        
        # 数据处理层模块
        'data_processing',
        'data_processing.frame_detector',
        'data_processing.data_parser',
        'data_processing.response_validator',
        'data_processing.queue_manager',
        'data_processing.protocol_processor',
        
        # 工具模块
        'utils',
        'utils.serial_config_manager',
        'utils.system_config_manager',
        'utils.helper_utils',
        'utils.exceptions',
        'utils.validators',
        'utils.constants',
        'utils.logging_config',
        
        # 用户界面模块
        'user_interface',
        
        # 第三方库
        'serial',
        'serial.tools',
        'serial.tools.list_ports',
        'jsonschema',
        'jsonschema.validators',
        'jsonschema.exceptions',
        
        # Python标准库
        'threading',
        'queue',
        'json',
        'struct',
        'logging',
        'logging.handlers',
        'pathlib',
        'dataclasses',
        'enum',
        'typing',
        're',
        'time',
        'signal',
        'sys',
        'os',
        'platform',
        'importlib',
        'importlib.metadata',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除测试模块
        'tests',
        'pytest',
        'pytest_cov',
        # 排除开发工具
        'black',
        'flake8', 
        'mypy',
        # 排除不需要的GUI库
        'tkinter',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
        # 排除不需要的科学计算库
        'numpy',
        'pandas',
        'matplotlib',
        'scipy',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 生成PYZ文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 生成可执行文件
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='DataStudio',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)

# 收集所有文件到dist目录
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='DataStudio'
)
