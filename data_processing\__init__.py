#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理层 (Data Layer)
动态处理JSON配置的自由串口协议解析

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

__version__ = "1.0.0"
__author__ = "LD (Lead Developer)"

# 导出主要组件（延迟导入以避免循环导入）
__all__ = [
    'FrameDetector',
    'FrameDetectionConfig',
    'DataParser',
    'ResponseValidator',
    'ValidationConfig',
    'QueueManager',
    'QueueConfig',
    'ProtocolProcessor'
]
