#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 system_config_manager.py 模块
验证系统配置管理器的功能

作者: LD (Lead Developer)
创建时间: 2025-08-06
版本: 1.0
"""

import os
import sys
import json
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.system_config_manager import (
    SystemConfigManager, get_system_config_manager,
    ErrorHandlingConfig, QueueConfig, PerformanceConfig, 
    LoggingConfig, MonitoringConfig, SystemConfig
)
from utils.exceptions import ConfigFileNotFoundError, ConfigParsingError


def test_singleton_pattern():
    """测试单例模式"""
    print("=" * 60)
    print("测试单例模式")
    print("=" * 60)
    
    try:
        # 获取多个实例
        manager1 = SystemConfigManager()
        manager2 = SystemConfigManager()
        manager3 = get_system_config_manager()
        
        # 验证是否为同一实例
        if manager1 is manager2 is manager3:
            print("✅ 单例模式工作正常")
            return True
        else:
            print("❌ 单例模式失败")
            return False
            
    except Exception as e:
        print(f"❌ 单例模式测试失败: {e}")
        return False


def test_default_config():
    """测试默认配置"""
    print("\n" + "=" * 60)
    print("测试默认配置")
    print("=" * 60)
    
    try:
        manager = get_system_config_manager()
        config = manager.get_config()
        
        if config is None:
            print("❌ 默认配置未加载")
            return False
        
        print("✅ 默认配置加载成功")
        print(f"   - 错误处理配置: {config.error_handling}")
        print(f"   - 队列配置: {config.queue_config}")
        print(f"   - 性能配置: {config.performance}")
        print(f"   - 日志配置: {config.logging}")
        print(f"   - 监控配置: {config.monitoring}")
        
        # 验证默认值
        if config.error_handling.max_consecutive_errors == 5:
            print("✅ 错误处理默认配置正确")
        else:
            print("❌ 错误处理默认配置不正确")
            return False
        
        if config.queue_config.response_queue_size == 100:
            print("✅ 队列默认配置正确")
        else:
            print("❌ 队列默认配置不正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 默认配置测试失败: {e}")
        return False


def test_load_config_from_file():
    """测试从文件加载配置"""
    print("\n" + "=" * 60)
    print("测试从文件加载配置")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # 创建测试配置文件
            test_config = {
                "error_handling": {
                    "continuous_mode": {
                        "max_consecutive_errors": 10,
                        "pause_on_max_errors": False,
                        "recovery_strategy": "auto_retry",
                        "error_types": {
                            "frame_error": {"weight": 2},
                            "parse_error": {"weight": 3},
                            "comm_error": {"weight": 4}
                        }
                    }
                },
                "queue_config": {
                    "response_queue_size": 200,
                    "queue_warning_threshold": 0.9,
                    "batch_processing_size": 20
                },
                "performance": {
                    "buffer_size": 8192,
                    "processing_timeout": 0.2,
                    "max_processing_threads": 4
                },
                "logging": {
                    "level": "DEBUG",
                    "file_max_size": "20MB",
                    "file_backup_count": 50,
                    "format": "[%(asctime)s] [%(levelname)s] - %(message)s"
                },
                "monitoring": {
                    "enable_performance_monitoring": False,
                    "monitoring_interval": 2.0,
                    "alert_thresholds": {
                        "cpu_usage": 90,
                        "memory_usage": 90,
                        "queue_usage": 95
                    }
                }
            }
            
            config_file = Path(temp_dir) / "test_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(test_config, f, indent=2, ensure_ascii=False)
            
            # 加载配置
            manager = get_system_config_manager()
            loaded_config = manager.load_config(str(config_file))
            
            print("✅ 配置文件加载成功")
            
            # 验证加载的配置
            if loaded_config.error_handling.max_consecutive_errors == 10:
                print("✅ 错误处理配置加载正确")
            else:
                print("❌ 错误处理配置加载不正确")
                return False
            
            if loaded_config.queue_config.response_queue_size == 200:
                print("✅ 队列配置加载正确")
            else:
                print("❌ 队列配置加载不正确")
                return False
            
            if loaded_config.performance.buffer_size == 8192:
                print("✅ 性能配置加载正确")
            else:
                print("❌ 性能配置加载不正确")
                return False
            
            if loaded_config.logging.level == "DEBUG":
                print("✅ 日志配置加载正确")
            else:
                print("❌ 日志配置加载不正确")
                return False
            
            if not loaded_config.monitoring.enable_performance_monitoring:
                print("✅ 监控配置加载正确")
            else:
                print("❌ 监控配置加载不正确")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 配置文件加载测试失败: {e}")
            return False


def test_config_file_not_found():
    """测试配置文件不存在的情况"""
    print("\n" + "=" * 60)
    print("测试配置文件不存在的情况")
    print("=" * 60)
    
    try:
        manager = get_system_config_manager()
        
        # 尝试加载不存在的文件
        try:
            manager.load_config("non_existent_file.json")
            print("❌ 应该抛出ConfigFileNotFoundError异常")
            return False
        except ConfigFileNotFoundError:
            print("✅ 正确抛出ConfigFileNotFoundError异常")
            return True
        except Exception as e:
            print(f"❌ 抛出了错误的异常类型: {type(e).__name__}")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件不存在测试失败: {e}")
        return False


def test_invalid_json_config():
    """测试无效JSON配置文件"""
    print("\n" + "=" * 60)
    print("测试无效JSON配置文件")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # 创建无效的JSON文件
            invalid_config_file = Path(temp_dir) / "invalid_config.json"
            with open(invalid_config_file, 'w', encoding='utf-8') as f:
                f.write('{"invalid": json, "syntax"}')  # 无效的JSON语法
            
            manager = get_system_config_manager()
            
            # 尝试加载无效的JSON文件
            try:
                manager.load_config(str(invalid_config_file))
                print("❌ 应该抛出ConfigParsingError异常")
                return False
            except ConfigParsingError:
                print("✅ 正确抛出ConfigParsingError异常")
                return True
            except Exception as e:
                print(f"❌ 抛出了错误的异常类型: {type(e).__name__}")
                return False
                
        except Exception as e:
            print(f"❌ 无效JSON配置测试失败: {e}")
            return False


def test_config_getters():
    """测试配置获取方法"""
    print("\n" + "=" * 60)
    print("测试配置获取方法")
    print("=" * 60)
    
    try:
        manager = get_system_config_manager()
        
        # 测试各种获取方法
        error_config = manager.get_error_handling_config()
        queue_config = manager.get_queue_config()
        performance_config = manager.get_performance_config()
        logging_config = manager.get_logging_config()
        monitoring_config = manager.get_monitoring_config()
        
        print("✅ 所有配置获取方法正常工作")
        print(f"   - 错误处理配置类型: {type(error_config).__name__}")
        print(f"   - 队列配置类型: {type(queue_config).__name__}")
        print(f"   - 性能配置类型: {type(performance_config).__name__}")
        print(f"   - 日志配置类型: {type(logging_config).__name__}")
        print(f"   - 监控配置类型: {type(monitoring_config).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置获取方法测试失败: {e}")
        return False


def test_create_config_template():
    """测试创建配置模板"""
    print("\n" + "=" * 60)
    print("测试创建配置模板")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            manager = get_system_config_manager()
            template_file = Path(temp_dir) / "template_config.json"
            
            # 创建配置模板
            manager.create_default_config_template(str(template_file))
            
            if template_file.exists():
                print("✅ 配置模板文件创建成功")
                
                # 验证模板文件内容
                with open(template_file, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)
                
                # 检查必要的字段
                required_fields = ['error_handling', 'queue_config', 'performance', 'logging', 'monitoring']
                for field in required_fields:
                    if field in template_data:
                        print(f"✅ 模板包含 {field} 字段")
                    else:
                        print(f"❌ 模板缺少 {field} 字段")
                        return False
                
                return True
            else:
                print("❌ 配置模板文件未创建")
                return False
                
        except Exception as e:
            print(f"❌ 创建配置模板测试失败: {e}")
            return False


def main():
    """主测试函数"""
    print("开始测试 system_config_manager.py 模块")
    print("=" * 80)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("单例模式", test_singleton_pattern()))
    test_results.append(("默认配置", test_default_config()))
    test_results.append(("从文件加载配置", test_load_config_from_file()))
    test_results.append(("配置文件不存在", test_config_file_not_found()))
    test_results.append(("无效JSON配置", test_invalid_json_config()))
    test_results.append(("配置获取方法", test_config_getters()))
    test_results.append(("创建配置模板", test_create_config_template()))
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {len(test_results)} 个测试")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    
    if failed == 0:
        print("\n🎉 所有测试通过！")
        return True
    else:
        print(f"\n⚠️  有 {failed} 个测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
