#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的异常信息功能
验证ConfigValidationError的字段路径信息显示

作者: LD (Lead Developer)
创建时间: 2025-08-06
版本: 1.0
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.exceptions import ConfigValidationError


def test_enhanced_exception_info():
    """测试增强的异常信息"""
    print("=" * 60)
    print("测试增强的异常信息功能")
    print("=" * 60)
    
    try:
        # 测试包含字段路径的异常
        try:
            raise ConfigValidationError(
                "配置验证失败: 测试错误",
                field_path="user.settings.database",
                validation_errors=["数据库连接配置无效"]
            )
        except ConfigValidationError as e:
            exception_str = str(e)
            print(f"✅ 异常信息: {exception_str}")
            
            if "user.settings.database" in exception_str:
                print("✅ 异常信息包含字段路径")
            else:
                print("❌ 异常信息不包含字段路径")
                return False
        
        # 测试不包含字段路径的异常
        try:
            raise ConfigValidationError(
                "配置验证失败: 通用错误",
                field_path=None,
                validation_errors=["通用验证错误"]
            )
        except ConfigValidationError as e:
            exception_str = str(e)
            print(f"✅ 无字段路径异常信息: {exception_str}")
            
            if "(字段:" not in exception_str:
                print("✅ 无字段路径时不显示字段信息")
            else:
                print("❌ 无字段路径时仍显示字段信息")
                return False
        
        # 测试空字段路径的异常
        try:
            raise ConfigValidationError(
                "配置验证失败: 空字段路径",
                field_path="",
                validation_errors=["空字段路径错误"]
            )
        except ConfigValidationError as e:
            exception_str = str(e)
            print(f"✅ 空字段路径异常信息: {exception_str}")
            
            if "(字段:" not in exception_str:
                print("✅ 空字段路径时不显示字段信息")
            else:
                print("❌ 空字段路径时仍显示字段信息")
                return False
        
        print("\n✅ 所有增强异常信息测试通过")
        print("✅ 异常信息现在更加完整和有用")
        return True
        
    except Exception as e:
        print(f"❌ 增强异常信息测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试增强的异常信息功能")
    print("=" * 80)
    
    success = test_enhanced_exception_info()
    
    print("\n" + "=" * 80)
    print("测试结果")
    print("=" * 80)
    
    if success:
        print("🎉 增强异常信息功能测试通过！")
        print("✅ ConfigValidationError现在提供更完整的错误信息")
        return True
    else:
        print("❌ 增强异常信息功能测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
