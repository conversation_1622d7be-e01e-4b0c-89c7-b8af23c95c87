# 通信抽象层数据读写时序图

## 概述

本文档详细描述了通信抽象层中数据读写的完整时序流程，包括连接建立、数据传输、缓冲区处理、健康检查和资源清理等各个阶段。

## 完整时序图

```mermaid
sequenceDiagram
    participant App as 用户应用
    participant Pool as ConnectionPool
    participant SM as SerialManager
    participant BM as BufferManager
    participant H<PERSON> as 物理串口
    participant CB as 状态回调
    
    Note over App,CB: 1. 连接建立阶段
    App->>Pool: 1.1 get_connection(config)
    Pool->>Pool: 1.2 检查连接池
    alt 连接不存在
        Pool->>SM: 1.3 创建新SerialManager
        SM->>SM: 1.4 初始化状态和统计
        Pool->>SM: 1.5 connect()
        SM->>HW: 1.6 打开串口连接
        HW-->>SM: 1.7 连接成功/失败
        SM->>SM: 1.8 更新连接状态
        SM->>CB: 1.9 触发状态回调
        CB-->>Pool: 1.10 状态变化通知
        SM-->>Pool: 1.11 返回连接结果
        Pool->>Pool: 1.12 添加到连接池
    else 连接已存在
        Pool->>Pool: 1.13 复用现有连接
        Pool->>Pool: 1.14 更新使用统计
    end
    Pool-->>App: 1.15 返回SerialManager
    
    Note over App,CB: 2. 数据写入阶段
    App->>SM: 2.1 write_data(data)
    SM->>SM: 2.2 检查连接状态
    alt 连接正常
        SM->>HW: 2.3 写入数据到串口
        HW-->>SM: 2.4 返回写入字节数
        SM->>SM: 2.5 更新发送统计
        SM-->>App: 2.6 返回写入结果
    else 连接异常
        SM->>SM: 2.7 标记连接错误
        SM->>CB: 2.8 触发错误回调
        SM-->>App: 2.9 抛出异常
    end
    
    Note over App,CB: 3. 数据接收阶段
    HW->>SM: 3.1 接收到数据
    SM->>SM: 3.2 更新接收统计
    App->>SM: 3.3 read_available_data()
    SM->>HW: 3.4 读取可用数据
    HW-->>SM: 3.5 返回数据
    SM-->>App: 3.6 返回接收数据
    
    Note over App,CB: 4. 缓冲区处理阶段
    App->>BM: 4.1 write(received_data)
    BM->>BM: 4.2 检查缓冲区空间
    alt 空间充足
        BM->>BM: 4.3 写入循环缓冲区
        BM->>BM: 4.4 更新写指针和数据大小
        BM->>BM: 4.5 更新缓冲区状态
        BM->>BM: 4.6 通知等待线程
        BM-->>App: 4.7 返回写入字节数
    else 空间不足
        alt 阻塞模式
            BM->>BM: 4.8 等待空间释放
            BM->>BM: 4.9 重试写入
        else 非阻塞模式
            BM-->>App: 4.10 抛出溢出异常
        end
    end
    
    App->>BM: 4.11 read(size)
    BM->>BM: 4.12 检查可用数据
    alt 数据充足
        BM->>BM: 4.13 从循环缓冲区读取
        BM->>BM: 4.14 更新读指针和数据大小
        BM->>BM: 4.15 更新缓冲区状态
        BM->>BM: 4.16 通知等待线程
        BM-->>App: 4.17 返回读取数据
    else 数据不足
        alt 阻塞模式
            BM->>BM: 4.18 等待数据到达
            BM->>BM: 4.19 重试读取
        else 非阻塞模式
            BM-->>App: 4.20 返回可用数据
        end
    end
    
    Note over App,CB: 5. 健康检查阶段
    Pool->>Pool: 5.1 定时健康检查
    Pool->>SM: 5.2 检查连接状态
    SM->>HW: 5.3 验证串口连接
    HW-->>SM: 5.4 返回状态
    alt 连接健康
        SM-->>Pool: 5.5 返回健康状态
        Pool->>Pool: 5.6 标记连接健康
    else 连接异常
        SM-->>Pool: 5.7 返回异常状态
        Pool->>SM: 5.8 尝试重连
        SM->>HW: 5.9 重新建立连接
        alt 重连成功
            HW-->>SM: 5.10 连接恢复
            SM->>CB: 5.11 触发恢复回调
            Pool->>Pool: 5.12 标记连接恢复
        else 重连失败
            SM->>CB: 5.13 触发失败回调
            Pool->>Pool: 5.14 移除异常连接
        end
    end
    
    Note over App,CB: 6. 资源清理阶段
    App->>Pool: 6.1 release_connection(port)
    Pool->>Pool: 6.2 标记连接可复用
    Pool->>Pool: 6.3 更新使用时间
    
    App->>Pool: 6.4 shutdown()
    Pool->>SM: 6.5 disconnect()
    SM->>HW: 6.6 关闭串口连接
    HW-->>SM: 6.7 连接已关闭
    SM->>SM: 6.8 清理资源
    SM->>CB: 6.9 触发断开回调
    Pool->>Pool: 6.10 清理连接池
    Pool-->>App: 6.11 清理完成
```

## 关键流程说明

### 1. 连接建立阶段
- **连接池检查**: 优先复用现有连接，提高性能
- **新连接创建**: 当连接不存在时创建新的SerialManager
- **状态回调**: 连接状态变化时触发回调通知
- **统计更新**: 维护连接使用统计信息

### 2. 数据写入阶段
- **状态检查**: 确保连接处于正常状态
- **数据发送**: 通过SerialManager写入物理串口
- **统计维护**: 更新发送字节数和操作次数
- **异常处理**: 连接异常时触发错误回调

### 3. 数据接收阶段
- **被动接收**: 物理串口接收到数据时通知SerialManager
- **主动读取**: 应用程序主动调用读取方法
- **统计更新**: 维护接收数据的统计信息

### 4. 缓冲区处理阶段
- **空间检查**: 写入前检查缓冲区可用空间
- **循环写入**: 使用循环缓冲区算法写入数据
- **指针更新**: 维护读写指针和数据大小
- **线程通知**: 通知等待的读写线程
- **阻塞处理**: 支持阻塞和非阻塞两种模式

### 5. 健康检查阶段
- **定时检查**: 连接池定期检查所有连接的健康状态
- **状态验证**: 通过实际操作验证连接可用性
- **自动恢复**: 检测到异常时尝试自动重连
- **连接清理**: 无法恢复的连接从池中移除

### 6. 资源清理阶段
- **连接释放**: 标记连接为可复用状态
- **优雅关闭**: 按顺序关闭各个组件
- **资源清理**: 释放所有占用的系统资源
- **状态通知**: 触发相应的状态变化回调

## 性能优化点

### 连接复用
- 避免频繁创建和销毁连接
- 维护连接池减少开销
- 智能的连接分配策略

### 缓冲区优化
- 循环缓冲区避免内存碎片
- 批量读写提高效率
- 线程安全的并发访问

### 异步处理
- 非阻塞模式避免线程阻塞
- 条件变量实现高效等待
- 回调机制支持事件驱动

---

**作者**: LD (Lead Developer)  
**创建时间**: 2025-08-07  
**版本**: 1.0
