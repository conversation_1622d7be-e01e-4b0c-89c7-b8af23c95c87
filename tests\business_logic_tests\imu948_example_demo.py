#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IMU948传感器完整示例程序

该程序演示了业务逻辑层的完整功能：
1. 启动主程序
2. 加载JSON配置（imu948_example.json）
3. 协议解析和指令执行
4. 连续数据处理
5. 欧拉角和三维位置信息输出

作者: DataStudio开发团队
创建时间: 2025-08-08
"""

import sys
import os
import time
import signal
import threading
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from business_logic.protocol_flow_controller import ProtocolFlowController, WorkMode, FlowState
from business_logic.error_handler import ErrorHandler, ErrorType, ErrorSeverity
from business_logic.logger_manager import LoggerManager, LogLevel, LogFormat


class IMU948Demo:
    """IMU948传感器演示程序"""
    
    def __init__(self):
        """初始化演示程序"""
        self.config_path = "config/protocols/imu948_example.json"
        self.running = False
        self.stop_event = threading.Event()
        
        # 初始化日志管理器
        log_config = {
            "log_level": LogLevel.INFO,
            "log_format": LogFormat.DETAILED,
            "log_dir": "logs",
            "enable_console": True,
            "enable_file": True,
            "enable_performance": True
        }
        self.logger_manager = LoggerManager(log_config)
        self.logger = self.logger_manager.get_logger("IMU948Demo")
        
        # 初始化错误处理器
        self.error_handler = ErrorHandler()
        
        # 协议流程控制器
        self.flow_controller = None
        
        # 数据统计
        self.data_count = 0
        self.start_time = None
        
        self.logger.info("IMU948演示程序初始化完成")
    
    def check_config_file(self) -> bool:
        """检查配置文件是否存在"""
        config_file = Path(self.config_path)
        if not config_file.exists():
            self.logger.error(f"配置文件不存在: {self.config_path}")
            self.logger.info("请确保配置文件存在并包含正确的IMU948协议配置")
            return False
        
        self.logger.info(f"配置文件检查通过: {self.config_path}")
        return True
    
    def initialize_protocol_controller(self) -> bool:
        """初始化协议流程控制器"""
        try:
            self.logger.info("正在初始化协议流程控制器...")
            self.flow_controller = ProtocolFlowController(self.config_path)
            
            # 获取协议信息
            protocol_info = self.flow_controller.get_protocol_info()
            self.logger.info(f"协议信息:")
            self.logger.info(f"  名称: {protocol_info['name']}")
            self.logger.info(f"  描述: {protocol_info['description']}")
            self.logger.info(f"  版本: {protocol_info['version']}")
            self.logger.info(f"  串口: {protocol_info['serial_config']['port']}")
            self.logger.info(f"  波特率: {protocol_info['serial_config']['baudrate']}")
            self.logger.info(f"  单次指令数: {protocol_info['single_commands']}")
            self.logger.info(f"  连续指令数: {protocol_info['continuous_commands']}")
            self.logger.info(f"  协议步骤数: {protocol_info['protocol_steps']}")
            self.logger.info(f"  连续数据配置: {protocol_info['continuous_data_configured']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"协议流程控制器初始化失败: {str(e)}")
            self.error_handler.handle_error(
                ErrorType.CONFIGURATION_ERROR,
                f"协议流程控制器初始化失败: {str(e)}",
                "IMU948Demo",
                {"config_path": self.config_path}
            )
            return False
    
    def connect_device(self) -> bool:
        """连接设备"""
        try:
            self.logger.info("正在连接IMU948设备...")
            success = self.flow_controller.connect()
            
            if success:
                self.logger.info("✅ IMU948设备连接成功")
                return True
            else:
                self.logger.error("❌ IMU948设备连接失败")
                self.error_handler.handle_error(
                    ErrorType.COMMUNICATION_ERROR,
                    "设备连接失败",
                    "IMU948Demo",
                    {"port": self.flow_controller.protocol_config.serial_config.port}
                )
                return False
                
        except Exception as e:
            self.logger.error(f"连接设备异常: {str(e)}")
            self.error_handler.handle_error(
                ErrorType.HARDWARE_ERROR,
                f"连接设备异常: {str(e)}",
                "IMU948Demo"
            )
            return False
    
    def execute_initialization_flow(self) -> bool:
        """执行初始化流程"""
        try:
            self.logger.info("正在执行传感器初始化流程...")
            
            # 执行协议流程
            results = self.flow_controller.execute_protocol_flow()
            
            success_count = 0
            total_count = len(results)
            
            for result in results:
                if result.success:
                    success_count += 1
                    self.logger.info(f"✅ 步骤 '{result.step_name}' 执行成功 (耗时: {result.execution_time:.3f}s)")
                else:
                    self.logger.error(f"❌ 步骤 '{result.step_name}' 执行失败: {result.error_message}")
                    self.error_handler.handle_error(
                        ErrorType.PROTOCOL_ERROR,
                        f"协议步骤执行失败: {result.error_message}",
                        "IMU948Demo",
                        {"step_name": result.step_name}
                    )
            
            if success_count == total_count:
                self.logger.info(f"🎉 初始化流程完成，所有 {total_count} 个步骤执行成功")
                return True
            else:
                self.logger.warning(f"⚠️ 初始化流程部分成功: {success_count}/{total_count}")
                return success_count > 0  # 部分成功也允许继续
                
        except Exception as e:
            self.logger.error(f"执行初始化流程异常: {str(e)}")
            self.error_handler.handle_error(
                ErrorType.PROTOCOL_ERROR,
                f"执行初始化流程异常: {str(e)}",
                "IMU948Demo"
            )
            return False
    
    def data_callback(self, data_result):
        """连续数据处理回调函数"""
        try:
            self.data_count += 1

            # 解析欧拉角和位置信息
            euler_angles = {}
            position_info = {}

            for field in data_result.parsed_fields:
                # 修复属性访问：使用 field_name 而不是 name，使用 scaled_value 而不是 value
                if "Roll" in field.field_name or "滚转角" in field.field_name:
                    euler_angles["Roll"] = f"{field.scaled_value:.2f}{field.unit}"
                elif "Pitch" in field.field_name or "俯仰角" in field.field_name:
                    euler_angles["Pitch"] = f"{field.scaled_value:.2f}{field.unit}"
                elif "Yaw" in field.field_name or "偏航角" in field.field_name:
                    euler_angles["Yaw"] = f"{field.scaled_value:.2f}{field.unit}"
                elif "X" in field.field_name and ("位置" in field.field_name or "Position" in field.field_name):
                    position_info["X"] = f"{field.scaled_value:.3f}{field.unit}"
                elif "Y" in field.field_name and ("位置" in field.field_name or "Position" in field.field_name):
                    position_info["Y"] = f"{field.scaled_value:.3f}{field.unit}"
                elif "Z" in field.field_name and ("位置" in field.field_name or "Position" in field.field_name):
                    position_info["Z"] = f"{field.scaled_value:.3f}{field.unit}"
            
            # 计算数据率
            if self.start_time:
                elapsed_time = time.time() - self.start_time
                data_rate = self.data_count / elapsed_time
            else:
                data_rate = 0.0
            
            # 输出数据
            print(f"\r📊 帧#{data_result.frame_number:06d} | ", end="")
            
            if euler_angles:
                print(f"🧭 欧拉角: ", end="")
                for name, value in euler_angles.items():
                    print(f"{name}={value} ", end="")
                print("| ", end="")
            
            if position_info:
                print(f"📍 位置: ", end="")
                for name, value in position_info.items():
                    print(f"{name}={value} ", end="")
                print("| ", end="")
            
            print(f"📈 数据率: {data_rate:.1f}Hz", end="", flush=True)
            
            # 每100帧输出一次详细信息
            if self.data_count % 100 == 0:
                print()  # 换行
                self.logger.info(f"已处理 {self.data_count} 帧数据，数据率: {data_rate:.1f}Hz")
                
                # 输出统计信息
                stats = self.flow_controller.get_statistics()
                self.logger.info(f"统计信息: 帧数={stats['frames_processed']}, 字段数={stats['fields_parsed']}")
            
        except Exception as e:
            self.logger.error(f"数据处理回调异常: {str(e)}")
            self.error_handler.handle_error(
                ErrorType.DATA_PARSING_ERROR,
                f"数据处理回调异常: {str(e)}",
                "IMU948Demo"
            )
    
    def start_continuous_data_collection(self) -> bool:
        """启动连续数据采集"""
        try:
            self.logger.info("🚀 启动连续数据采集模式...")
            
            # 启动连续模式
            self.flow_controller.start_continuous_mode(self.data_callback)
            
            # 记录开始时间
            self.start_time = time.time()
            self.data_count = 0
            
            self.logger.info("✅ 连续数据采集已启动")
            self.logger.info("📊 实时数据显示:")
            self.logger.info("   🧭 欧拉角: Roll(滚转), Pitch(俯仰), Yaw(偏航)")
            self.logger.info("   📍 位置: X, Y, Z坐标")
            self.logger.info("   📈 数据率: 每秒帧数")
            self.logger.info("按 Ctrl+C 停止数据采集")
            print()  # 空行分隔
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动连续数据采集失败: {str(e)}")
            self.error_handler.handle_error(
                ErrorType.SYSTEM_ERROR,
                f"启动连续数据采集失败: {str(e)}",
                "IMU948Demo"
            )
            return False
    
    def stop_data_collection(self):
        """停止数据采集"""
        try:
            print("\n")  # 换行
            self.logger.info("🛑 正在停止数据采集...")
            
            # 停止连续模式
            self.flow_controller.stop_continuous_mode()
            
            # 输出最终统计
            if self.start_time and self.data_count > 0:
                total_time = time.time() - self.start_time
                avg_rate = self.data_count / total_time
                
                self.logger.info("📊 数据采集统计:")
                self.logger.info(f"   总帧数: {self.data_count}")
                self.logger.info(f"   总时间: {total_time:.1f}秒")
                self.logger.info(f"   平均数据率: {avg_rate:.1f}Hz")
            
            # 输出系统统计
            stats = self.flow_controller.get_statistics()
            self.logger.info("🔧 系统统计:")
            self.logger.info(f"   流程执行: {stats['flows_executed']} (成功: {stats['flows_successful']})")
            self.logger.info(f"   指令执行: {stats['commands_executed']} (成功: {stats['commands_successful']})")
            self.logger.info(f"   帧处理: {stats['frames_processed']}")
            self.logger.info(f"   字段解析: {stats['fields_parsed']}")
            
            self.logger.info("✅ 数据采集已停止")
            
        except Exception as e:
            self.logger.error(f"停止数据采集异常: {str(e)}")
    
    def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("🧹 正在清理资源...")
            
            # 断开设备连接
            if self.flow_controller:
                self.flow_controller.disconnect()
            
            # 输出错误统计
            error_stats = self.error_handler.get_statistics()
            if error_stats["total_errors"] > 0:
                self.logger.info("⚠️ 错误统计:")
                self.logger.info(f"   总错误数: {error_stats['total_errors']}")
                self.logger.info(f"   恢复尝试: {error_stats['recovery_attempts']}")
                self.logger.info(f"   恢复成功: {error_stats['recovery_successes']}")
            
            # 关闭日志管理器
            self.logger.info("✅ 资源清理完成")
            self.logger_manager.shutdown()
            
        except Exception as e:
            print(f"清理资源异常: {str(e)}")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在优雅退出...")
        self.running = False
        self.stop_event.set()
    
    def run(self):
        """运行演示程序"""
        try:
            # 注册信号处理器
            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)
            
            self.logger.info("🚀 IMU948传感器演示程序启动")
            self.logger.info("=" * 60)
            
            # 1. 检查配置文件
            if not self.check_config_file():
                return False
            
            # 2. 初始化协议控制器
            if not self.initialize_protocol_controller():
                return False
            
            # 3. 连接设备
            if not self.connect_device():
                return False
            
            # 4. 执行初始化流程
            if not self.execute_initialization_flow():
                self.logger.warning("初始化流程未完全成功，但继续运行...")
            
            # 5. 启动连续数据采集
            if not self.start_continuous_data_collection():
                return False
            
            # 6. 主循环
            self.running = True
            try:
                while self.running and not self.stop_event.is_set():
                    time.sleep(0.1)
            except KeyboardInterrupt:
                print("\n用户中断程序")
            
            # 7. 停止数据采集
            self.stop_data_collection()
            
            return True
            
        except Exception as e:
            self.logger.error(f"程序运行异常: {str(e)}")
            return False
        
        finally:
            # 8. 清理资源
            self.cleanup()


def main():
    """主函数"""
    print("🎯 IMU948传感器数据采集演示程序")
    print("=" * 60)
    print("功能说明:")
    print("1. 🔧 动态加载JSON协议配置")
    print("2. 🔗 自动连接IMU948传感器 (COM6)")
    print("3. ⚙️  执行传感器初始化流程")
    print("4. 📊 实时采集和解析传感器数据")
    print("5. 🧭 显示欧拉角 (Roll, Pitch, Yaw)")
    print("6. 📍 显示三维位置信息 (X, Y, Z)")
    print("7. 📈 实时数据率监控")
    print("8. 🛑 优雅停止和资源清理")
    print("=" * 60)
    
    # 检查配置文件
    config_path = Path("config/protocols/imu948_example.json")
    if not config_path.exists():
        print(f"❌ 错误: 配置文件不存在: {config_path}")
        print("请确保配置文件存在并包含正确的IMU948协议配置")
        return 1
    
    # 创建并运行演示程序
    demo = IMU948Demo()
    
    try:
        success = demo.run()
        return 0 if success else 1
    except Exception as e:
        print(f"❌ 程序异常: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)