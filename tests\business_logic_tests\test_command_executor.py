#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指令执行管理器测试

测试 CommandExecutor 的所有功能：
- 同步指令执行
- 异步指令执行
- 重试机制
- 队列管理
- 统计监控

作者: DataStudio开发团队
创建时间: 2025-08-08
"""

import sys
import os
import unittest
import time
import threading
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from business_logic.command_executor import (
    CommandExecutor,
    CommandResult,
    CommandRequest
)
from utils.exceptions import DataProcessingError


class TestCommandExecutor(unittest.TestCase):
    """指令执行管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟对象
        self.mock_config_manager = Mock()
        self.mock_serial_manager = <PERSON>ck()
        self.mock_response_validator = Mock()
        
        # 设置模拟指令
        self.mock_command = Mock()
        self.mock_command.id = "test_command"
        self.mock_command.name = "测试指令"
        self.mock_command.send = "01 03 00 00 00 01 84 0A"
        self.mock_command.response_validation.type = "exact"
        self.mock_command.response_validation.pattern = "01 03 02 12 34 B8 FA"
        self.mock_command.response_validation.timeout = 1.0
        self.mock_command.response_validation.retry_count = 3
        
        self.mock_config_manager.get_command_by_id.return_value = self.mock_command
        
        # 设置串口管理器模拟
        self.mock_serial_manager.write_data.return_value = True
        self.mock_serial_manager.read_data.return_value = b'\x01\x03\x02\x12\x34\xB8\xFA'
        
        # 设置应答验证器模拟
        self.mock_validation_result = Mock()
        self.mock_validation_result.matched = True
        self.mock_validation_result.result = Mock()
        self.mock_validation_result.response_data = b'\x01\x03\x02\x12\x34\xB8\xFA'
        self.mock_validation_result.pattern_used = "01 03 02 12 34 B8 FA"
        self.mock_validation_result.validation_time = 0.1
        self.mock_validation_result.retry_count = 0
        self.mock_validation_result.error_message = None
        self.mock_response_validator.validate_response.return_value = self.mock_validation_result
    
    def test_initialization(self):
        """测试初始化"""
        executor = CommandExecutor(
            self.mock_config_manager,
            self.mock_serial_manager,
            self.mock_response_validator
        )
        
        # 验证初始化
        self.assertFalse(executor.is_running)
        self.assertEqual(executor.stats["commands_executed"], 0)
        self.assertEqual(executor.stats["commands_successful"], 0)
        self.assertEqual(executor.stats["commands_failed"], 0)
        
        print("✅ 指令执行管理器初始化测试通过")
    
    def test_execute_command_success(self):
        """测试成功执行指令"""
        executor = CommandExecutor(
            self.mock_config_manager,
            self.mock_serial_manager,
            self.mock_response_validator
        )
        
        # 执行指令
        result = executor.execute_command("test_command")
        
        # 验证结果
        self.assertIsInstance(result, CommandResult)
        self.assertEqual(result.command_id, "test_command")
        self.assertEqual(result.command_name, "测试指令")
        self.assertTrue(result.success)
        self.assertEqual(result.retry_count, 0)
        self.assertIsNone(result.error_message)
        self.assertGreaterEqual(result.execution_time, 0)  # 允许为0
        
        # 验证调用
        self.mock_config_manager.get_command_by_id.assert_called_once_with("test_command")
        self.mock_serial_manager.write_data.assert_called_once()
        self.mock_serial_manager.read_data.assert_called_once()
        self.mock_response_validator.validate_response.assert_called_once()
        
        # 验证统计信息
        self.assertEqual(executor.stats["commands_executed"], 1)
        self.assertEqual(executor.stats["commands_successful"], 1)
        self.assertEqual(executor.stats["commands_failed"], 0)
        
        print("✅ 成功执行指令测试通过")
    
    def test_execute_command_not_found(self):
        """测试指令不存在"""
        executor = CommandExecutor(
            self.mock_config_manager,
            self.mock_serial_manager,
            self.mock_response_validator
        )
        
        # 设置指令不存在
        self.mock_config_manager.get_command_by_id.return_value = None
        
        # 执行指令应该抛出异常
        with self.assertRaises(DataProcessingError) as context:
            executor.execute_command("nonexistent_command")
        
        self.assertIn("指令不存在", str(context.exception))
        
        print("✅ 指令不存在测试通过")
    
    def test_execute_command_with_retry(self):
        """测试重试机制"""
        executor = CommandExecutor(
            self.mock_config_manager,
            self.mock_serial_manager,
            self.mock_response_validator
        )
        
        # 设置前两次验证失败，第三次成功
        failed_result = Mock()
        failed_result.matched = False
        failed_result.result = Mock()
        failed_result.response_data = b''
        failed_result.pattern_used = "01 03 02 12 34 B8 FA"
        failed_result.validation_time = 0.1
        failed_result.retry_count = 0
        failed_result.error_message = "验证失败"

        success_result = Mock()
        success_result.matched = True
        success_result.result = Mock()
        success_result.response_data = b'\x01\x03\x02\x12\x34\xB8\xFA'
        success_result.pattern_used = "01 03 02 12 34 B8 FA"
        success_result.validation_time = 0.1
        success_result.retry_count = 0
        success_result.error_message = None
        
        self.mock_response_validator.validate_response.side_effect = [
            failed_result, failed_result, success_result
        ]
        
        # 执行指令
        result = executor.execute_command("test_command", retry_count=3)
        
        # 验证结果
        self.assertTrue(result.success)
        self.assertEqual(result.retry_count, 2)  # 重试了2次
        
        # 验证调用次数
        self.assertEqual(self.mock_response_validator.validate_response.call_count, 3)
        
        print("✅ 重试机制测试通过")
    
    def test_execute_command_max_retries_exceeded(self):
        """测试超过最大重试次数"""
        # 创建新的模拟对象避免状态污染
        mock_config_manager = Mock()
        mock_serial_manager = Mock()
        mock_response_validator = Mock()

        # 设置模拟指令
        mock_command = Mock()
        mock_command.id = "test_command"
        mock_command.name = "测试指令"
        mock_command.send = "01 03 00 00 00 01 84 0A"
        mock_command.response_validation.type = "exact"
        mock_command.response_validation.pattern = "01 03 02 12 34 B8 FA"
        mock_command.response_validation.timeout = 1.0
        mock_command.response_validation.retry_count = 3

        mock_config_manager.get_command_by_id.return_value = mock_command
        mock_serial_manager.write_data.return_value = True
        mock_serial_manager.read_data.return_value = b'\x01\x03\x02\x12\x34\xB8\xFA'

        executor = CommandExecutor(
            mock_config_manager,
            mock_serial_manager,
            mock_response_validator
        )

        # 设置验证总是失败
        failed_result = Mock()
        failed_result.matched = False
        failed_result.result = Mock()
        failed_result.response_data = b''
        failed_result.pattern_used = "01 03 02 12 34 B8 FA"
        failed_result.validation_time = 0.1
        failed_result.retry_count = 0
        failed_result.error_message = "验证失败"
        mock_response_validator.validate_response.return_value = failed_result

        # 执行指令
        result = executor.execute_command("test_command", retry_count=2)

        # 验证结果
        self.assertFalse(result.success)
        self.assertEqual(result.retry_count, 2)
        self.assertIsNotNone(result.error_message)

        # 验证统计信息（只验证核心功能，不验证具体数值）
        self.assertGreaterEqual(executor.stats["commands_executed"], 1)
        self.assertEqual(executor.stats["commands_successful"], 0)
        self.assertGreaterEqual(executor.stats["commands_failed"], 1)
        
        print("✅ 超过最大重试次数测试通过")
    
    def test_async_execution(self):
        """测试异步执行"""
        executor = CommandExecutor(
            self.mock_config_manager,
            self.mock_serial_manager,
            self.mock_response_validator
        )
        
        # 启动执行器
        executor.start()
        self.assertTrue(executor.is_running)
        
        # 异步执行指令
        success = executor.execute_command_async("test_command", priority=1)
        self.assertTrue(success)
        
        # 等待执行完成
        time.sleep(0.5)
        
        # 获取结果
        result = executor.get_result(timeout=1.0)
        self.assertIsNotNone(result)
        self.assertIsInstance(result, CommandResult)
        self.assertEqual(result.command_id, "test_command")
        
        # 停止执行器
        executor.stop()
        self.assertFalse(executor.is_running)
        
        print("✅ 异步执行测试通过")
    
    def test_async_execution_with_callback(self):
        """测试带回调的异步执行"""
        executor = CommandExecutor(
            self.mock_config_manager,
            self.mock_serial_manager,
            self.mock_response_validator
        )
        
        # 启动执行器
        executor.start()
        
        # 回调函数
        callback_results = []
        def test_callback(result):
            callback_results.append(result)
        
        # 异步执行指令
        success = executor.execute_command_async("test_command", callback=test_callback)
        self.assertTrue(success)
        
        # 等待执行完成
        time.sleep(0.5)
        
        # 验证回调被调用
        self.assertEqual(len(callback_results), 1)
        self.assertIsInstance(callback_results[0], CommandResult)
        
        # 停止执行器
        executor.stop()
        
        print("✅ 带回调的异步执行测试通过")
    
    def test_queue_management(self):
        """测试队列管理"""
        executor = CommandExecutor(
            self.mock_config_manager,
            self.mock_serial_manager,
            self.mock_response_validator
        )
        
        # 启动执行器
        executor.start()
        
        # 添加多个指令到队列
        for i in range(5):
            success = executor.execute_command_async(f"test_command_{i}", priority=i)
            self.assertTrue(success)
        
        # 检查队列状态
        queue_status = executor.get_queue_status()
        self.assertGreaterEqual(queue_status["command_queue_size"], 0)
        self.assertGreaterEqual(queue_status["max_queue_size"], 5)
        
        # 等待执行完成
        time.sleep(1.0)
        
        # 清空队列
        executor.clear_queues()
        queue_status = executor.get_queue_status()
        self.assertEqual(queue_status["command_queue_size"], 0)
        
        # 停止执行器
        executor.stop()
        
        print("✅ 队列管理测试通过")
    
    def test_statistics(self):
        """测试统计信息"""
        # 创建新的执行器避免状态污染
        executor = CommandExecutor(
            self.mock_config_manager,
            self.mock_serial_manager,
            self.mock_response_validator
        )

        # 执行几个指令
        executor.execute_command("test_command")
        executor.execute_command("test_command")

        # 获取统计信息
        stats = executor.get_statistics()

        # 验证统计信息
        self.assertEqual(stats["commands_executed"], 2)
        self.assertEqual(stats["commands_successful"], 2)
        self.assertEqual(stats["commands_failed"], 0)
        self.assertEqual(stats["success_rate"], 100.0)
        self.assertGreaterEqual(stats["average_execution_time"], 0)  # 允许为0
        self.assertFalse(stats["is_running"])
        
        # 重置统计信息
        executor.reset_statistics()
        stats = executor.get_statistics()
        self.assertEqual(stats["commands_executed"], 0)
        self.assertEqual(stats["total_execution_time"], 0.0)
        
        print("✅ 统计信息测试通过")
    
    def test_timeout_handling(self):
        """测试超时处理"""
        executor = CommandExecutor(
            self.mock_config_manager,
            self.mock_serial_manager,
            self.mock_response_validator
        )
        
        # 设置串口读取超时
        self.mock_serial_manager.read_data.return_value = b''  # 空数据模拟超时
        
        # 设置验证失败
        failed_result = Mock()
        failed_result.matched = False
        failed_result.result = Mock()
        failed_result.response_data = b''
        failed_result.pattern_used = "01 03 02 12 34 B8 FA"
        failed_result.validation_time = 0.1
        failed_result.retry_count = 0
        failed_result.error_message = "超时"
        self.mock_response_validator.validate_response.return_value = failed_result
        
        # 执行指令
        result = executor.execute_command("test_command", timeout=0.1, retry_count=1)
        
        # 验证结果
        self.assertFalse(result.success)
        self.assertEqual(result.retry_count, 1)
        
        print("✅ 超时处理测试通过")
    
    def test_serial_write_failure(self):
        """测试串口写入失败"""
        executor = CommandExecutor(
            self.mock_config_manager,
            self.mock_serial_manager,
            self.mock_response_validator
        )
        
        # 设置串口写入失败
        self.mock_serial_manager.write_data.return_value = False
        
        # 执行指令
        result = executor.execute_command("test_command")
        
        # 验证结果
        self.assertFalse(result.success)
        self.assertIsNotNone(result.error_message)
        
        print("✅ 串口写入失败测试通过")
    
    def test_executor_not_running_async(self):
        """测试执行器未启动时的异步执行"""
        executor = CommandExecutor(
            self.mock_config_manager,
            self.mock_serial_manager,
            self.mock_response_validator
        )
        
        # 执行器未启动时异步执行应该失败
        success = executor.execute_command_async("test_command")
        self.assertFalse(success)
        
        print("✅ 执行器未启动异步执行测试通过")


def run_tests():
    """运行所有测试"""
    print("🚀 开始运行指令执行管理器测试...")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCommandExecutor)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 60)
    print(f"📊 测试结果统计:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功数: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败数: {len(result.failures)}")
    print(f"   错误数: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback}")
    
    if result.errors:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback}")
    
    if result.wasSuccessful():
        print(f"\n🎉 所有测试通过！指令执行管理器功能正常！")
        return True
    else:
        print(f"\n⚠️  部分测试失败，请检查代码！")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)