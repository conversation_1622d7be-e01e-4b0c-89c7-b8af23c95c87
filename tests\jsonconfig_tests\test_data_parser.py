#!/usr/bin/env python3
"""
数据解析器测试模块

测试DataParser的所有功能，包括：
1. 基于JSON配置的动态数据解析
2. 多种数据类型支持
3. 大端和小端字节序处理
4. 缩放系数和单位转换
5. IMU948真实协议测试

运行方式：
python test_data_parser.py

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import sys
import os
import unittest
import json
import struct
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from data_processing.data_parser import DataParser, DataField, ParsedData
from utils.helper_utils import hex_to_bytes
from utils.exceptions import DataProcessingError


class TestDataField(unittest.TestCase):
    """测试数据字段类"""
    
    def test_valid_field_creation(self):
        """测试有效字段创建"""
        field = DataField(
            name="温度",
            offset=4,
            length=2,
            data_type="int16",
            endian="big",
            scale_factor=0.1,
            unit="°C"
        )
        
        self.assertEqual(field.name, "温度")
        self.assertEqual(field.offset, 4)
        self.assertEqual(field.length, 2)
        self.assertEqual(field.data_type, "int16")
        self.assertEqual(field.endian, "big")
        self.assertEqual(field.scale_factor, 0.1)
        self.assertEqual(field.unit, "°C")
    
    def test_invalid_offset(self):
        """测试无效偏移量"""
        with self.assertRaises(ValueError):
            DataField(
                name="测试",
                offset=-1,
                length=2,
                data_type="int16",
                endian="big",
                scale_factor=1.0,
                unit=""
            )
    
    def test_invalid_length(self):
        """测试无效长度"""
        with self.assertRaises(ValueError):
            DataField(
                name="测试",
                offset=0,
                length=0,
                data_type="int16",
                endian="big",
                scale_factor=1.0,
                unit=""
            )
    
    def test_invalid_data_type(self):
        """测试无效数据类型"""
        with self.assertRaises(ValueError):
            DataField(
                name="测试",
                offset=0,
                length=2,
                data_type="invalid_type",
                endian="big",
                scale_factor=1.0,
                unit=""
            )
    
    def test_invalid_endian(self):
        """测试无效字节序"""
        with self.assertRaises(ValueError):
            DataField(
                name="测试",
                offset=0,
                length=2,
                data_type="int16",
                endian="invalid_endian",
                scale_factor=1.0,
                unit=""
            )


class TestDataParser(unittest.TestCase):
    """测试数据解析器类"""
    
    def setUp(self):
        """测试前准备"""
        # 基本测试配置
        self.basic_config = [
            {
                "name": "温度",
                "offset": 0,
                "length": 2,
                "data_type": "int16",
                "endian": "big",
                "scale_factor": 0.1,
                "unit": "°C"
            },
            {
                "name": "湿度",
                "offset": 2,
                "length": 2,
                "data_type": "int16",
                "endian": "big",
                "scale_factor": 0.01,
                "unit": "%RH"
            }
        ]
        self.basic_parser = DataParser(self.basic_config)
        
        # IMU948配置
        self.imu_config = [
            {
                "name": "Roll_滚转角",
                "offset": 10,
                "length": 2,
                "data_type": "int16",
                "endian": "little",
                "scale_factor": 0.0054931640625,
                "unit": "°"
            },
            {
                "name": "Pitch_俯仰角",
                "offset": 12,
                "length": 2,
                "data_type": "int16",
                "endian": "little",
                "scale_factor": 0.0054931640625,
                "unit": "°"
            },
            {
                "name": "Yaw_偏航角",
                "offset": 14,
                "length": 2,
                "data_type": "int16",
                "endian": "little",
                "scale_factor": 0.0054931640625,
                "unit": "°"
            }
        ]
        self.imu_parser = DataParser(self.imu_config)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(len(self.basic_parser.fields), 2)
        self.assertEqual(self.basic_parser.fields[0].name, "温度")
        self.assertEqual(self.basic_parser.fields[1].name, "湿度")
        
        stats = self.basic_parser.get_statistics()
        self.assertEqual(stats["frames_parsed"], 0)
        self.assertEqual(stats["fields_parsed"], 0)
    
    def test_int16_big_endian_parsing(self):
        """测试int16大端解析"""
        # 温度: 0x0100 = 256, 缩放后 25.6°C
        # 湿度: 0x1388 = 5000, 缩放后 50.00%RH
        test_data = b'\x01\x00\x13\x88'
        
        results = self.basic_parser.parse_frame(test_data)
        
        self.assertEqual(len(results), 2)
        
        # 检查温度
        temp_result = results[0]
        self.assertEqual(temp_result.field_name, "温度")
        self.assertEqual(temp_result.raw_value, 256)
        self.assertAlmostEqual(temp_result.scaled_value, 25.6, places=1)
        self.assertEqual(temp_result.unit, "°C")
        
        # 检查湿度
        humidity_result = results[1]
        self.assertEqual(humidity_result.field_name, "湿度")
        self.assertEqual(humidity_result.raw_value, 5000)
        self.assertAlmostEqual(humidity_result.scaled_value, 50.0, places=2)
        self.assertEqual(humidity_result.unit, "%RH")
    
    def test_int16_little_endian_parsing(self):
        """测试int16小端解析"""
        # IMU948真实数据帧
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        
        results = self.imu_parser.parse_frame(frame_data)
        
        self.assertEqual(len(results), 3)
        
        # 检查Roll角度 (偏移10: C6 F7 -> 0xF7C6 = -2106)
        roll_result = results[0]
        self.assertEqual(roll_result.field_name, "Roll_滚转角")
        self.assertEqual(roll_result.raw_value, -2106)
        expected_roll = -2106 * 0.0054931640625
        self.assertAlmostEqual(roll_result.scaled_value, expected_roll, places=3)
        self.assertEqual(roll_result.unit, "°")
        
        # 检查Pitch角度 (偏移12: 08 FF -> 0xFF08 = -248)
        pitch_result = results[1]
        self.assertEqual(pitch_result.field_name, "Pitch_俯仰角")
        self.assertEqual(pitch_result.raw_value, -248)
        expected_pitch = -248 * 0.0054931640625
        self.assertAlmostEqual(pitch_result.scaled_value, expected_pitch, places=3)
        
        # 检查Yaw角度 (偏移14: FE B8 -> 0xB8FE = -18178)
        yaw_result = results[2]
        self.assertEqual(yaw_result.field_name, "Yaw_偏航角")
        self.assertEqual(yaw_result.raw_value, -18178)
        expected_yaw = -18178 * 0.0054931640625
        self.assertAlmostEqual(yaw_result.scaled_value, expected_yaw, places=3)
    
    def test_different_data_types(self):
        """测试不同数据类型"""
        config = [
            {
                "name": "int8_field",
                "offset": 0,
                "length": 1,
                "data_type": "int8",
                "endian": "big",
                "scale_factor": 1.0,
                "unit": ""
            },
            {
                "name": "int32_field",
                "offset": 1,
                "length": 4,
                "data_type": "int32",
                "endian": "big",
                "scale_factor": 1.0,
                "unit": ""
            },
            {
                "name": "float32_field",
                "offset": 5,
                "length": 4,
                "data_type": "float32",
                "endian": "big",
                "scale_factor": 1.0,
                "unit": ""
            }
        ]
        
        parser = DataParser(config)
        
        # 构造测试数据
        test_data = bytearray()
        test_data.extend(struct.pack('b', -128))  # int8: -128
        test_data.extend(struct.pack('>i', 123456))  # int32: 123456 (big endian)
        test_data.extend(struct.pack('>f', 3.14159))  # float32: 3.14159 (big endian)
        
        results = parser.parse_frame(bytes(test_data))
        
        self.assertEqual(len(results), 3)
        
        # 检查int8
        self.assertEqual(results[0].raw_value, -128)
        
        # 检查int32
        self.assertEqual(results[1].raw_value, 123456)
        
        # 检查float32
        self.assertAlmostEqual(results[2].raw_value, 3.14159, places=5)
    
    def test_frame_too_short(self):
        """测试帧数据太短"""
        short_data = b'\x01\x00'  # 只有2字节，但需要4字节
        
        results = self.basic_parser.parse_frame(short_data)
        
        # 应该只解析第一个字段，第二个字段会失败
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].field_name, "温度")
        
        stats = self.basic_parser.get_statistics()
        self.assertEqual(stats["parse_errors"], 1)  # 第二个字段解析失败
    
    def test_empty_frame(self):
        """测试空帧数据"""
        results = self.basic_parser.parse_frame(b'')
        self.assertEqual(len(results), 0)
    
    def test_field_name_lookup(self):
        """测试字段名称查找"""
        field_names = self.basic_parser.get_field_names()
        self.assertEqual(field_names, ["温度", "湿度"])
        
        temp_field = self.basic_parser.get_field_by_name("温度")
        self.assertIsNotNone(temp_field)
        self.assertEqual(temp_field.name, "温度")
        
        nonexistent_field = self.basic_parser.get_field_by_name("不存在的字段")
        self.assertIsNone(nonexistent_field)
    
    def test_frame_compatibility_validation(self):
        """测试帧兼容性验证"""
        # 足够长的帧
        self.assertTrue(self.basic_parser.validate_frame_compatibility(10))
        
        # 刚好够长的帧
        self.assertTrue(self.basic_parser.validate_frame_compatibility(4))
        
        # 太短的帧
        self.assertFalse(self.basic_parser.validate_frame_compatibility(3))
    
    def test_frame_requirements(self):
        """测试帧要求获取"""
        requirements = self.basic_parser.get_frame_requirements()
        
        self.assertEqual(requirements["min_frame_length"], 4)  # 偏移2 + 长度2
        self.assertEqual(requirements["field_count"], 2)
        self.assertEqual(requirements["max_offset"], 2)
        self.assertEqual(requirements["max_field_length"], 2)
    
    def test_statistics_tracking(self):
        """测试统计信息跟踪"""
        test_data = b'\x01\x00\x13\x88'
        
        # 解析多次
        self.basic_parser.parse_frame(test_data)
        self.basic_parser.parse_frame(test_data)
        
        stats = self.basic_parser.get_statistics()
        self.assertEqual(stats["frames_parsed"], 2)
        self.assertEqual(stats["fields_parsed"], 4)  # 2帧 × 2字段
        
        # 重置统计
        self.basic_parser.reset_statistics()
        stats = self.basic_parser.get_statistics()
        self.assertEqual(stats["frames_parsed"], 0)
        self.assertEqual(stats["fields_parsed"], 0)


class TestIMU948RealProtocol(unittest.TestCase):
    """测试IMU948真实协议数据解析"""
    
    def setUp(self):
        """加载IMU948配置"""
        config_path = project_root / "config" / "protocols" / "imu948_example.json"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            self.protocol_config = json.load(f)
        
        parsing_config = self.protocol_config["continuous_data"]["data_parsing"]
        self.parser = DataParser(parsing_config)
    
    def test_imu948_data_parsing_config(self):
        """测试IMU948数据解析配置"""
        field_names = self.parser.get_field_names()
        expected_names = [
            "Roll_滚转角", "Pitch_俯仰角", "Yaw_偏航角",
            "Position_X", "Position_Y", "Position_Z"
        ]
        
        self.assertEqual(field_names, expected_names)
        
        # 检查字段配置
        roll_field = self.parser.get_field_by_name("Roll_滚转角")
        self.assertEqual(roll_field.offset, 10)
        self.assertEqual(roll_field.length, 2)
        self.assertEqual(roll_field.data_type, "int16")
        self.assertEqual(roll_field.endian, "little")
        self.assertAlmostEqual(roll_field.scale_factor, 0.0054931640625, places=10)
    
    def test_imu948_real_frame_parsing(self):
        """测试IMU948真实帧数据解析"""
        # IMU948真实数据帧
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        
        results = self.parser.parse_frame(frame_data)
        
        self.assertEqual(len(results), 6)
        
        # 验证所有字段都被解析
        field_names = [result.field_name for result in results]
        expected_names = [
            "Roll_滚转角", "Pitch_俯仰角", "Yaw_偏航角",
            "Position_X", "Position_Y", "Position_Z"
        ]
        self.assertEqual(field_names, expected_names)
        
        # 验证具体数值（基于真实IMU948数据）
        roll_result = results[0]
        self.assertEqual(roll_result.raw_value, -2106)  # 0xF7C6
        self.assertAlmostEqual(roll_result.scaled_value, -11.57, places=1)
        
        pitch_result = results[1]
        self.assertEqual(pitch_result.raw_value, -248)   # 0xFF08
        self.assertAlmostEqual(pitch_result.scaled_value, -1.36, places=1)
        
        yaw_result = results[2]
        self.assertEqual(yaw_result.raw_value, -18178)  # 0xB8FE
        self.assertAlmostEqual(yaw_result.scaled_value, -99.87, places=1)
    
    def test_imu948_multiple_frames(self):
        """测试IMU948多帧数据解析"""
        frames = [
            "49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D",
            "49 00 13 11 C0 00 2F 41 05 06 D1 F7 09 FF FE B8 00 00 00 00 00 00 E4 4D",
            "49 00 13 11 C0 00 2F 41 05 06 DC F7 0A FF FE B8 00 00 00 00 00 00 EF 4D"
        ]
        
        for i, frame_hex in enumerate(frames):
            frame_data = hex_to_bytes(frame_hex)
            results = self.parser.parse_frame(frame_data)
            
            self.assertEqual(len(results), 6, f"帧 {i+1} 解析字段数量不正确")
            
            # 验证每帧都有所有必需字段
            field_names = [result.field_name for result in results]
            self.assertIn("Roll_滚转角", field_names)
            self.assertIn("Pitch_俯仰角", field_names)
            self.assertIn("Yaw_偏航角", field_names)


def run_comprehensive_tests():
    """运行全面测试"""
    print("=" * 60)
    print("数据解析器全面测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加所有测试类
    test_classes = [
        TestDataField,
        TestDataParser,
        TestIMU948RealProtocol
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"运行测试数量: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n测试通过率: {success_rate:.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
