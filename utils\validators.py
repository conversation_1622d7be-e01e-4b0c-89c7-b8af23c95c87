#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证器
提供JSON配置文件的验证功能

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

import re
from typing import Dict, Any, List

from .constants import ConfigLimits, LOG_LEVELS, SerialLimits
from .exceptions import ConfigValidationError
from .helper_utils import HexUtils, ValidationUtils


class ConfigValidator:
    """配置验证器基类"""
    
    def __init__(self):
        self.errors: List[str] = []
    
    def clear_errors(self) -> None:
        """清除错误列表"""
        self.errors.clear()
    
    def add_error(self, error_message: str) -> None:
        """添加错误信息"""
        self.errors.append(error_message)
    
    def has_errors(self) -> bool:
        """检查是否有错误"""
        return len(self.errors) > 0
    
    def get_errors(self) -> List[str]:
        """获取错误列表"""
        return self.errors.copy()
    
    def raise_if_errors(self, field_path: str = "") -> None:
        """如果有错误则抛出异常"""
        if self.has_errors():
            raise ConfigValidationError(
                f"配置验证失败: {'; '.join(self.errors)}",
                field_path=field_path,
                validation_errors=self.errors
            )








class ProtocolConfigValidator(ConfigValidator):
    """自由串口协议配置验证器"""

    def validate(self, config: Dict[str, Any]) -> bool:
        """
        验证协议配置

        Args:
            config: 协议配置字典

        Returns:
            验证是否通过
        """
        self.clear_errors()

        # 验证协议信息
        if 'protocol_info' in config:
            self._validate_protocol_info(config['protocol_info'])
        else:
            self.add_error("缺少必需的protocol_info字段")

        # 验证串口配置
        if 'serial_config' in config:
            self._validate_serial_config(config['serial_config'])
        else:
            self.add_error("缺少必需的serial_config字段")

        # 验证协议流程
        if 'protocol_flow' in config:
            self._validate_protocol_flow(config['protocol_flow'])
        else:
            self.add_error("缺少必需的protocol_flow字段")

        # 验证指令定义
        if 'commands' in config:
            self._validate_commands(config['commands'])
        else:
            self.add_error("缺少必需的commands字段")

        # 验证连续数据配置（可选）
        if 'continuous_data' in config:
            self._validate_continuous_data(config['continuous_data'])

        # 验证引用一致性
        if not self.has_errors():
            self._validate_references(config)

        return not self.has_errors()

    def _validate_protocol_info(self, protocol_info: Dict[str, Any]) -> None:
        """验证协议信息"""
        if 'name' not in protocol_info or not protocol_info['name']:
            self.add_error("protocol_info.name字段不能为空")

        if 'version' in protocol_info:
            version = protocol_info['version']
            if not isinstance(version, str):
                self.add_error("protocol_info.version必须是字符串")

    def _validate_serial_config(self, serial_config: Dict[str, Any]) -> None:
        """验证串口配置"""
        # 验证端口
        if 'port' not in serial_config or not serial_config['port']:
            self.add_error("serial_config.port字段不能为空")

        # 验证波特率
        if 'baudrate' in serial_config:
            baudrate = serial_config['baudrate']
            if not ValidationUtils.is_valid_baudrate(baudrate):
                self.add_error(f"无效的波特率: {baudrate}")
        else:
            self.add_error("缺少必需的serial_config.baudrate字段")

        # 验证数据位
        if 'databits' in serial_config:
            databits = serial_config['databits']
            if databits not in SerialLimits.SUPPORTED_DATABITS:
                self.add_error(f"无效的数据位: {databits}")
        else:
            self.add_error("缺少必需的serial_config.databits字段")

        # 验证校验位
        if 'parity' in serial_config:
            parity = serial_config['parity']
            if parity not in SerialLimits.SUPPORTED_PARITIES:
                self.add_error(f"无效的校验位: {parity}")
        else:
            self.add_error("缺少必需的serial_config.parity字段")

        # 验证停止位
        if 'stopbits' in serial_config:
            stopbits = serial_config['stopbits']
            if stopbits not in SerialLimits.SUPPORTED_STOPBITS:
                self.add_error(f"无效的停止位: {stopbits}")
        else:
            self.add_error("缺少必需的serial_config.stopbits字段")

        # 验证超时时间
        if 'timeout' in serial_config:
            timeout = serial_config['timeout']
            if not ValidationUtils.is_valid_timeout(timeout):
                self.add_error(f"无效的超时时间: {timeout}")
        else:
            self.add_error("缺少必需的serial_config.timeout字段")

    def _validate_protocol_flow(self, protocol_flow: Dict[str, Any]) -> None:
        """验证协议流程"""
        if 'steps' not in protocol_flow:
            self.add_error("protocol_flow缺少必需的steps字段")
            return

        steps = protocol_flow['steps']
        if not isinstance(steps, list) or len(steps) == 0:
            self.add_error("protocol_flow.steps必须是非空数组")
            return

        for i, step in enumerate(steps):
            self._validate_protocol_step(step, i)

    def _validate_protocol_step(self, step: Dict[str, Any], index: int) -> None:
        """验证协议步骤"""
        step_prefix = f"protocol_flow.steps[{index}]"

        # 验证步骤名称
        if 'name' not in step or not step['name']:
            self.add_error(f"{step_prefix}.name字段不能为空")

        # 验证步骤类型
        if 'type' not in step:
            self.add_error(f"{step_prefix}缺少必需的type字段")
        else:
            step_type = step['type']
            if step_type not in ['single_command', 'continuous_command']:
                self.add_error(f"{step_prefix}.type无效: {step_type}")

        # 验证指令列表
        if 'commands' not in step:
            self.add_error(f"{step_prefix}缺少必需的commands字段")
        else:
            commands = step['commands']
            if not isinstance(commands, list):
                self.add_error(f"{step_prefix}.commands必须是数组")

        # 验证auto_start字段（可选）
        if 'auto_start' in step:
            auto_start = step['auto_start']
            if not isinstance(auto_start, bool):
                self.add_error(f"{step_prefix}.auto_start必须是布尔值")

    def _validate_commands(self, commands: Dict[str, Any]) -> None:
        """验证指令定义"""
        # 验证单条指令
        if 'single' in commands:
            single_commands = commands['single']
            if not isinstance(single_commands, list):
                self.add_error("commands.single必须是数组")
            else:
                for i, cmd in enumerate(single_commands):
                    self._validate_command(cmd, f"commands.single[{i}]")

        # 验证连续指令
        if 'continuous' in commands:
            continuous_commands = commands['continuous']
            if not isinstance(continuous_commands, list):
                self.add_error("commands.continuous必须是数组")
            else:
                for i, cmd in enumerate(continuous_commands):
                    self._validate_command(cmd, f"commands.continuous[{i}]")

    def _validate_command(self, command: Dict[str, Any], prefix: str) -> None:
        """验证单个指令"""
        # 验证指令ID
        if 'id' not in command or not command['id']:
            self.add_error(f"{prefix}.id字段不能为空")

        # 验证指令名称
        if 'name' not in command or not command['name']:
            self.add_error(f"{prefix}.name字段不能为空")

        # 验证发送指令
        if 'send' not in command:
            self.add_error(f"{prefix}缺少必需的send字段")
        else:
            send_data = command['send']
            if not isinstance(send_data, str):
                self.add_error(f"{prefix}.send必须是字符串")
            elif not HexUtils.is_valid_hex_string(send_data):
                self.add_error(f"{prefix}.send不是有效的十六进制字符串: {send_data}")

        # 验证应答验证配置
        if 'response_validation' not in command:
            self.add_error(f"{prefix}缺少必需的response_validation字段")
        else:
            self._validate_response_validation(command['response_validation'], f"{prefix}.response_validation")

    def _validate_response_validation(self, validation: Dict[str, Any], prefix: str) -> None:
        """验证应答验证配置"""
        # 验证验证类型
        if 'type' not in validation:
            self.add_error(f"{prefix}缺少必需的type字段")
        else:
            val_type = validation['type']
            if val_type not in ['exact', 'regex']:
                self.add_error(f"{prefix}.type无效: {val_type}")

        # 验证验证模式
        if 'pattern' not in validation or not validation['pattern']:
            self.add_error(f"{prefix}.pattern字段不能为空")
        else:
            pattern = validation['pattern']
            val_type = validation.get('type', 'exact')

            if val_type == 'exact':
                # 精确匹配模式，验证十六进制格式
                if not HexUtils.is_valid_hex_string(pattern):
                    self.add_error(f"{prefix}.pattern不是有效的十六进制字符串: {pattern}")
            elif val_type == 'regex':
                # 正则表达式模式，验证正则表达式有效性
                try:
                    re.compile(pattern)
                except re.error as e:
                    self.add_error(f"{prefix}.pattern不是有效的正则表达式: {e}")

        # 验证超时时间
        if 'timeout' not in validation:
            self.add_error(f"{prefix}缺少必需的timeout字段")
        else:
            timeout = validation['timeout']
            if not ValidationUtils.is_valid_timeout(timeout):
                self.add_error(f"{prefix}.timeout无效: {timeout}")

        # 验证重试次数
        if 'retry_count' not in validation:
            self.add_error(f"{prefix}缺少必需的retry_count字段")
        else:
            retry_count = validation['retry_count']
            if not isinstance(retry_count, int) or retry_count < 0:
                self.add_error(f"{prefix}.retry_count必须是非负整数")

    def _validate_continuous_data(self, continuous_data: Dict[str, Any]) -> None:
        """验证连续数据配置"""
        # 验证帧检测配置
        if 'frame_detection' not in continuous_data:
            self.add_error("continuous_data缺少必需的frame_detection字段")
        else:
            self._validate_frame_detection(continuous_data['frame_detection'])

        # 验证数据解析配置
        if 'data_parsing' not in continuous_data:
            self.add_error("continuous_data缺少必需的data_parsing字段")
        else:
            data_parsing = continuous_data['data_parsing']
            if not isinstance(data_parsing, list):
                self.add_error("continuous_data.data_parsing必须是数组")
            else:
                for i, field in enumerate(data_parsing):
                    self._validate_data_field(field, f"continuous_data.data_parsing[{i}]")

    def _validate_frame_detection(self, frame_detection: Dict[str, Any]) -> None:
        """验证帧检测配置"""
        prefix = "continuous_data.frame_detection"

        # 验证帧头
        if 'header' not in frame_detection or not frame_detection['header']:
            self.add_error(f"{prefix}.header字段不能为空")
        else:
            header = frame_detection['header']
            if not HexUtils.is_valid_hex_string(header):
                self.add_error(f"{prefix}.header不是有效的十六进制字符串: {header}")

        # 验证帧尾
        if 'tail' not in frame_detection or not frame_detection['tail']:
            self.add_error(f"{prefix}.tail字段不能为空")
        else:
            tail = frame_detection['tail']
            if not HexUtils.is_valid_hex_string(tail):
                self.add_error(f"{prefix}.tail不是有效的十六进制字符串: {tail}")

        # 验证最小长度
        if 'min_length' not in frame_detection:
            self.add_error(f"{prefix}缺少必需的min_length字段")
        else:
            min_length = frame_detection['min_length']
            if not isinstance(min_length, int) or min_length < 1:
                self.add_error(f"{prefix}.min_length必须是正整数")

        # 验证最大长度
        if 'max_length' not in frame_detection:
            self.add_error(f"{prefix}缺少必需的max_length字段")
        else:
            max_length = frame_detection['max_length']
            if not isinstance(max_length, int) or max_length < 1:
                self.add_error(f"{prefix}.max_length必须是正整数")

            # 验证长度范围合理性
            min_length = frame_detection.get('min_length', 1)
            if isinstance(min_length, int) and max_length < min_length:
                self.add_error(f"{prefix}.max_length不能小于min_length")

    def _validate_data_field(self, field: Dict[str, Any], prefix: str) -> None:
        """验证数据字段配置"""
        # 验证字段名称
        if 'name' not in field or not field['name']:
            self.add_error(f"{prefix}.name字段不能为空")

        # 验证偏移量
        if 'offset' not in field:
            self.add_error(f"{prefix}缺少必需的offset字段")
        else:
            offset = field['offset']
            if not isinstance(offset, int) or offset < 0:
                self.add_error(f"{prefix}.offset必须是非负整数")

        # 验证长度
        if 'length' not in field:
            self.add_error(f"{prefix}缺少必需的length字段")
        else:
            length = field['length']
            if not isinstance(length, int) or length < 1:
                self.add_error(f"{prefix}.length必须是正整数")

        # 验证数据类型
        if 'data_type' not in field:
            self.add_error(f"{prefix}缺少必需的data_type字段")
        else:
            data_type = field['data_type']
            valid_types = ['int8', 'int16', 'int32', 'float32', 'float64']
            if data_type not in valid_types:
                self.add_error(f"{prefix}.data_type无效: {data_type}")

        # 验证字节序
        if 'endian' not in field:
            self.add_error(f"{prefix}缺少必需的endian字段")
        else:
            endian = field['endian']
            if endian not in ['big', 'little']:
                self.add_error(f"{prefix}.endian无效: {endian}")

        # 验证缩放系数
        if 'scale_factor' not in field:
            self.add_error(f"{prefix}缺少必需的scale_factor字段")
        else:
            scale_factor = field['scale_factor']
            if not isinstance(scale_factor, (int, float)):
                self.add_error(f"{prefix}.scale_factor必须是数值")

    def _validate_references(self, config: Dict[str, Any]) -> None:
        """验证引用一致性"""
        # 收集所有指令ID
        command_ids = set()
        commands = config.get('commands', {})

        for cmd in commands.get('single', []):
            if 'id' in cmd:
                command_ids.add(cmd['id'])

        for cmd in commands.get('continuous', []):
            if 'id' in cmd:
                command_ids.add(cmd['id'])

        # 验证协议流程中的引用
        protocol_flow = config.get('protocol_flow', {})
        steps = protocol_flow.get('steps', [])

        for i, step in enumerate(steps):
            step_commands = step.get('commands', [])
            step_type = step.get('type', '')

            for cmd_id in step_commands:
                if cmd_id not in command_ids:
                    self.add_error(f"protocol_flow.steps[{i}]引用了不存在的指令ID: {cmd_id}")
                else:
                    # 验证步骤类型与指令类型的匹配
                    if step_type == 'single_command':
                        # 检查指令是否在single类型中
                        single_ids = {cmd.get('id') for cmd in commands.get('single', [])}
                        if cmd_id not in single_ids:
                            self.add_error(f"protocol_flow.steps[{i}]类型为single_command，但引用了continuous类型的指令: {cmd_id}")

                    elif step_type == 'continuous_command':
                        # 检查指令是否在continuous类型中
                        continuous_ids = {cmd.get('id') for cmd in commands.get('continuous', [])}
                        if cmd_id not in continuous_ids:
                            self.add_error(f"protocol_flow.steps[{i}]类型为continuous_command，但引用了single类型的指令: {cmd_id}")


class SystemConfigValidator(ConfigValidator):
    """系统配置验证器"""

    def validate(self, config: Dict[str, Any]) -> bool:
        """
        验证系统配置

        Args:
            config: 系统配置字典

        Returns:
            验证是否通过
        """
        self.clear_errors()

        # 验证缓冲区配置
        if 'buffer' in config:
            self._validate_buffer_config(config['buffer'])

        # 验证队列配置
        if 'queue' in config:
            self._validate_queue_config(config['queue'])

        # 验证错误处理配置
        if 'error_handling' in config:
            self._validate_error_handling_config(config['error_handling'])

        # 验证日志配置
        if 'logging' in config:
            self._validate_logging_config(config['logging'])

        return not self.has_errors()
    
    def _validate_buffer_config(self, buffer_config: Dict[str, Any]) -> None:
        """验证缓冲区配置"""
        if 'size' in buffer_config:
            size = buffer_config['size']
            if not isinstance(size, int) or size < ConfigLimits.MIN_BUFFER_SIZE:
                self.add_error(f"缓冲区大小必须是不小于 {ConfigLimits.MIN_BUFFER_SIZE} 的整数")

        if 'warning_threshold' in buffer_config:
            threshold = buffer_config['warning_threshold']
            if not isinstance(threshold, (int, float)) or not (0 < threshold < 1):
                self.add_error("缓冲区警告阈值必须是0到1之间的数值")
    
    def _validate_queue_config(self, queue_config: Dict[str, Any]) -> None:
        """验证队列配置"""
        if 'size' in queue_config:
            size = queue_config['size']
            if not isinstance(size, int) or size < 1:
                self.add_error("队列大小必须是正整数")
        
        if 'warning_threshold' in queue_config:
            threshold = queue_config['warning_threshold']
            if not isinstance(threshold, (int, float)) or not (0 < threshold < 1):
                self.add_error("队列警告阈值必须是0到1之间的数值")
        
        if 'batch_size' in queue_config:
            batch_size = queue_config['batch_size']
            if not isinstance(batch_size, int) or batch_size < 1:
                self.add_error("批处理大小必须是正整数")
    
    def _validate_error_handling_config(self, error_config: Dict[str, Any]) -> None:
        """验证错误处理配置"""
        if 'max_consecutive_errors' in error_config:
            max_errors = error_config['max_consecutive_errors']
            if not isinstance(max_errors, int) or max_errors < 1:
                self.add_error("最大连续错误次数必须是正整数")
        
        if 'retry_delay' in error_config:
            delay = error_config['retry_delay']
            if not isinstance(delay, (int, float)) or delay < 0:
                self.add_error("重试延迟必须是非负数")
        
        if 'max_retry_attempts' in error_config:
            attempts = error_config['max_retry_attempts']
            if not isinstance(attempts, int) or attempts < 0:
                self.add_error("最大重试次数必须是非负整数")
    
    def _validate_logging_config(self, logging_config: Dict[str, Any]) -> None:
        """验证日志配置"""
        if 'level' in logging_config:
            level = logging_config['level']
            valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if level not in valid_levels:
                self.add_error(f"日志级别无效: {level}")
        
        if 'max_file_size' in logging_config:
            size = logging_config['max_file_size']
            if not isinstance(size, int) or size < 1024:  # 至少1KB
                self.add_error("日志文件最大大小必须是不小于1024的整数")
        
        if 'max_files' in logging_config:
            files = logging_config['max_files']
            if not isinstance(files, int) or files < 1:
                self.add_error("日志文件最大数量必须是正整数")


# 便捷函数
def validate_system_config(config: Dict[str, Any]) -> None:
    """
    验证系统配置的便捷函数

    Args:
        config: 系统配置字典

    Raises:
        ConfigValidationError: 验证失败时抛出
    """
    validator = SystemConfigValidator()
    validator.validate(config)
    validator.raise_if_errors("system")


def validate_protocol_config(config: Dict[str, Any]) -> None:
    """
    验证协议配置的便捷函数

    Args:
        config: 协议配置字典

    Raises:
        ConfigValidationError: 验证失败时抛出
    """
    validator = ProtocolConfigValidator()
    validator.validate(config)
    validator.raise_if_errors("protocol")
