#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 logging_config.py 模块
验证全局日志配置系统的功能

作者: LD (Lead Developer)
创建时间: 2025-08-06
版本: 1.0
"""

import os
import sys
import logging
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.logging_config import (
    LoggingConfig, setup_logging, get_logger, create_component_logger,
    get_serial_logger, get_data_processing_logger, get_business_logic_logger,
    get_ui_logger, get_config_logger
)


def test_logging_config_setup():
    """测试日志配置设置"""
    print("=" * 60)
    print("测试日志配置设置")
    print("=" * 60)

    # 创建临时目录用于测试
    temp_dir = tempfile.mkdtemp()
    try:
        # 设置日志配置
        setup_logging(
            log_level="DEBUG",
            log_dir=temp_dir,
            console_output=True,
            file_output=True,
            max_file_size=1024*1024,  # 1MB
            max_files=5
        )

        print("✅ 日志配置设置成功")

        # 验证日志文件是否创建
        log_files = list(Path(temp_dir).glob("*.log"))
        print(f"✅ 创建了 {len(log_files)} 个日志文件")
        for log_file in log_files:
            print(f"   - {log_file.name}")

        # 关闭所有日志处理器以释放文件
        import logging
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            handler.close()
            root_logger.removeHandler(handler)

        return True

    except Exception as e:
        print(f"❌ 日志配置设置失败: {e}")
        return False
    finally:
        # 手动清理临时目录
        try:
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
        except:
            pass


def test_get_logger():
    """测试获取日志器"""
    print("\n" + "=" * 60)
    print("测试获取日志器")
    print("=" * 60)
    
    try:
        # 获取不同的日志器
        logger1 = get_logger("test_module1")
        logger2 = get_logger("test_module2")
        logger3 = get_logger("test_module1")  # 重复获取
        
        print("✅ 成功获取日志器")
        print(f"   - logger1: {logger1.name}")
        print(f"   - logger2: {logger2.name}")
        print(f"   - logger3: {logger3.name}")
        
        # 验证相同名称的日志器是同一个实例
        if logger1 is logger3:
            print("✅ 相同名称的日志器返回同一实例")
        else:
            print("❌ 相同名称的日志器返回不同实例")
            return False
        
        # 测试日志记录
        logger1.info("这是一条测试信息")
        logger1.warning("这是一条测试警告")
        logger1.error("这是一条测试错误")
        
        print("✅ 日志记录功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 获取日志器失败: {e}")
        return False


def test_component_loggers():
    """测试组件专用日志器"""
    print("\n" + "=" * 60)
    print("测试组件专用日志器")
    print("=" * 60)
    
    try:
        # 获取各种组件日志器
        serial_logger = get_serial_logger()
        data_logger = get_data_processing_logger()
        business_logger = get_business_logic_logger()
        ui_logger = get_ui_logger()
        config_logger = get_config_logger()
        
        print("✅ 成功获取所有组件日志器")
        print(f"   - 串口日志器: {serial_logger.name}")
        print(f"   - 数据处理日志器: {data_logger.name}")
        print(f"   - 业务逻辑日志器: {business_logger.name}")
        print(f"   - 用户界面日志器: {ui_logger.name}")
        print(f"   - 配置管理日志器: {config_logger.name}")
        
        # 测试各个日志器的记录功能
        serial_logger.info("串口通信测试信息")
        data_logger.debug("数据处理调试信息")
        business_logger.warning("业务逻辑警告信息")
        ui_logger.error("用户界面错误信息")
        config_logger.critical("配置管理严重错误")
        
        print("✅ 所有组件日志器记录功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 组件日志器测试失败: {e}")
        return False


def test_custom_component_logger():
    """测试自定义组件日志器（简化版）"""
    print("\n" + "=" * 60)
    print("测试自定义组件日志器（简化版）")
    print("=" * 60)

    try:
        # 测试不同类型的组件名称映射
        test_cases = [
            ("serial_component", "串口"),
            ("data_processor", "数据处理"),
            ("business_logic", "业务逻辑"),
            ("ui_component", "用户界面"),
            ("config_manager", "配置管理"),
            ("custom_component", "通用组件")
        ]

        for component_name, expected_type in test_cases:
            # 创建组件日志器
            logger = create_component_logger(component_name, "unused.log")

            print(f"✅ {expected_type}组件日志器创建成功")
            print(f"   - 组件名称: {component_name}")
            print(f"   - 日志器名称: {logger.name}")

            # 测试日志记录功能
            logger.info(f"{component_name}测试信息")
            logger.warning(f"{component_name}测试警告")

            # 验证日志器类型
            if component_name == "serial_component":
                expected_name = "datastudio.serial"
            elif component_name == "data_processor":
                expected_name = "datastudio.data_processing"
            elif component_name == "business_logic":
                expected_name = "datastudio.business_logic"
            elif component_name == "ui_component":
                expected_name = "datastudio.ui"
            elif component_name == "config_manager":
                expected_name = "datastudio.config"
            else:
                expected_name = f"datastudio.{component_name}"

            if logger.name == expected_name:
                print(f"✅ 日志器映射正确: {expected_name}")
            else:
                print(f"❌ 日志器映射错误: 期望 {expected_name}, 得到 {logger.name}")
                return False

        print("\n✅ 所有组件日志器映射测试通过")
        print("✅ 简化版设计避免了创建过多日志文件的问题")

        return True

    except Exception as e:
        print(f"❌ 自定义组件日志器测试失败: {e}")
        return False


def test_logging_levels():
    """测试日志级别"""
    print("\n" + "=" * 60)
    print("测试日志级别")
    print("=" * 60)
    
    try:
        # 获取测试日志器
        test_logger = get_logger("level_test")
        
        # 测试不同级别的日志
        test_logger.debug("DEBUG级别日志")
        test_logger.info("INFO级别日志")
        test_logger.warning("WARNING级别日志")
        test_logger.error("ERROR级别日志")
        test_logger.critical("CRITICAL级别日志")
        
        print("✅ 所有日志级别测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 日志级别测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试 logging_config.py 模块")
    print("=" * 80)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("日志配置设置", test_logging_config_setup()))
    test_results.append(("获取日志器", test_get_logger()))
    test_results.append(("组件专用日志器", test_component_loggers()))
    test_results.append(("自定义组件日志器", test_custom_component_logger()))
    test_results.append(("日志级别", test_logging_levels()))
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {len(test_results)} 个测试")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    
    if failed == 0:
        print("\n🎉 所有测试通过！")
        return True
    else:
        print(f"\n⚠️  有 {failed} 个测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
