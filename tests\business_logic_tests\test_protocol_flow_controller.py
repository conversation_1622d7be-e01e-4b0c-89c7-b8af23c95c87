#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
协议流程控制器测试

测试 ProtocolFlowController 的所有功能：
- 协议流程管理和执行
- 串口通信协调
- 工作模式管理
- 业务状态管理
- 连续数据处理

作者: DataStudio开发团队
创建时间: 2025-08-08
"""

import sys
import os
import unittest
import time
import threading
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from business_logic.protocol_flow_controller import (
    ProtocolFlowController, 
    WorkMode, 
    FlowState, 
    FlowResult,
    ContinuousDataResult
)
from utils.exceptions import DataProcessingError


class TestProtocolFlowController(unittest.TestCase):
    """协议流程控制器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config_path = "config/protocols/imu948_example.json"
        
        # 模拟配置管理器
        self.mock_config_manager = Mock()
        self.mock_protocol_config = Mock()
        
        # 设置模拟配置
        self.mock_protocol_config.protocol_info.name = "IMU948测试协议"
        self.mock_protocol_config.serial_config.port = "COM6"
        self.mock_protocol_config.serial_config.baudrate = 115200
        self.mock_protocol_config.serial_config.databits = 8
        self.mock_protocol_config.serial_config.parity = "N"
        self.mock_protocol_config.serial_config.stopbits = 1
        self.mock_protocol_config.serial_config.timeout = 1.0
        
        # 设置协议流程
        self.mock_step = Mock()
        self.mock_step.name = "传感器初始化"
        self.mock_step.commands = ["disable_auto_report", "set_sensor_params"]
        self.mock_protocol_config.protocol_flow.steps = [self.mock_step]
        
        # 设置连续数据配置
        self.mock_continuous_data = Mock()
        self.mock_continuous_data.frame_detection.header = "55 51"
        self.mock_continuous_data.frame_detection.tail = "AA BB"
        self.mock_continuous_data.frame_detection.min_length = 20
        self.mock_continuous_data.frame_detection.max_length = 50
        
        self.mock_field = Mock()
        self.mock_field.name = "Roll_滚转角"
        self.mock_field.offset = 4
        self.mock_field.length = 2
        self.mock_field.data_type = "int16"
        self.mock_field.endian = "little"
        self.mock_field.scale_factor = 0.01
        self.mock_field.unit = "°"
        
        self.mock_continuous_data.data_parsing = [self.mock_field]
        self.mock_protocol_config.continuous_data = self.mock_continuous_data
        
        self.mock_config_manager.load_config.return_value = self.mock_protocol_config
    
    @patch('business_logic.protocol_flow_controller.SerialConfigManager')
    @patch('business_logic.protocol_flow_controller.SerialManager')
    @patch('business_logic.protocol_flow_controller.ResponseValidator')
    @patch('business_logic.protocol_flow_controller.FrameDetector')
    @patch('business_logic.protocol_flow_controller.DataParser')
    @patch('business_logic.protocol_flow_controller.QueueManager')
    def test_initialization(self, mock_queue_mgr, mock_data_parser, mock_frame_detector,
                           mock_response_validator, mock_serial_mgr, mock_config_mgr):
        """测试初始化"""
        # 设置模拟对象
        mock_config_mgr.return_value = self.mock_config_manager
        
        # 创建控制器
        controller = ProtocolFlowController(self.config_path)
        
        # 验证初始化
        self.assertEqual(controller.current_mode, WorkMode.IDLE)
        self.assertEqual(controller.flow_state, FlowState.STOPPED)
        self.assertEqual(controller.frame_counter, 0)
        
        # 验证配置加载
        mock_config_mgr.assert_called_once()
        self.mock_config_manager.load_config.assert_called_once_with(self.config_path)
        
        print("✅ 协议流程控制器初始化测试通过")
    
    @patch('business_logic.protocol_flow_controller.SerialConfigManager')
    @patch('business_logic.protocol_flow_controller.SerialManager')
    @patch('business_logic.protocol_flow_controller.ResponseValidator')
    @patch('business_logic.protocol_flow_controller.FrameDetector')
    @patch('business_logic.protocol_flow_controller.DataParser')
    @patch('business_logic.protocol_flow_controller.QueueManager')
    def test_connect_disconnect(self, mock_queue_mgr, mock_data_parser, mock_frame_detector,
                               mock_response_validator, mock_serial_mgr, mock_config_mgr):
        """测试连接和断开"""
        # 设置模拟对象
        mock_config_mgr.return_value = self.mock_config_manager
        mock_serial_instance = Mock()
        mock_serial_mgr.return_value = mock_serial_instance
        
        # 创建控制器
        controller = ProtocolFlowController(self.config_path)
        
        # 测试连接成功
        mock_serial_instance.connect.return_value = True
        result = controller.connect()
        
        self.assertTrue(result)
        self.assertEqual(controller.flow_state, FlowState.STOPPED)
        mock_serial_instance.connect.assert_called_once()
        
        # 测试连接失败
        mock_serial_instance.connect.return_value = False
        result = controller.connect()
        
        self.assertFalse(result)
        self.assertEqual(controller.flow_state, FlowState.ERROR)
        
        # 测试断开连接
        mock_serial_instance.is_connected.return_value = True
        controller.disconnect()
        
        mock_serial_instance.disconnect.assert_called()
        self.assertEqual(controller.flow_state, FlowState.STOPPED)
        self.assertEqual(controller.current_mode, WorkMode.IDLE)
        
        print("✅ 连接和断开测试通过")
    
    @patch('business_logic.protocol_flow_controller.SerialConfigManager')
    @patch('business_logic.protocol_flow_controller.SerialManager')
    @patch('business_logic.protocol_flow_controller.ResponseValidator')
    @patch('business_logic.protocol_flow_controller.FrameDetector')
    @patch('business_logic.protocol_flow_controller.DataParser')
    @patch('business_logic.protocol_flow_controller.QueueManager')
    @patch('business_logic.command_executor.CommandExecutor')
    def test_execute_protocol_flow(self, mock_cmd_executor, mock_queue_mgr, mock_data_parser, 
                                  mock_frame_detector, mock_response_validator, mock_serial_mgr, mock_config_mgr):
        """测试协议流程执行"""
        # 设置模拟对象
        mock_config_mgr.return_value = self.mock_config_manager
        
        # 模拟指令执行器
        mock_executor_instance = Mock()
        mock_cmd_executor.return_value = mock_executor_instance
        
        # 模拟指令执行结果
        mock_result = Mock()
        mock_result.success = True
        mock_result.command_id = "disable_auto_report"
        mock_result.execution_time = 0.1
        mock_executor_instance.execute_command.return_value = mock_result
        
        # 创建控制器
        controller = ProtocolFlowController(self.config_path)
        controller.flow_state = FlowState.STOPPED  # 设置为可执行状态
        
        # 执行协议流程
        results = controller.execute_protocol_flow()
        
        # 验证结果
        self.assertEqual(len(results), 1)  # 一个步骤
        self.assertIsInstance(results[0], FlowResult)
        self.assertEqual(results[0].step_name, "传感器初始化")
        self.assertTrue(results[0].success)
        self.assertEqual(len(results[0].command_results), 2)  # 两个指令
        
        # 验证统计信息更新
        self.assertEqual(controller.stats["flows_executed"], 1)
        self.assertEqual(controller.stats["flows_successful"], 1)
        
        print("✅ 协议流程执行测试通过")
    
    @patch('business_logic.protocol_flow_controller.SerialConfigManager')
    @patch('business_logic.protocol_flow_controller.SerialManager')
    @patch('business_logic.protocol_flow_controller.ResponseValidator')
    @patch('business_logic.protocol_flow_controller.FrameDetector')
    @patch('business_logic.protocol_flow_controller.DataParser')
    @patch('business_logic.protocol_flow_controller.QueueManager')
    def test_continuous_mode(self, mock_queue_mgr, mock_data_parser, mock_frame_detector,
                            mock_response_validator, mock_serial_mgr, mock_config_mgr):
        """测试连续数据模式"""
        # 设置模拟对象
        mock_config_mgr.return_value = self.mock_config_manager
        
        mock_frame_detector_instance = Mock()
        mock_frame_detector.return_value = mock_frame_detector_instance
        
        mock_data_parser_instance = Mock()
        mock_data_parser.return_value = mock_data_parser_instance
        
        mock_queue_mgr_instance = Mock()
        mock_queue_mgr.return_value = mock_queue_mgr_instance
        
        mock_serial_instance = Mock()
        mock_serial_mgr.return_value = mock_serial_instance
        
        # 创建控制器
        controller = ProtocolFlowController(self.config_path)
        
        # 模拟数据处理回调
        received_data = []
        def data_callback(result):
            received_data.append(result)
        
        # 启动连续模式
        controller.start_continuous_mode(data_callback)
        
        # 验证状态
        self.assertEqual(controller.current_mode, WorkMode.CONTINUOUS)
        self.assertEqual(controller.flow_state, FlowState.RUNNING)
        self.assertIsNotNone(controller.continuous_thread)
        
        # 等待线程启动
        time.sleep(0.1)
        
        # 停止连续模式
        controller.stop_continuous_mode()
        
        # 验证状态
        self.assertEqual(controller.current_mode, WorkMode.IDLE)
        self.assertEqual(controller.flow_state, FlowState.STOPPED)
        
        print("✅ 连续数据模式测试通过")
    
    @patch('business_logic.protocol_flow_controller.SerialConfigManager')
    @patch('business_logic.protocol_flow_controller.SerialManager')
    @patch('business_logic.protocol_flow_controller.ResponseValidator')
    @patch('business_logic.protocol_flow_controller.FrameDetector')
    @patch('business_logic.protocol_flow_controller.DataParser')
    @patch('business_logic.protocol_flow_controller.QueueManager')
    def test_process_continuous_data(self, mock_queue_mgr, mock_data_parser, mock_frame_detector,
                                    mock_response_validator, mock_serial_mgr, mock_config_mgr):
        """测试连续数据处理"""
        # 设置模拟对象
        mock_config_mgr.return_value = self.mock_config_manager
        
        mock_frame_detector_instance = Mock()
        mock_frame_detector.return_value = mock_frame_detector_instance
        
        mock_data_parser_instance = Mock()
        mock_data_parser.return_value = mock_data_parser_instance
        
        # 模拟帧检测结果
        test_frame = b'\x55\x51\x01\x02\x03\x04\x05\x06'
        mock_frame_detector_instance.process_data.return_value = [test_frame]
        
        # 模拟数据解析结果
        mock_parsed_field = Mock()
        mock_parsed_field.name = "Roll_滚转角"
        mock_parsed_field.value = 12.34
        mock_parsed_field.unit = "°"
        mock_data_parser_instance.parse_frame.return_value = [mock_parsed_field]
        
        # 创建控制器
        controller = ProtocolFlowController(self.config_path)
        
        # 处理连续数据
        raw_data = b'\x55\x51\x01\x02\x03\x04\x05\x06\x07\x08'
        results = controller._process_continuous_data(raw_data)
        
        # 验证结果
        self.assertEqual(len(results), 1)
        self.assertIsInstance(results[0], ContinuousDataResult)
        self.assertEqual(results[0].frame_data, test_frame)
        self.assertEqual(len(results[0].parsed_fields), 1)
        self.assertEqual(results[0].frame_number, 1)
        
        # 验证统计信息
        self.assertEqual(controller.stats["frames_processed"], 1)
        self.assertEqual(controller.stats["fields_parsed"], 1)
        
        print("✅ 连续数据处理测试通过")
    
    @patch('business_logic.protocol_flow_controller.SerialConfigManager')
    @patch('business_logic.protocol_flow_controller.SerialManager')
    @patch('business_logic.protocol_flow_controller.ResponseValidator')
    @patch('business_logic.protocol_flow_controller.FrameDetector')
    @patch('business_logic.protocol_flow_controller.DataParser')
    @patch('business_logic.protocol_flow_controller.QueueManager')
    def test_get_protocol_info(self, mock_queue_mgr, mock_data_parser, mock_frame_detector,
                              mock_response_validator, mock_serial_mgr, mock_config_mgr):
        """测试获取协议信息"""
        # 设置模拟对象
        mock_config_mgr.return_value = self.mock_config_manager
        
        # 设置更多模拟数据
        self.mock_protocol_config.protocol_info.description = "IMU948传感器协议"
        self.mock_protocol_config.protocol_info.version = "1.0"
        self.mock_protocol_config.single_commands = [Mock(), Mock()]
        self.mock_protocol_config.continuous_commands = [Mock()]
        
        # 创建控制器
        controller = ProtocolFlowController(self.config_path)
        
        # 获取协议信息
        info = controller.get_protocol_info()
        
        # 验证信息
        self.assertEqual(info["name"], "IMU948测试协议")
        self.assertEqual(info["description"], "IMU948传感器协议")
        self.assertEqual(info["version"], "1.0")
        self.assertEqual(info["serial_config"]["port"], "COM6")
        self.assertEqual(info["serial_config"]["baudrate"], 115200)
        self.assertEqual(info["single_commands"], 2)
        self.assertEqual(info["continuous_commands"], 1)
        self.assertEqual(info["protocol_steps"], 1)
        self.assertTrue(info["continuous_data_configured"])
        
        print("✅ 获取协议信息测试通过")
    
    @patch('business_logic.protocol_flow_controller.SerialConfigManager')
    @patch('business_logic.protocol_flow_controller.SerialManager')
    @patch('business_logic.protocol_flow_controller.ResponseValidator')
    @patch('business_logic.protocol_flow_controller.FrameDetector')
    @patch('business_logic.protocol_flow_controller.DataParser')
    @patch('business_logic.protocol_flow_controller.QueueManager')
    def test_statistics(self, mock_queue_mgr, mock_data_parser, mock_frame_detector,
                       mock_response_validator, mock_serial_mgr, mock_config_mgr):
        """测试统计信息"""
        # 设置模拟对象
        mock_config_mgr.return_value = self.mock_config_manager
        
        # 创建控制器
        controller = ProtocolFlowController(self.config_path)
        
        # 获取初始统计信息
        stats = controller.get_statistics()
        
        # 验证初始统计信息
        self.assertEqual(stats["flows_executed"], 0)
        self.assertEqual(stats["flows_successful"], 0)
        self.assertEqual(stats["flows_failed"], 0)
        self.assertEqual(stats["commands_executed"], 0)
        self.assertEqual(stats["frames_processed"], 0)
        self.assertEqual(stats["current_mode"], WorkMode.IDLE.value)
        self.assertEqual(stats["flow_state"], FlowState.STOPPED.value)
        
        # 重置统计信息
        controller.reset_statistics()
        
        # 验证重置后的统计信息
        stats = controller.get_statistics()
        self.assertEqual(stats["flows_executed"], 0)
        self.assertEqual(stats["total_processing_time"], 0.0)
        
        print("✅ 统计信息测试通过")
    
    @patch('business_logic.protocol_flow_controller.SerialConfigManager')
    @patch('business_logic.protocol_flow_controller.SerialManager')
    @patch('business_logic.protocol_flow_controller.ResponseValidator')
    def test_error_handling(self, mock_response_validator, mock_serial_mgr, mock_config_mgr):
        """测试错误处理"""
        # 设置模拟对象
        mock_config_mgr.return_value = self.mock_config_manager
        
        # 设置无连续数据配置
        self.mock_protocol_config.continuous_data = None
        
        # 创建控制器
        controller = ProtocolFlowController(self.config_path)
        
        # 测试连续模式启动失败
        with self.assertRaises(DataProcessingError) as context:
            controller.start_continuous_mode()
        
        self.assertIn("连续数据处理组件未配置", str(context.exception))
        
        # 测试错误状态下执行流程
        controller.flow_state = FlowState.ERROR
        with self.assertRaises(DataProcessingError) as context:
            controller.execute_protocol_flow()
        
        self.assertIn("流程控制器处于错误状态", str(context.exception))
        
        print("✅ 错误处理测试通过")


def run_tests():
    """运行所有测试"""
    print("🚀 开始运行协议流程控制器测试...")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestProtocolFlowController)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 60)
    print(f"📊 测试结果统计:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功数: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败数: {len(result.failures)}")
    print(f"   错误数: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback}")
    
    if result.errors:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback}")
    
    if result.wasSuccessful():
        print(f"\n🎉 所有测试通过！协议流程控制器功能正常！")
        return True
    else:
        print(f"\n⚠️  部分测试失败，请检查代码！")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)