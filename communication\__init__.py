#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通信抽象层 - 协议无关的串口通信基础服务

本模块提供完整的串口通信抽象层，包括：
- SerialManager: 串口管理器，提供协议无关的串口底层操作
- BufferManager: 缓冲区管理器，提供线程安全的循环缓冲区
- ConnectionPool: 连接池管理器，提供连接复用和异常恢复

核心特性:
- 协议无关性: 完全不依赖具体协议实现
- 线程安全: 支持多线程环境下的安全操作
- 异常处理: 完善的错误恢复和状态管理机制
- 高性能: 优化的读写算法和连接复用机制
- 易于使用: 提供简洁的API和上下文管理器支持

作者: LD (Lead Developer)
创建时间: 2025-08-07
版本: 1.0
"""

from .serial_manager import SerialManager, ConnectionState
from .buffer_manager import BufferManager, BufferState
from .connection_pool import ConnectionPool, PoolState, ConnectionInfo

# 版本信息
__version__ = "1.0.0"
__author__ = "LD (Lead Developer)"
__email__ = "<EMAIL>"

# 导出的公共接口
__all__ = [
    # 核心管理器
    "SerialManager",
    "BufferManager",
    "ConnectionPool",

    # 状态枚举
    "ConnectionState",
    "BufferState",
    "PoolState",

    # 辅助类
    "ConnectionInfo",

    # 版本信息
    "__version__",
    "__author__",
    "__email__"
]


# 模块级别的文档字符串
def get_module_info():
    """
    获取通信抽象层模块信息

    Returns:
        dict: 包含模块信息的字典
    """
    return {
        "name": "Communication Layer",
        "version": __version__,
        "author": __author__,
        "description": "协议无关的串口通信基础服务",
        "components": {
            "SerialManager": "串口管理器 - 协议无关的串口底层操作",
            "BufferManager": "缓冲区管理器 - 线程安全的循环缓冲区",
            "ConnectionPool": "连接池管理器 - 连接复用和异常恢复"
        },
        "features": [
            "协议无关性",
            "线程安全",
            "异常处理",
            "高性能",
            "易于使用"
        ]
    }


# 便捷函数
def create_serial_manager(config, name="SerialManager"):
    """
    创建串口管理器的便捷函数

    Args:
        config: 串口配置对象
        name: 管理器名称

    Returns:
        SerialManager: 串口管理器实例
    """
    return SerialManager(config, name)


def create_buffer_manager(size=4096, warning_threshold=0.8, name="BufferManager"):
    """
    创建缓冲区管理器的便捷函数

    Args:
        size: 缓冲区大小
        warning_threshold: 警告阈值
        name: 管理器名称

    Returns:
        BufferManager: 缓冲区管理器实例
    """
    return BufferManager(size, warning_threshold, name)


def create_connection_pool(max_connections=5, max_idle_time=300.0,
                          health_check_interval=60.0, name="ConnectionPool"):
    """
    创建连接池管理器的便捷函数

    Args:
        max_connections: 最大连接数
        max_idle_time: 最大空闲时间
        health_check_interval: 健康检查间隔
        name: 连接池名称

    Returns:
        ConnectionPool: 连接池管理器实例
    """
    return ConnectionPool(max_connections, max_idle_time, health_check_interval, name)
