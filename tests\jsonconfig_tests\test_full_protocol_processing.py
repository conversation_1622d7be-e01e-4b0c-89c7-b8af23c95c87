#!/usr/bin/env python3
"""
完整协议处理测试模块

测试ProtocolProcessor的完整协议处理功能，包括：
1. 协议配置加载和管理
2. 指令发送和应答验证
3. 连续数据接收和解析
4. 协议流程管理
5. IMU948真实协议完整测试

运行方式：
python test_full_protocol_processing.py

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import sys
import os
import unittest
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from data_processing.protocol_processor import ProtocolProcessor, ProcessingMode, CommandResult, ContinuousDataResult
from utils.helper_utils import hex_to_bytes, bytes_to_hex
from utils.exceptions import DataProcessingError


class MockSerialInterface:
    """模拟串口接口"""
    
    def __init__(self):
        self.sent_data = []
        self.response_queue = []
        self.continuous_data_queue = []
    
    def send_data(self, data: bytes) -> bool:
        """模拟发送数据"""
        self.sent_data.append(data)
        print(f"📤 发送数据: {bytes_to_hex(data)}")
        return True
    
    def receive_data(self) -> bytes:
        """模拟接收应答数据"""
        if self.response_queue:
            data = self.response_queue.pop(0)
            print(f"📥 接收应答: {bytes_to_hex(data)}")
            return data
        return b''
    
    def add_response(self, response_hex: str):
        """添加模拟应答数据"""
        self.response_queue.append(hex_to_bytes(response_hex))
    
    def add_continuous_data(self, data_hex: str):
        """添加模拟连续数据"""
        self.continuous_data_queue.append(hex_to_bytes(data_hex))
    
    def get_continuous_data(self) -> bytes:
        """获取连续数据"""
        if self.continuous_data_queue:
            return self.continuous_data_queue.pop(0)
        return b''


class TestFullProtocolProcessing(unittest.TestCase):
    """测试完整协议处理功能"""
    
    def setUp(self):
        """测试前准备"""
        # 协议配置文件路径
        self.config_path = project_root / "config" / "protocols" / "imu948_example.json"
        
        # 创建协议处理器
        self.processor = ProtocolProcessor(str(self.config_path))
        
        # 创建模拟串口接口
        self.mock_serial = MockSerialInterface()
    
    def test_protocol_info_loading(self):
        """测试协议信息加载"""
        print("\n" + "="*60)
        print("🔍 测试协议信息加载")
        print("="*60)
        
        protocol_info = self.processor.get_protocol_info()
        
        print(f"📋 协议名称: {protocol_info['name']}")
        print(f"📝 协议描述: {protocol_info['description']}")
        print(f"🔢 协议版本: {protocol_info['version']}")
        print(f"🔌 串口配置: {protocol_info['serial_config']}")
        print(f"📨 单条指令数量: {protocol_info['single_commands']}")
        print(f"🔄 连续指令数量: {protocol_info['continuous_commands']}")
        print(f"📊 协议步骤数量: {protocol_info['protocol_steps']}")
        print(f"📡 连续数据配置: {'已配置' if protocol_info['continuous_data_configured'] else '未配置'}")
        
        # 验证基本信息
        self.assertEqual(protocol_info['name'], "IMU948传感器协议")
        self.assertEqual(protocol_info['single_commands'], 2)
        self.assertEqual(protocol_info['continuous_commands'], 1)
        self.assertTrue(protocol_info['continuous_data_configured'])
        
        print("✅ 协议信息加载测试通过")
    
    def test_single_command_execution(self):
        """测试单条指令执行"""
        print("\n" + "="*60)
        print("🎯 测试单条指令执行")
        print("="*60)
        
        # 准备模拟应答数据
        self.mock_serial.add_response("49 00 01 18 19 4D")  # 关闭自动上报的应答
        
        # 执行指令
        result = self.processor.execute_command(
            "disable_auto_report",
            send_callback=self.mock_serial.send_data,
            receive_callback=self.mock_serial.receive_data
        )
        
        print(f"🆔 指令ID: {result.command_id}")
        print(f"📛 指令名称: {result.command_name}")
        print(f"📤 发送数据: {bytes_to_hex(result.send_data)}")
        print(f"📥 应答数据: {bytes_to_hex(result.response_data)}")
        print(f"✅ 验证结果: {'成功' if result.validation_result.matched else '失败'}")
        print(f"⏱️ 执行耗时: {result.execution_time:.3f}s")
        print(f"🎯 执行状态: {'成功' if result.success else '失败'}")
        
        # 验证结果
        self.assertEqual(result.command_id, "disable_auto_report")
        self.assertTrue(result.success)
        self.assertTrue(result.validation_result.matched)
        
        print("✅ 单条指令执行测试通过")
    
    def test_protocol_flow_execution(self):
        """测试协议流程执行"""
        print("\n" + "="*60)
        print("🔄 测试协议流程执行")
        print("="*60)
        
        # 准备所有指令的模拟应答数据
        self.mock_serial.add_response("49 00 01 18 19 4D")  # 关闭自动上报
        self.mock_serial.add_response("49 00 01 12 13 4D")  # 设置传感器参数
        self.mock_serial.add_response("49 00 01 19 1A 4D")  # 开启自动上报
        
        # 执行完整协议流程
        results = self.processor.execute_protocol_flow(
            send_callback=self.mock_serial.send_data,
            receive_callback=self.mock_serial.receive_data
        )
        
        print(f"📊 执行步骤数量: {len(results)}")
        
        for i, result in enumerate(results, 1):
            print(f"\n📋 步骤 {i}:")
            print(f"  🆔 指令ID: {result.command_id}")
            print(f"  📛 指令名称: {result.command_name}")
            print(f"  📤 发送数据: {bytes_to_hex(result.send_data)}")
            print(f"  📥 应答数据: {bytes_to_hex(result.response_data)}")
            print(f"  ✅ 验证结果: {'成功' if result.validation_result.matched else '失败'}")
            print(f"  ⏱️ 执行耗时: {result.execution_time:.3f}s")
            print(f"  🎯 执行状态: {'成功' if result.success else '失败'}")
        
        # 验证结果
        self.assertEqual(len(results), 3)  # 应该执行3个指令
        for result in results:
            self.assertTrue(result.success)
        
        print("✅ 协议流程执行测试通过")
    
    def test_continuous_data_processing(self):
        """测试连续数据处理"""
        print("\n" + "="*60)
        print("📡 测试连续数据处理")
        print("="*60)
        
        # 准备IMU948真实连续数据帧
        test_frames = [
            "49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D",
            "49 00 13 11 C0 00 2F 41 05 06 D1 F7 09 FF FE B8 00 00 00 00 00 00 E4 4D",
            "49 00 13 11 C0 00 2F 41 05 06 DC F7 0A FF FE B8 00 00 00 00 00 00 EF 4D"
        ]
        
        all_results = []
        
        for i, frame_hex in enumerate(test_frames, 1):
            print(f"\n📊 处理第 {i} 帧数据:")
            print(f"📥 原始数据: {frame_hex}")
            
            frame_data = hex_to_bytes(frame_hex)
            results = self.processor.process_continuous_data(frame_data)
            
            for result in results:
                print(f"🔢 帧序号: {result.frame_number}")
                print(f"⏰ 时间戳: {result.timestamp:.3f}")
                print(f"📏 帧长度: {len(result.frame_data)} 字节")
                print(f"🔍 解析字段数量: {len(result.parsed_fields)}")
                
                print("📋 解析结果:")
                for field in result.parsed_fields:
                    print(f"  📊 {field.field_name}: {field.scaled_value:.6f} {field.unit} "
                          f"(原始值: {field.raw_value})")
                
                all_results.append(result)
        
        # 验证结果
        self.assertEqual(len(all_results), 3)
        for result in all_results:
            self.assertEqual(len(result.parsed_fields), 6)  # 应该解析6个字段
        
        print("✅ 连续数据处理测试通过")
    
    def test_complete_protocol_simulation(self):
        """测试完整协议模拟"""
        print("\n" + "="*60)
        print("🎭 测试完整协议模拟")
        print("="*60)
        
        print("🚀 开始完整协议处理流程...")
        
        # 第一步：执行协议流程
        print("\n📋 步骤1: 执行协议初始化流程")
        self.mock_serial.add_response("49 00 01 18 19 4D")  # 关闭自动上报
        self.mock_serial.add_response("49 00 01 12 13 4D")  # 设置传感器参数
        
        # 只执行前两个步骤（初始化）
        init_results = []
        init_results.append(self.processor.execute_command(
            "disable_auto_report",
            send_callback=self.mock_serial.send_data,
            receive_callback=self.mock_serial.receive_data
        ))
        init_results.append(self.processor.execute_command(
            "set_sensor_params",
            send_callback=self.mock_serial.send_data,
            receive_callback=self.mock_serial.receive_data
        ))
        
        print(f"✅ 初始化完成，执行了 {len(init_results)} 个指令")
        
        # 第二步：开启连续数据上报
        print("\n📋 步骤2: 开启连续数据上报")
        self.mock_serial.add_response("49 00 01 19 1A 4D")  # 开启自动上报
        
        enable_result = self.processor.execute_command(
            "enable_auto_report",
            send_callback=self.mock_serial.send_data,
            receive_callback=self.mock_serial.receive_data
        )
        
        print(f"✅ 连续数据上报已开启")
        
        # 第三步：处理连续数据流
        print("\n📋 步骤3: 处理连续数据流")
        
        # 模拟连续数据流（多帧数据）
        continuous_stream = hex_to_bytes(
            "49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D"
            "49 00 13 11 C0 00 2F 41 05 06 D1 F7 09 FF FE B8 00 00 00 00 00 00 E4 4D"
            "49 00 13 11 C0 00 2F 41 05 06 DC F7 0A FF FE B8 00 00 00 00 00 00 EF 4D"
        )
        
        continuous_results = self.processor.process_continuous_data(continuous_stream)
        
        print(f"📊 处理了 {len(continuous_results)} 帧连续数据")
        
        # 第四步：显示统计信息
        print("\n📋 步骤4: 显示处理统计信息")
        stats = self.processor.get_statistics()
        
        print("📈 处理统计信息:")
        print(f"  📨 指令执行总数: {stats['commands_executed']}")
        print(f"  ✅ 指令成功数量: {stats['commands_successful']}")
        print(f"  ❌ 指令失败数量: {stats['commands_failed']}")
        print(f"  📊 指令成功率: {stats['command_success_rate']:.1f}%")
        print(f"  📡 处理帧数量: {stats['frames_processed']}")
        print(f"  🔍 解析字段数量: {stats['fields_parsed']}")
        print(f"  ⏱️ 总处理时间: {stats['total_processing_time']:.3f}s")
        
        # 验证完整流程
        self.assertEqual(len(init_results), 2)
        self.assertTrue(enable_result.success)
        self.assertEqual(len(continuous_results), 3)
        self.assertEqual(stats['commands_executed'], 3)
        self.assertEqual(stats['commands_successful'], 3)
        self.assertEqual(stats['frames_processed'], 3)
        
        print("✅ 完整协议模拟测试通过")
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n" + "="*60)
        print("⚠️ 测试错误处理")
        print("="*60)
        
        # 测试不存在的指令
        print("🔍 测试不存在的指令:")
        with self.assertRaises(DataProcessingError):
            self.processor.execute_command("nonexistent_command")
        print("✅ 不存在指令的错误处理正确")
        
        # 测试应答验证失败
        print("\n🔍 测试应答验证失败:")
        self.mock_serial.add_response("FF FF FF FF")  # 错误的应答
        
        result = self.processor.execute_command(
            "disable_auto_report",
            send_callback=self.mock_serial.send_data,
            receive_callback=self.mock_serial.receive_data
        )
        
        self.assertFalse(result.success)
        self.assertFalse(result.validation_result.matched)
        print("✅ 应答验证失败的处理正确")
        
        print("✅ 错误处理测试通过")


def run_comprehensive_tests():
    """运行全面测试"""
    print("=" * 80)
    print("🎯 IMU948协议完整处理测试")
    print("=" * 80)
    print("📋 测试内容:")
    print("  1. 协议配置加载和管理")
    print("  2. 指令发送和应答验证")
    print("  3. 连续数据接收和解析")
    print("  4. 协议流程管理")
    print("  5. 完整协议模拟")
    print("  6. 错误处理机制")
    print("=" * 80)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    tests = unittest.TestLoader().loadTestsFromTestCase(TestFullProtocolProcessing)
    test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=0)  # 设置为0以减少unittest的输出
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("📊 完整协议处理测试结果汇总")
    print("=" * 80)
    print(f"🔢 运行测试数量: {result.testsRun}")
    print(f"✅ 成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ 失败: {len(result.failures)}")
    print(f"⚠️ 错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n⚠️ 错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n🎯 测试通过率: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("\n🎉 所有测试都通过了！")
        print("✅ IMU948协议完整处理功能验证成功")
        print("🚀 协议处理器已准备就绪，支持完整的自由串口协议处理")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
