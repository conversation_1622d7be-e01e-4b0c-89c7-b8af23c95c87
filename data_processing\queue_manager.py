"""
队列管理器模块 - 基于JSON配置的动态队列管理引擎

该模块实现了完全基于JSON配置驱动的队列管理功能，支持任意自由串口协议的数据队列管理。
核心特性：
- 动态配置驱动：所有队列管理逻辑完全由JSON配置决定，零硬编码
- 高性能队列：支持高效的队列操作和批量处理
- 队列监控：实时监控队列使用率和状态
- 线程安全：支持多线程环境下的安全操作
- 协议无关：完全不依赖具体协议实现

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import logging
import threading
import time
from typing import Any, Dict, List, Optional, Union
from queue import Queue, Empty, Full
from dataclasses import dataclass
from enum import Enum
from utils.exceptions import DataProcessingError


class QueueStatus(Enum):
    """队列状态枚举"""
    EMPTY = "empty"           # 空队列
    NORMAL = "normal"         # 正常状态
    WARNING = "warning"       # 警告状态（接近满）
    FULL = "full"            # 队列已满


@dataclass
class QueueConfig:
    """队列配置数据类"""
    queue_size: int           # 队列大小
    warning_threshold: float  # 警告阈值（0-1）
    batch_size: int          # 批处理大小
    
    def __post_init__(self):
        """初始化后验证"""
        if self.queue_size <= 0:
            raise ValueError("队列大小必须大于0")
        if not 0 < self.warning_threshold < 1:
            raise ValueError("警告阈值必须在0和1之间")
        if self.batch_size <= 0:
            raise ValueError("批处理大小必须大于0")
        if self.batch_size > self.queue_size:
            raise ValueError("批处理大小不能大于队列大小")


@dataclass
class QueueItem:
    """队列项数据类"""
    data: Any                # 数据内容
    timestamp: float         # 时间戳
    item_type: str          # 数据类型
    metadata: Dict[str, Any] # 元数据


class QueueManager:
    """
    基于JSON配置的动态队列管理器
    
    核心功能：
    1. 高性能队列操作
    2. 队列状态监控
    3. 批量处理支持
    4. 线程安全操作
    """
    
    def __init__(self, config: QueueConfig):
        """
        初始化队列管理器
        
        Args:
            config: 队列配置对象
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.QueueManager")
        
        # 创建队列
        self.queue = Queue(maxsize=config.queue_size)
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            "items_added": 0,
            "items_removed": 0,
            "items_dropped": 0,
            "batch_operations": 0,
            "queue_full_events": 0,
            "queue_empty_events": 0,
            "max_queue_size_reached": 0
        }
        
        # 状态监控
        self._last_status = QueueStatus.EMPTY
        self._status_change_callbacks = []
        
        self.logger.info(f"队列管理器初始化完成 - 队列大小: {config.queue_size}, "
                        f"警告阈值: {config.warning_threshold}, 批处理大小: {config.batch_size}")
    
    def put(self, data: Any, item_type: str = "data", metadata: Optional[Dict[str, Any]] = None, 
            block: bool = True, timeout: Optional[float] = None) -> bool:
        """
        向队列添加数据
        
        Args:
            data: 数据内容
            item_type: 数据类型
            metadata: 元数据
            block: 是否阻塞
            timeout: 超时时间
            
        Returns:
            是否成功添加
        """
        if metadata is None:
            metadata = {}
        
        item = QueueItem(
            data=data,
            timestamp=time.time(),
            item_type=item_type,
            metadata=metadata
        )
        
        try:
            with self._lock:
                self.queue.put(item, block=block, timeout=timeout)
                self.stats["items_added"] += 1
                
                # 检查队列状态
                self._check_queue_status()
                
                return True
                
        except Full:
            self.logger.warning("队列已满，无法添加数据")
            self.stats["items_dropped"] += 1
            self.stats["queue_full_events"] += 1
            return False
        except Exception as e:
            self.logger.error(f"添加数据到队列失败: {str(e)}")
            return False
    
    def get(self, block: bool = True, timeout: Optional[float] = None) -> Optional[QueueItem]:
        """
        从队列获取数据
        
        Args:
            block: 是否阻塞
            timeout: 超时时间
            
        Returns:
            队列项或None
        """
        try:
            with self._lock:
                item = self.queue.get(block=block, timeout=timeout)
                self.stats["items_removed"] += 1
                
                # 检查队列状态
                self._check_queue_status()
                
                return item
                
        except Empty:
            if block:
                self.logger.debug("队列为空，获取数据超时")
                self.stats["queue_empty_events"] += 1
            return None
        except Exception as e:
            self.logger.error(f"从队列获取数据失败: {str(e)}")
            return None
    
    def get_batch(self, max_items: Optional[int] = None, timeout: Optional[float] = None) -> List[QueueItem]:
        """
        批量获取数据
        
        Args:
            max_items: 最大获取数量，None表示使用配置的批处理大小
            timeout: 超时时间
            
        Returns:
            队列项列表
        """
        if max_items is None:
            max_items = self.config.batch_size
        
        items = []
        start_time = time.time()
        
        try:
            with self._lock:
                while len(items) < max_items:
                    # 检查超时
                    if timeout is not None:
                        elapsed = time.time() - start_time
                        if elapsed >= timeout:
                            break
                        remaining_timeout = timeout - elapsed
                    else:
                        remaining_timeout = None
                    
                    # 获取数据项
                    item = self.get(block=len(items) == 0, timeout=remaining_timeout)
                    if item is None:
                        break
                    
                    items.append(item)
                
                if items:
                    self.stats["batch_operations"] += 1
                
                return items
                
        except Exception as e:
            self.logger.error(f"批量获取数据失败: {str(e)}")
            return items
    
    def put_batch(self, items: List[Any], item_type: str = "data", 
                  metadata: Optional[Dict[str, Any]] = None) -> int:
        """
        批量添加数据
        
        Args:
            items: 数据列表
            item_type: 数据类型
            metadata: 元数据
            
        Returns:
            成功添加的数量
        """
        if not items:
            return 0
        
        added_count = 0
        
        try:
            with self._lock:
                for data in items:
                    if self.put(data, item_type, metadata, block=False):
                        added_count += 1
                    else:
                        break  # 队列满了，停止添加
                
                if added_count > 0:
                    self.stats["batch_operations"] += 1
                
                return added_count
                
        except Exception as e:
            self.logger.error(f"批量添加数据失败: {str(e)}")
            return added_count
    
    def clear(self) -> int:
        """
        清空队列
        
        Returns:
            清除的项目数量
        """
        cleared_count = 0
        
        try:
            with self._lock:
                while not self.queue.empty():
                    try:
                        self.queue.get_nowait()
                        cleared_count += 1
                    except Empty:
                        break
                
                # 检查队列状态
                self._check_queue_status()
                
                self.logger.info(f"队列已清空，清除了 {cleared_count} 个项目")
                return cleared_count
                
        except Exception as e:
            self.logger.error(f"清空队列失败: {str(e)}")
            return cleared_count
    
    def get_status(self) -> QueueStatus:
        """获取队列状态"""
        with self._lock:
            current_size = self.queue.qsize()
            max_size = self.config.queue_size
            
            if current_size == 0:
                return QueueStatus.EMPTY
            elif current_size >= max_size:
                return QueueStatus.FULL
            elif current_size >= max_size * self.config.warning_threshold:
                return QueueStatus.WARNING
            else:
                return QueueStatus.NORMAL
    
    def get_usage_info(self) -> Dict[str, Any]:
        """获取队列使用信息"""
        with self._lock:
            current_size = self.queue.qsize()
            max_size = self.config.queue_size
            usage_rate = current_size / max_size if max_size > 0 else 0
            
            return {
                "current_size": current_size,
                "max_size": max_size,
                "usage_rate": usage_rate,
                "usage_percentage": usage_rate * 100,
                "available_space": max_size - current_size,
                "status": self.get_status().value,
                "is_empty": current_size == 0,
                "is_full": current_size >= max_size,
                "is_warning": usage_rate >= self.config.warning_threshold
            }
    
    def _check_queue_status(self):
        """检查队列状态变化"""
        current_status = self.get_status()
        
        if current_status != self._last_status:
            self.logger.debug(f"队列状态变化: {self._last_status.value} -> {current_status.value}")
            
            # 更新统计信息
            if current_status == QueueStatus.FULL:
                self.stats["max_queue_size_reached"] += 1
            
            # 调用状态变化回调
            for callback in self._status_change_callbacks:
                try:
                    callback(self._last_status, current_status)
                except Exception as e:
                    self.logger.error(f"状态变化回调执行失败: {str(e)}")
            
            self._last_status = current_status
    
    def add_status_change_callback(self, callback):
        """添加状态变化回调函数"""
        self._status_change_callbacks.append(callback)
    
    def remove_status_change_callback(self, callback):
        """移除状态变化回调函数"""
        if callback in self._status_change_callbacks:
            self._status_change_callbacks.remove(callback)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            stats = self.stats.copy()
            usage_info = self.get_usage_info()
            
            # 计算额外统计信息
            total_operations = stats["items_added"] + stats["items_removed"]
            if total_operations > 0:
                stats["drop_rate"] = stats["items_dropped"] / stats["items_added"] * 100 if stats["items_added"] > 0 else 0
            else:
                stats["drop_rate"] = 0.0
            
            # 合并使用信息
            stats.update(usage_info)
            
            return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        with self._lock:
            self.stats = {
                "items_added": 0,
                "items_removed": 0,
                "items_dropped": 0,
                "batch_operations": 0,
                "queue_full_events": 0,
                "queue_empty_events": 0,
                "max_queue_size_reached": 0
            }
            self.logger.info("统计信息已重置")
    
    @classmethod
    def create_from_config(cls, config_dict: Dict[str, Any]) -> 'QueueManager':
        """
        从配置字典创建队列管理器
        
        Args:
            config_dict: 配置字典
            
        Returns:
            队列管理器实例
        """
        try:
            config = QueueConfig(
                queue_size=config_dict["response_queue_size"],
                warning_threshold=config_dict["queue_warning_threshold"],
                batch_size=config_dict["batch_processing_size"]
            )
            return cls(config)
        except Exception as e:
            raise DataProcessingError(
                f"队列管理器配置创建失败: {str(e)}",
                error_code="QUEUE_CONFIG_ERROR",
                details={"config": config_dict}
            )
