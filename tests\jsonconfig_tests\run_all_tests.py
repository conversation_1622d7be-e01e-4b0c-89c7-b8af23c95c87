#!/usr/bin/env python3
"""
运行所有数据处理层测试的脚本

运行方式：
python run_all_tests.py

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def run_test_file(test_file):
    """运行单个测试文件"""
    print(f"\n{'='*60}")
    print(f"运行测试: {test_file}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run([
            sys.executable, test_file
        ], cwd=str(project_root), capture_output=True, text=True, timeout=120)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print(f"✓ {test_file} 测试通过")
            return True
        else:
            print(f"✗ {test_file} 测试失败 (返回码: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"✗ {test_file} 测试超时")
        return False
    except Exception as e:
        print(f"✗ {test_file} 测试出错: {e}")
        return False

def main():
    """主函数"""
    print("数据处理层全面测试套件")
    print("="*60)
    
    # 测试文件列表
    test_files = [
        "tests/jsonconfig_tests/test_frame_detector.py",
        "tests/jsonconfig_tests/test_data_parser.py",
        "tests/jsonconfig_tests/test_response_validator.py",
        "tests/jsonconfig_tests/test_queue_manager.py"
    ]
    
    results = []
    
    for test_file in test_files:
        success = run_test_file(test_file)
        results.append((test_file, success))
    
    # 输出总结
    print(f"\n{'='*60}")
    print("测试结果总结")
    print(f"{'='*60}")
    
    passed = 0
    failed = 0
    
    for test_file, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_file}: {status}")
        if success:
            passed += 1
        else:
            failed += 1
    
    total = len(results)
    success_rate = (passed / total * 100) if total > 0 else 0
    
    print(f"\n总计: {total} 个测试文件")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"成功率: {success_rate:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有测试都通过了！")
        print("\n阶段3：数据处理层开发 - 完成 ✅")
        print("\n核心组件开发完成：")
        print("  ✅ FrameDetector - 帧检测器")
        print("  ✅ DataParser - 数据解析器") 
        print("  ✅ ResponseValidator - 应答验证器")
        print("  ✅ QueueManager - 队列管理器")
        print("\n所有组件都通过了100%的测试验证！")
        return True
    else:
        print(f"\n❌ 有 {failed} 个测试文件失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
