#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自由串口协议配置管理器
专门管理自由串口协议配置文件的加载、验证和访问
严格按照JSON配置规范文档设计

作者: LD (Lead Developer)
创建时间: 2025-08-06
版本: 1.0
"""

import json
import threading
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from .constants import DEFAULT_PROTOCOLS_DIR
from .exceptions import (
    ConfigFileNotFoundError, ConfigParsingError, ConfigValidationError
)


@dataclass
class ProtocolInfo:
    """协议信息"""
    name: str
    description: str = ""
    version: str = "1.0"


@dataclass
class SerialConfig:
    """串口配置"""
    port: str
    baudrate: int
    databits: int
    parity: str
    stopbits: float
    timeout: float


@dataclass
class ResponseValidation:
    """应答验证配置"""
    type: str  # "exact" or "regex"
    pattern: str
    timeout: float
    retry_count: int


@dataclass
class Command:
    """指令定义"""
    id: str
    name: str
    send: str
    response_validation: ResponseValidation


@dataclass
class ProtocolStep:
    """协议步骤"""
    name: str
    type: str  # "single_command" or "continuous_command"
    commands: List[str]
    auto_start: bool = False


@dataclass
class ProtocolFlow:
    """协议流程"""
    steps: List[ProtocolStep]


@dataclass
class FrameDetection:
    """帧检测配置"""
    header: str
    tail: str
    min_length: int
    max_length: int


@dataclass
class DataField:
    """数据字段解析配置"""
    name: str
    offset: int
    length: int
    data_type: str
    endian: str
    scale_factor: float
    unit: str = ""


@dataclass
class ContinuousData:
    """连续数据配置"""
    frame_detection: FrameDetection
    data_parsing: List[DataField]


@dataclass
class SerialProtocolConfig:
    """完整的串口协议配置"""
    protocol_info: ProtocolInfo
    serial_config: SerialConfig
    protocol_flow: ProtocolFlow
    single_commands: List[Command]
    continuous_commands: List[Command]
    continuous_data: Optional[ContinuousData] = None


class SerialConfigManager:
    """自由串口协议配置管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        import logging
        self._logger = logging.getLogger("datastudio.serial_config")
        self._config: Optional[SerialProtocolConfig] = None
        self._config_file_path: Optional[Path] = None
        self._lock = threading.RLock()
    
    def load_config(self, config_path: str) -> SerialProtocolConfig:
        """
        加载自由串口协议配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            串口协议配置对象
        """
        with self._lock:
            config_file = Path(config_path)
            if not config_file.exists():
                raise ConfigFileNotFoundError(str(config_file))
            
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 验证和解析配置
                self._config = self._parse_config(config_data)
                self._config_file_path = config_file
                
                self._logger.info(f"串口协议配置加载成功: {config_file}")
                
            except json.JSONDecodeError as e:
                raise ConfigParsingError(f"JSON解析失败: {e}", str(config_file))
            except Exception as e:
                self._logger.error(f"加载串口协议配置失败: {e}")
                raise
            
            return self._config
    
    def _parse_config(self, config_data: Dict[str, Any]) -> SerialProtocolConfig:
        """解析配置数据"""
        # 解析协议信息
        protocol_info_data = config_data.get('protocol_info', {})
        protocol_info = ProtocolInfo(
            name=protocol_info_data.get('name', ''),
            description=protocol_info_data.get('description', ''),
            version=protocol_info_data.get('version', '1.0')
        )
        
        # 解析串口配置
        serial_config_data = config_data.get('serial_config', {})
        serial_config = SerialConfig(
            port=serial_config_data.get('port', ''),
            baudrate=serial_config_data.get('baudrate', 9600),
            databits=serial_config_data.get('databits', 8),
            parity=serial_config_data.get('parity', 'none'),
            stopbits=serial_config_data.get('stopbits', 1),
            timeout=serial_config_data.get('timeout', 1.0)
        )
        
        # 解析协议流程
        protocol_flow_data = config_data.get('protocol_flow', {})
        steps_data = protocol_flow_data.get('steps', [])
        steps = []
        for step_data in steps_data:
            step = ProtocolStep(
                name=step_data.get('name', ''),
                type=step_data.get('type', ''),
                commands=step_data.get('commands', []),
                auto_start=step_data.get('auto_start', False)
            )
            steps.append(step)
        
        protocol_flow = ProtocolFlow(steps=steps)
        
        # 解析指令定义
        commands_data = config_data.get('commands', {})
        
        # 解析单条指令
        single_commands = []
        for cmd_data in commands_data.get('single', []):
            response_val_data = cmd_data.get('response_validation', {})
            response_validation = ResponseValidation(
                type=response_val_data.get('type', 'exact'),
                pattern=response_val_data.get('pattern', ''),
                timeout=response_val_data.get('timeout', 1.0),
                retry_count=response_val_data.get('retry_count', 1)
            )
            
            command = Command(
                id=cmd_data.get('id', ''),
                name=cmd_data.get('name', ''),
                send=cmd_data.get('send', ''),
                response_validation=response_validation
            )
            single_commands.append(command)
        
        # 解析连续指令
        continuous_commands = []
        for cmd_data in commands_data.get('continuous', []):
            response_val_data = cmd_data.get('response_validation', {})
            response_validation = ResponseValidation(
                type=response_val_data.get('type', 'exact'),
                pattern=response_val_data.get('pattern', ''),
                timeout=response_val_data.get('timeout', 1.0),
                retry_count=response_val_data.get('retry_count', 1)
            )
            
            command = Command(
                id=cmd_data.get('id', ''),
                name=cmd_data.get('name', ''),
                send=cmd_data.get('send', ''),
                response_validation=response_validation
            )
            continuous_commands.append(command)
        
        # 解析连续数据配置（可选）
        continuous_data = None
        if 'continuous_data' in config_data:
            continuous_data_config = config_data['continuous_data']
            
            # 解析帧检测配置
            frame_detection_data = continuous_data_config.get('frame_detection', {})
            frame_detection = FrameDetection(
                header=frame_detection_data.get('header', ''),
                tail=frame_detection_data.get('tail', ''),
                min_length=frame_detection_data.get('min_length', 1),
                max_length=frame_detection_data.get('max_length', 1024)
            )
            
            # 解析数据解析配置
            data_parsing_list = []
            for field_data in continuous_data_config.get('data_parsing', []):
                data_field = DataField(
                    name=field_data.get('name', ''),
                    offset=field_data.get('offset', 0),
                    length=field_data.get('length', 1),
                    data_type=field_data.get('data_type', 'int8'),
                    endian=field_data.get('endian', 'big'),
                    scale_factor=field_data.get('scale_factor', 1.0),
                    unit=field_data.get('unit', '')
                )
                data_parsing_list.append(data_field)
            
            continuous_data = ContinuousData(
                frame_detection=frame_detection,
                data_parsing=data_parsing_list
            )
        
        return SerialProtocolConfig(
            protocol_info=protocol_info,
            serial_config=serial_config,
            protocol_flow=protocol_flow,
            single_commands=single_commands,
            continuous_commands=continuous_commands,
            continuous_data=continuous_data
        )

    def get_config(self) -> Optional[SerialProtocolConfig]:
        """获取当前配置"""
        with self._lock:
            return self._config

    def get_protocol_info(self) -> Optional[ProtocolInfo]:
        """获取协议信息"""
        with self._lock:
            return self._config.protocol_info if self._config else None

    def get_serial_config(self) -> Optional[SerialConfig]:
        """获取串口配置"""
        with self._lock:
            return self._config.serial_config if self._config else None

    def get_protocol_flow(self) -> Optional[ProtocolFlow]:
        """获取协议流程"""
        with self._lock:
            return self._config.protocol_flow if self._config else None

    def get_single_commands(self) -> List[Command]:
        """获取单条指令列表"""
        with self._lock:
            return self._config.single_commands if self._config else []

    def get_continuous_commands(self) -> List[Command]:
        """获取连续指令列表"""
        with self._lock:
            return self._config.continuous_commands if self._config else []

    def get_command_by_id(self, command_id: str) -> Optional[Command]:
        """根据ID获取指令"""
        with self._lock:
            if not self._config:
                return None

            # 在单条指令中查找
            for cmd in self._config.single_commands:
                if cmd.id == command_id:
                    return cmd

            # 在连续指令中查找
            for cmd in self._config.continuous_commands:
                if cmd.id == command_id:
                    return cmd

            return None

    def get_continuous_data_config(self) -> Optional[ContinuousData]:
        """获取连续数据配置"""
        with self._lock:
            return self._config.continuous_data if self._config else None

    def get_frame_detection_config(self) -> Optional[FrameDetection]:
        """获取帧检测配置"""
        with self._lock:
            if self._config and self._config.continuous_data:
                return self._config.continuous_data.frame_detection
            return None

    def get_data_parsing_config(self) -> List[DataField]:
        """获取数据解析配置"""
        with self._lock:
            if self._config and self._config.continuous_data:
                return self._config.continuous_data.data_parsing
            return []

    def get_config_file_path(self) -> Optional[Path]:
        """获取当前配置文件路径"""
        with self._lock:
            return self._config_file_path

    def is_config_loaded(self) -> bool:
        """检查配置是否已加载"""
        with self._lock:
            return self._config is not None

    def reload_config(self) -> Optional[SerialProtocolConfig]:
        """重新加载配置"""
        with self._lock:
            if self._config_file_path:
                return self.load_config(str(self._config_file_path))
            return self._config

    def _reset_for_testing(self) -> None:
        """
        重置配置状态（仅用于测试）

        清空当前加载的配置和文件路径，将管理器恢复到初始状态。
        这个方法仅应在单元测试中使用，以确保测试的独立性。
        """
        with self._lock:
            self._config = None
            self._config_file_path = None
            self._logger.debug("配置状态已重置（测试模式）")

    def validate_protocol_flow_references(self) -> List[str]:
        """验证协议流程中的指令引用是否存在"""
        errors = []
        if not self._config:
            return errors

        # 收集所有可用的指令ID
        available_commands = set()
        for cmd in self._config.single_commands:
            available_commands.add(cmd.id)
        for cmd in self._config.continuous_commands:
            available_commands.add(cmd.id)

        # 检查协议流程中的引用
        for step in self._config.protocol_flow.steps:
            for cmd_id in step.commands:
                if cmd_id not in available_commands:
                    errors.append(f"步骤 '{step.name}' 引用了不存在的指令ID: {cmd_id}")

            # 检查步骤类型与指令类型的匹配
            if step.type == "single_command":
                for cmd_id in step.commands:
                    cmd = self.get_command_by_id(cmd_id)
                    if cmd and cmd not in self._config.single_commands:
                        errors.append(f"步骤 '{step.name}' 类型为single_command，但引用了continuous类型的指令: {cmd_id}")

            elif step.type == "continuous_command":
                for cmd_id in step.commands:
                    cmd = self.get_command_by_id(cmd_id)
                    if cmd and cmd not in self._config.continuous_commands:
                        errors.append(f"步骤 '{step.name}' 类型为continuous_command，但引用了single类型的指令: {cmd_id}")

        return errors


# 全局串口协议配置管理器实例
_serial_config_manager = SerialConfigManager()


def get_serial_config_manager() -> SerialConfigManager:
    """获取串口协议配置管理器实例"""
    return _serial_config_manager
