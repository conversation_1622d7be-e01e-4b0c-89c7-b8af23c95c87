#!/usr/bin/env python3
"""
帧检测器测试模块

测试FrameDetector的所有功能，包括：
1. 基于JSON配置的动态帧检测
2. 状态机实现的帧检测算法
3. 数据粘连和分片处理
4. IMU948真实协议测试

运行方式：
python test_frame_detector.py

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import sys
import os
import unittest
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from data_processing.frame_detector import FrameDetector, FrameDetectionConfig, FrameDetectionState
from utils.helper_utils import hex_to_bytes, bytes_to_hex
from utils.exceptions import DataProcessingError


class TestFrameDetectionConfig(unittest.TestCase):
    """测试帧检测配置类"""
    
    def test_from_json_config_valid(self):
        """测试有效的JSON配置解析"""
        config_data = {
            "header": "49",
            "tail": "4D",
            "min_length": 24,
            "max_length": 24
        }
        
        config = FrameDetectionConfig.from_json_config(config_data)
        
        self.assertEqual(config.header, b'\x49')
        self.assertEqual(config.tail, b'\x4D')
        self.assertEqual(config.min_length, 24)
        self.assertEqual(config.max_length, 24)
    
    def test_from_json_config_complex_header_tail(self):
        """测试复杂帧头帧尾的配置解析"""
        config_data = {
            "header": "AA BB CC",
            "tail": "DD EE",
            "min_length": 10,
            "max_length": 50
        }
        
        config = FrameDetectionConfig.from_json_config(config_data)
        
        self.assertEqual(config.header, b'\xAA\xBB\xCC')
        self.assertEqual(config.tail, b'\xDD\xEE')
        self.assertEqual(config.min_length, 10)
        self.assertEqual(config.max_length, 50)
    
    def test_from_json_config_invalid_length(self):
        """测试无效长度配置"""
        config_data = {
            "header": "49",
            "tail": "4D",
            "min_length": 0,
            "max_length": 24
        }
        
        with self.assertRaises(DataProcessingError):
            FrameDetectionConfig.from_json_config(config_data)
    
    def test_from_json_config_min_greater_than_max(self):
        """测试最小长度大于最大长度"""
        config_data = {
            "header": "49",
            "tail": "4D",
            "min_length": 30,
            "max_length": 24
        }
        
        with self.assertRaises(DataProcessingError):
            FrameDetectionConfig.from_json_config(config_data)


class TestFrameDetector(unittest.TestCase):
    """测试帧检测器类"""
    
    def setUp(self):
        """测试前准备"""
        # IMU948配置
        self.imu_config = FrameDetectionConfig(
            header=b'\x49',
            tail=b'\x4D',
            min_length=24,
            max_length=24
        )
        self.imu_detector = FrameDetector(self.imu_config)
        
        # 通用测试配置
        self.test_config = FrameDetectionConfig(
            header=b'\xAA\xBB',
            tail=b'\xCC\xDD',
            min_length=8,
            max_length=32
        )
        self.test_detector = FrameDetector(self.test_config)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.imu_detector.state, FrameDetectionState.SEARCHING_HEADER)
        self.assertEqual(len(self.imu_detector.buffer), 0)
        self.assertEqual(len(self.imu_detector.current_frame), 0)
        
        stats = self.imu_detector.get_statistics()
        self.assertEqual(stats["frames_detected"], 0)
        self.assertEqual(stats["total_bytes_processed"], 0)
    
    def test_single_complete_frame(self):
        """测试单个完整帧检测"""
        # IMU948真实数据帧
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        
        frames = self.imu_detector.process_data(frame_data)
        
        self.assertEqual(len(frames), 1)
        self.assertEqual(frames[0], frame_data)
        
        stats = self.imu_detector.get_statistics()
        self.assertEqual(stats["frames_detected"], 1)
        self.assertEqual(stats["total_bytes_processed"], 24)
    
    def test_multiple_frames_in_single_data(self):
        """测试单次数据中的多个帧"""
        frame1 = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        frame2 = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        combined_data = frame1 + frame2
        
        frames = self.imu_detector.process_data(combined_data)
        
        self.assertEqual(len(frames), 2)
        self.assertEqual(frames[0], frame1)
        self.assertEqual(frames[1], frame2)
        
        stats = self.imu_detector.get_statistics()
        self.assertEqual(stats["frames_detected"], 2)
    
    def test_fragmented_frame(self):
        """测试分片帧处理"""
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        
        # 分成三部分发送
        part1 = frame_data[:8]
        part2 = frame_data[8:16]
        part3 = frame_data[16:]
        
        frames1 = self.imu_detector.process_data(part1)
        frames2 = self.imu_detector.process_data(part2)
        frames3 = self.imu_detector.process_data(part3)
        
        # 前两部分不应该产生完整帧
        self.assertEqual(len(frames1), 0)
        self.assertEqual(len(frames2), 0)
        
        # 第三部分应该产生完整帧
        self.assertEqual(len(frames3), 1)
        self.assertEqual(frames3[0], frame_data)
    
    def test_data_with_noise_before_header(self):
        """测试帧头前有噪声数据"""
        noise = hex_to_bytes("FF EE DD CC BB AA")
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        combined_data = noise + frame_data
        
        frames = self.imu_detector.process_data(combined_data)
        
        self.assertEqual(len(frames), 1)
        self.assertEqual(frames[0], frame_data)
    
    def test_invalid_frame_length(self):
        """测试无效帧长度"""
        # 创建一个长度不符合要求的帧（IMU948要求24字节）
        invalid_frame = hex_to_bytes("49 00 13 11 C0 4D")  # 只有6字节
        
        frames = self.imu_detector.process_data(invalid_frame)
        
        self.assertEqual(len(frames), 0)
        
        stats = self.imu_detector.get_statistics()
        self.assertEqual(stats["invalid_frames"], 1)
    
    def test_frame_too_long(self):
        """测试帧长度超过最大限制"""
        # 使用通用测试配置（最大32字节）
        long_data = b'\xAA\xBB' + b'\x00' * 50 + b'\xCC\xDD'  # 54字节，超过32字节限制
        
        frames = self.test_detector.process_data(long_data)
        
        self.assertEqual(len(frames), 0)
        
        stats = self.test_detector.get_statistics()
        self.assertEqual(stats["invalid_frames"], 1)
    
    def test_statistics_tracking(self):
        """测试统计信息跟踪"""
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        
        # 处理多个帧
        self.imu_detector.process_data(frame_data)
        self.imu_detector.process_data(frame_data)
        
        stats = self.imu_detector.get_statistics()
        self.assertEqual(stats["frames_detected"], 2)
        self.assertEqual(stats["total_bytes_processed"], 48)
        self.assertEqual(stats["header_matches"], 2)
        self.assertEqual(stats["tail_matches"], 2)
    
    def test_reset_functionality(self):
        """测试重置功能"""
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        
        # 处理一些数据
        self.imu_detector.process_data(frame_data)
        
        # 重置
        self.imu_detector.reset()
        
        # 检查状态
        self.assertEqual(self.imu_detector.state, FrameDetectionState.SEARCHING_HEADER)
        self.assertEqual(len(self.imu_detector.buffer), 0)
        
        stats = self.imu_detector.get_statistics()
        self.assertEqual(stats["frames_detected"], 0)


class TestIMU948RealProtocol(unittest.TestCase):
    """测试IMU948真实协议"""
    
    def setUp(self):
        """加载IMU948配置"""
        config_path = project_root / "config" / "protocols" / "imu948_example.json"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            self.protocol_config = json.load(f)
        
        frame_config_data = self.protocol_config["continuous_data"]["frame_detection"]
        self.frame_config = FrameDetectionConfig.from_json_config(frame_config_data)
        self.detector = FrameDetector(self.frame_config)
    
    def test_imu948_frame_detection_config(self):
        """测试IMU948帧检测配置"""
        self.assertEqual(self.frame_config.header, b'\x49')
        self.assertEqual(self.frame_config.tail, b'\x4D')
        self.assertEqual(self.frame_config.min_length, 24)
        self.assertEqual(self.frame_config.max_length, 24)
    
    def test_imu948_real_frame_data(self):
        """测试IMU948真实帧数据"""
        # IMU948真实数据帧示例
        real_frames = [
            "49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D",
            "49 00 13 11 C0 00 2F 41 05 06 D1 F7 09 FF FE B8 00 00 00 00 00 00 E4 4D",
            "49 00 13 11 C0 00 2F 41 05 06 DC F7 0A FF FE B8 00 00 00 00 00 00 EF 4D"
        ]
        
        for frame_hex in real_frames:
            frame_data = hex_to_bytes(frame_hex)
            frames = self.detector.process_data(frame_data)
            
            self.assertEqual(len(frames), 1, f"帧检测失败: {frame_hex}")
            self.assertEqual(frames[0], frame_data, f"帧数据不匹配: {frame_hex}")
    
    def test_imu948_continuous_data_stream(self):
        """测试IMU948连续数据流"""
        # 模拟连续数据流
        stream_data = hex_to_bytes(
            "49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D"
            "49 00 13 11 C0 00 2F 41 05 06 D1 F7 09 FF FE B8 00 00 00 00 00 00 E4 4D"
            "49 00 13 11 C0 00 2F 41 05 06 DC F7 0A FF FE B8 00 00 00 00 00 00 EF 4D"
        )
        
        frames = self.detector.process_data(stream_data)
        
        self.assertEqual(len(frames), 3)
        self.assertEqual(len(frames[0]), 24)
        self.assertEqual(len(frames[1]), 24)
        self.assertEqual(len(frames[2]), 24)


def run_comprehensive_tests():
    """运行全面测试"""
    print("=" * 60)
    print("帧检测器全面测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加所有测试类
    test_classes = [
        TestFrameDetectionConfig,
        TestFrameDetector,
        TestIMU948RealProtocol
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"运行测试数量: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n测试通过率: {success_rate:.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
