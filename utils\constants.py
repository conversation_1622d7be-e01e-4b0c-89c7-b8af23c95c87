#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统核心常量定义
定义数据采集系统中不可配置的核心常量

作者: LD (Lead Developer)
创建时间: 2025-08-05
版本: 1.0
"""

from enum import Enum, IntEnum
from typing import Dict

# ============================================================================
# 系统版本信息
# ============================================================================
SYSTEM_VERSION = "1.0.0"
SYSTEM_NAME = "DataStudio"
SYSTEM_DESCRIPTION = "基于五层架构的动态配置数据采集系统"

# ============================================================================
# 文件路径常量
# ============================================================================
DEFAULT_CONFIG_DIR = "config"
DEFAULT_PROTOCOLS_DIR = "config/protocols"
DEFAULT_LOGS_DIR = "logs"
DEFAULT_TESTS_DIR = "tests"

# 配置文件扩展名
CONFIG_FILE_EXTENSION = ".json"
LOG_FILE_EXTENSION = ".log"

# ============================================================================
# 串口通信支持的参数范围（用于验证）
# ============================================================================
class SerialLimits:
    """串口参数限制"""
    # 支持的波特率
    SUPPORTED_BAUDRATES = [
        1200, 2400, 4800, 9600, 14400, 19200, 38400,
        57600, 115200, 230400, 460800, 921600
    ]

    # 支持的数据位
    SUPPORTED_DATABITS = [5, 6, 7, 8]

    # 支持的校验位
    SUPPORTED_PARITIES = ['none', 'even', 'odd', 'mark', 'space']

    # 支持的停止位
    SUPPORTED_STOPBITS = [1, 1.5, 2]



# ============================================================================
# 错误类型枚举（系统核心）
# ============================================================================
class ErrorType(IntEnum):
    """错误类型枚举"""
    COMMUNICATION_ERROR = 1  # 通信错误
    FRAME_DETECTION_ERROR = 2  # 帧检测错误
    DATA_PARSING_ERROR = 3  # 数据解析错误
    VALIDATION_ERROR = 4  # 验证错误
    CONFIGURATION_ERROR = 5  # 配置错误
    SYSTEM_ERROR = 6  # 系统错误

# ============================================================================
# 日志级别映射（系统核心）
# ============================================================================
LOG_LEVELS: Dict[str, int] = {
    "DEBUG": 10,
    "INFO": 20,
    "WARNING": 30,
    "ERROR": 40,
    "CRITICAL": 50,
}

# ============================================================================
# 配置验证限制（系统核心）
# ============================================================================
class ConfigLimits:
    """配置验证限制"""
    MIN_PORT_NUMBER = 1
    MAX_PORT_NUMBER = 65535
    MIN_TIMEOUT = 0.1
    MAX_TIMEOUT = 60.0
    MIN_BUFFER_SIZE = 256
    MAX_BUFFER_SIZE = 65536

# ============================================================================
# 系统状态枚举（系统核心）
# ============================================================================
class SystemState(Enum):
    """系统运行状态"""
    INITIALIZING = "initializing"  # 初始化中
    READY = "ready"  # 就绪
    RUNNING = "running"  # 运行中
    PAUSED = "paused"  # 暂停
    STOPPING = "stopping"  # 停止中
    STOPPED = "stopped"  # 已停止
    ERROR = "error"  # 错误状态

# ============================================================================
# 日志系统默认配置（系统核心）
# ============================================================================
class LogDefaults:
    """日志系统默认配置"""
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "[%(asctime)s] [%(levelname)s] [%(name)s] - %(message)s"
    LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
    LOG_ENCODING = "utf-8"
    MAX_LOG_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_LOG_FILES = 30
