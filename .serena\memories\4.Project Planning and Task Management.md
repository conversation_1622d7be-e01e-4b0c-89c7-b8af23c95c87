# 4. 项目规划与任务管理文档 - 数据采集系统

| 版本 | 最后更新时间               | 更新者 | 变更摘要                               |
| :--- | :------------------------- | :----- | :------------------------------------- |
| 1.0  | 2025-08-05 14:52:24 +08:00 | PL     | 初始创建，基于技术架构制定详细项目规划 |
| 2.0  | 2025-08-05 15:09:05 +08:00 | PL     | 按五层架构重新优化，分阶段开发与测试   |

---

## 1. 项目概述与目标

### 1.1 项目目标

开发一个基于五层架构的动态配置数据采集系统，支持任意自由串口协议的JSON配置驱动，实现高性能、高可靠性的串口数据采集和处理。

### 1.2 核心特性

- **动态协议配置：** 程序启动后通过JSON动态加载任意自由串口协议
- **五层架构设计：** 工具辅助层、通信抽象层、数据处理层、业务逻辑层、用户界面层
- **分层开发模式：** 每层独立开发和测试，确保功能完整性
- **高性能处理：** >1000帧/秒处理速度，<100ms通信延迟
- **多线程架构：** 主线程、串口线程、处理线程的协同工作

### 1.3 开发策略

- **分层递进开发：** 按照五层架构从底层到上层逐层开发
- **阶段性验证：** 每层开发完成后进行功能覆盖性测试
- **协议无关设计：** 底层组件完全不依赖具体协议实现
- **配置驱动实现：** 所有协议相关逻辑完全由JSON配置驱动

## 2. 分层开发里程碑规划

### 2.1 总体开发阶段

| 阶段            | 层级                 | 预计工期 | 主要交付物             | 关键验收标准         |
| :-------------- | :------------------- | :------- | :--------------------- | :------------------- |
| **阶段1** | 环境搭建与工具辅助层 | 4-6天    | 开发环境、配置管理系统 | JSON配置正确加载验证 |
| **阶段2** | 通信抽象层           | 3-4天    | 串口管理、缓冲区管理   | 串口通信稳定可靠     |
| **阶段3** | 数据处理层           | 5-7天    | 动态协议解析引擎       | 任意协议正确解析     |
| **阶段4** | 业务逻辑层           | 4-5天    | 协议流程控制系统       | 完整业务逻辑执行     |
| **阶段5** | 用户界面层           | 6-8天    | CLI和GUI用户界面       | 完整用户交互体验     |

### 2.2 关键里程碑定义

- **M1 - 配置系统就绪：** 动态配置加载和验证系统完成
- **M2 - 通信基础就绪：** 串口通信和数据缓冲功能完成
- **M3 - 协议解析就绪：** JSON驱动的协议解析功能完成
- **M4 - 核心逻辑就绪：** 完整的业务流程控制功能完成
- **M5 - 系统交付就绪：** 完整的用户界面和系统集成完成

## 3. 详细分层开发计划

### 3.1 阶段1：环境搭建与工具辅助层开发 (4-6天)

#### 3.1.1 阶段目标

建立完整的开发环境，实现动态配置管理系统，为整个项目提供配置驱动的基础支撑。

#### 3.1.2 核心任务

**任务1.1：开发环境搭建 (1天)**

- 创建Conda虚拟环境 (environment.yml)
- 安装核心依赖库 (pyserial>=3.5, jsonschema>=3.2等)
- 配置开发工具和调试环境
- 验证Python环境和库兼容性

**任务1.2：项目结构创建 (0.5天)**

- 建立五层架构目录结构
- 创建配置文件目录 (config/protocols/)
- 建立日志和测试目录

**任务1.3：基础组件实现 (1.5天)**

- 实现constants.py (系统常量定义)
- 实现helper_utils.py (十六进制处理、数据转换工具)
- 实现exceptions.py (自定义异常层次结构)
- 创建基础日志配置

**任务1.4：工具辅助层开发 (2天)**

- 实现ConfigManager (动态配置管理核心)
- 实现JSON Schema验证引擎
- 实现validators.py (配置验证器)
- 创建配置文件模板和示例

#### 3.1.3 技术要点

- 使用dataclass定义配置对象结构
- JSON Schema严格验证确保配置正确性
- 单例模式保证全局配置一致性
- 友好的配置错误提示机制

#### 3.1.4 测试策略

**单元测试：**

- 配置文件加载功能测试
- JSON Schema验证准确性测试
- 工具函数正确性测试
- 异常处理机制测试

**集成测试：**

- 配置管理器与验证器集成测试
- 配置文件模板完整性测试

**功能测试：**

- IMU948传感器配置文件加载测试
- 复杂配置场景验证测试

#### 3.1.5 验收标准

- ✅ Conda环境成功创建并激活
- ✅ 所有依赖库正确安装
- ✅ 能够正确加载和验证JSON配置文件
- ✅ 配置错误时提供友好的错误提示
- ✅ 工具函数功能完整且测试通过

### 3.2 阶段2：通信抽象层开发 (3-4天)

#### 3.2.1 阶段目标

实现协议无关的串口通信管理和数据缓冲系统，为上层提供稳定可靠的数据传输基础。

#### 3.2.2 核心任务

**任务2.1：串口管理器开发 (1.5天)**

- 实现SerialManager (协议无关串口管理)
- 封装pyserial库，提供统一接口
- 实现串口连接、断开、参数配置
- 实现连接状态监控和异常恢复

**任务2.2：缓冲区管理器开发 (1天)**

- 实现BufferManager (固定大小循环缓冲区)
- 4096字节循环缓冲区实现
- 线程安全的读写操作
- 缓冲区状态监控和告警

**任务2.3：连接池管理开发 (0.5天)**

- 实现连接池管理
- 连接复用和资源管理
- 连接异常的自动恢复机制

#### 3.2.3 技术要点

- pyserial库的深度封装和优化
- 使用RLock确保线程安全
- 条件变量协调读写操作
- 支持阻塞和非阻塞操作模式

#### 3.2.4 测试策略

**单元测试：**

- 串口连接和断开功能测试
- 缓冲区读写操作测试
- 线程安全机制测试
- 异常恢复机制测试

**集成测试：**

- 串口管理器与缓冲区管理器集成测试
- 多线程环境下的稳定性测试

**功能测试：**

- 真实串口设备连接测试
- 长时间连续数据传输测试
- 异常断开和重连测试

#### 3.2.5 验收标准

- ✅ 能够稳定连接和管理串口设备
- ✅ 数据收发功能正常且可靠
- ✅ 循环缓冲区读写操作正确
- ✅ 多线程环境下运行稳定
- ✅ 异常情况下能够自动恢复

### 3.3 阶段3：数据处理层开发 (5-7天) 【核心重点阶段】

#### 3.3.1 阶段目标

实现JSON配置驱动的动态协议解析系统，这是整个项目的核心技术难点，必须确保任意自由串口协议都能正确解析。

#### 3.3.2 核心任务

**任务3.1：帧检测器开发 (2天)**

- 实现FrameDetector (动态帧检测引擎)
- 基于JSON配置的帧头、帧尾检测
- 状态机实现的高效帧检测算法
- 数据粘连和分片处理机制

**任务3.2：数据解析器开发 (2天)**

- 实现DataParser (配置驱动数据解析)
- 支持int8/16/32、float32/64等数据类型
- 大端和小端字节序动态处理
- 配置化的数据缩放和单位转换

**任务3.3：应答验证器开发 (1天)**

- 实现ResponseValidator (动态应答验证)
- 精确匹配和正则表达式验证策略
- 超时重试机制和验证结果缓存
- 验证器工厂动态创建机制

**任务3.4：队列管理器开发 (0.5天)**

- 实现QueueManager (队列管理)
- 优先级队列和批量处理机制
- 队列监控和性能优化
- 队列满时的处理策略

#### 3.3.3 技术要点

- 状态机模式实现帧检测逻辑
- struct模块优化的二进制解析
- 工厂模式创建解析器和验证器
- 策略模式实现不同验证方式

#### 3.3.4 测试策略

**单元测试：**

- 帧检测算法准确性测试
- 数据解析正确性测试
- 应答验证策略测试
- 队列管理功能测试

**集成测试：**

- 各组件间协作测试
- 配置驱动的端到端测试

**功能测试：**

- IMU948传感器协议解析测试
- 多种自由串口协议解析测试
- 复杂数据格式解析测试
- 异常数据处理测试

#### 3.3.5 验收标准

- ✅ 能够基于JSON配置正确检测任意协议帧
- ✅ 数据解析结果准确，支持多种数据类型
- ✅ 应答验证功能完整，支持多种验证策略
- ✅ 队列管理高效，满足性能要求
- ✅ IMU948等真实设备协议解析成功

### 3.4 阶段4：业务逻辑层开发 (4-5天)

#### 3.4.1 阶段目标

实现完整的协议流程控制和业务逻辑，整合下层功能，提供完整的数据采集业务能力。

#### 3.4.2 核心任务

**任务4.1：协议流程控制器开发 (2天)**

- 实现ProtocolFlowController (动态协议流程控制)
- 基于JSON配置的协议步骤执行
- single_command和continuous_command模式实现
- 动态状态机管理复杂协议流程

**任务4.2：指令执行器开发 (1天)**

- 实现CommandExecutor (配置驱动指令执行)
- 异步指令执行和超时重试机制
- 指令队列管理和优先级处理
- 指令执行结果的统计和监控

**任务4.3：错误处理器开发 (1天)**

- 实现ErrorHandler (智能错误处理)
- 分级错误处理和自动恢复策略
- 连续错误计数和暂停机制
- 错误类型分类和统计分析

**任务4.4：日志管理器开发 (0.5天)**

- 实现LoggerManager (日志管理)
- 结构化日志记录和性能监控
- 日志轮转和存储管理
- 日志级别控制和过滤

#### 3.4.3 技术要点

- 状态机驱动的协议流程管理
- 异步执行和并发控制
- 观察者模式的事件通知机制
- 策略模式的错误处理

#### 3.4.4 测试策略

**单元测试：**

- 协议流程执行逻辑测试
- 指令执行和重试机制测试
- 错误处理策略测试
- 日志记录功能测试

**集成测试：**

- 业务逻辑层与数据处理层集成测试
- 多线程协调机制测试

**功能测试：**

- 完整协议流程执行测试
- 三种工作模式功能测试
- 错误恢复机制测试
- 长时间运行稳定性测试

#### 3.4.5 验收标准

- ✅ 协议流程控制功能完整且稳定
- ✅ 三种工作模式正常执行
- ✅ 错误处理和恢复机制有效
- ✅ 日志记录完整且有用
- ✅ 整个核心逻辑系统运行正常

### 3.5 阶段5：用户界面层开发 (6-8天)

#### 3.5.1 阶段目标

实现完整的用户交互界面，包括命令行界面和图形用户界面，提供良好的用户体验。

#### 3.5.2 核心任务

**任务5.1：命令行界面开发 (3天)**

- 实现CLIInterface (交互式命令行界面)
- 实现OutputManager (输出管理器)
- 实现数据格式化器 (控制台和文件格式化)
- 配置文件选择和管理界面

**任务5.2：GUI界面开发 (3-4天)**

- 设计PyQt图形界面架构
- 实现主窗口和配置管理界面
- 实现数据可视化和实时展示
- 实现数据导出和历史记录功能

**任务5.3：界面集成优化 (1天)**

- 用户体验优化和界面响应性改进
- 跨平台兼容性测试和调整
- 界面与业务逻辑的最终集成

#### 3.5.3 技术要点

- 交互式命令行设计和实现
- PyQt5/6的MVC架构设计
- 实时数据展示和图表绘制
- 多格式数据输出和导出

#### 3.5.4 测试策略

**单元测试：**

- 界面组件功能测试
- 数据格式化器测试
- 输出管理器测试

**集成测试：**

- 界面与业务逻辑集成测试
- 数据流从底层到界面的端到端测试

**功能测试：**

- 用户交互流程测试
- 界面响应性能测试
- 数据展示准确性测试
- 跨平台兼容性测试

#### 3.5.5 验收标准

- ✅ 命令行界面功能完整且易用
- ✅ 图形界面美观且响应迅速
- ✅ 数据展示准确且实时
- ✅ 用户体验良好且稳定
- ✅ 跨平台运行正常

## 3. 技术实现要点

### 3.1 动态协议配置核心保障

- **协议无关性：** 每层设计都确保与具体协议无关
- **配置驱动：** 所有协议相关逻辑完全由JSON配置驱动
- **运行时加载：** 程序启动后动态加载任意协议配置
- **零硬编码：** 底层组件不包含任何协议相关硬编码

### 3.2 分层开发质量保证

- **层间接口：** 每层都有清晰定义的接口和职责
- **独立测试：** 每层开发完成后进行全面的功能测试
- **回归验证：** 上层开发时确保不影响下层功能
- **集成验证：** 每层集成时进行完整的端到端测试

### 3.3 性能目标实现

- **处理速度：** >1000帧/秒数据处理能力
- **通信延迟：** <100ms指令响应时间
- **资源使用：** <100MB内存占用，<10%CPU使用
- **运行稳定：** 支持24小时+连续运行

**递进关系说明:** 本文档作为模式4的优化产出，基于五层架构设计了分阶段开发计划，每个阶段对应一个架构层级，确保了动态协议配置的核心要求得到充分实现，为模式5的开发实施提供了清晰的分层递进执行路线图。
