#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IMU948数据解析测试脚本
专门测试IMU948传感器的数据解析功能，验证ParsedData对象的属性访问

作者: DataStudio开发团队
创建时间: 2025-08-08
"""

import sys
import os
from pathlib import Path
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from business_logic.protocol_flow_controller import ProtocolFlowController
from business_logic.logger_manager import LoggerManager, LogLevel, LogFormat
from data_processing.data_parser import DataParser, ParsedData
from utils.helper_utils import hex_to_bytes


class IMU948DataParsingTest:
    """IMU948数据解析测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        # 初始化日志管理器
        log_config = {
            "log_level": LogLevel.DEBUG,
            "log_format": LogFormat.DETAILED,
            "log_dir": "logs",
            "enable_console": True,
            "enable_file": True,
            "enable_performance": True
        }
        self.logger_manager = LoggerManager(log_config)
        self.logger = self.logger_manager.get_logger("IMU948DataParsingTest")
        
        self.config_path = "config/protocols/imu948_example.json"
        self.flow_controller = None
        
    def test_parsed_data_structure(self):
        """测试ParsedData数据结构"""
        print("🔍 测试1: ParsedData数据结构验证")
        print("=" * 60)
        
        try:
            # 创建一个测试的ParsedData对象
            test_data = ParsedData(
                field_name="Test_Field",
                raw_value=1000,
                scaled_value=10.0,
                unit="°",
                raw_bytes=b'\x03\xe8'
            )
            
            # 验证属性访问
            print(f"✅ field_name: {test_data.field_name}")
            print(f"✅ raw_value: {test_data.raw_value}")
            print(f"✅ scaled_value: {test_data.scaled_value}")
            print(f"✅ unit: {test_data.unit}")
            print(f"✅ raw_bytes: {test_data.raw_bytes.hex()}")
            
            # 测试错误的属性访问
            try:
                _ = test_data.name  # 这应该会失败
                print("❌ 错误：test_data.name 不应该存在")
                return False
            except AttributeError:
                print("✅ 正确：test_data.name 属性不存在（符合预期）")
            
            try:
                _ = test_data.value  # 这应该会失败
                print("❌ 错误：test_data.value 不应该存在")
                return False
            except AttributeError:
                print("✅ 正确：test_data.value 属性不存在（符合预期）")
            
            print("🎉 ParsedData数据结构测试通过")
            return True
            
        except Exception as e:
            print(f"❌ ParsedData数据结构测试失败: {str(e)}")
            return False
    
    def test_data_parser_initialization(self):
        """测试数据解析器初始化"""
        print("\n🔍 测试2: 数据解析器初始化")
        print("=" * 60)
        
        try:
            # 检查配置文件
            config_file = Path(self.config_path)
            if not config_file.exists():
                print(f"❌ 配置文件不存在: {self.config_path}")
                return False
            
            print(f"✅ 配置文件检查通过: {self.config_path}")
            
            # 初始化协议流程控制器
            self.flow_controller = ProtocolFlowController(self.config_path)
            
            # 获取数据解析器
            data_parser = self.flow_controller.data_parser
            if not data_parser:
                print("❌ 数据解析器未初始化")
                return False
            
            print("✅ 数据解析器初始化成功")
            
            # 检查字段配置
            field_names = data_parser.get_field_names()
            print(f"📋 配置的字段: {field_names}")
            
            expected_fields = ["Roll_滚转角", "Pitch_俯仰角", "Yaw_偏航角", "Position_X", "Position_Y", "Position_Z"]
            for field_name in expected_fields:
                if field_name in field_names:
                    print(f"✅ 字段 {field_name} 配置正确")
                else:
                    print(f"❌ 字段 {field_name} 配置缺失")
                    return False
            
            print("🎉 数据解析器初始化测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 数据解析器初始化测试失败: {str(e)}")
            return False
    
    def test_frame_parsing(self):
        """测试帧数据解析"""
        print("\n🔍 测试3: 帧数据解析")
        print("=" * 60)
        
        try:
            if not self.flow_controller:
                print("❌ 协议流程控制器未初始化")
                return False
            
            data_parser = self.flow_controller.data_parser
            
            # 构造测试帧数据（24字节的IMU948数据帧）
            # 帧头(2) + 长度(2) + 功能码(2) + 数据(12) + 校验(2) + 帧尾(2) + 保留(2) = 24字节
            test_frame_hex = "49 00 0C 91 00 00 10 00 20 00 30 00 40 00 50 00 60 00 00 00 00 00 4D 00"
            test_frame_data = hex_to_bytes(test_frame_hex)
            
            print(f"📥 测试帧数据: {test_frame_hex}")
            print(f"📏 帧长度: {len(test_frame_data)} 字节")
            
            # 解析帧数据
            parsed_fields = data_parser.parse_frame(test_frame_data)
            
            print(f"🔍 解析字段数量: {len(parsed_fields)}")
            
            if len(parsed_fields) == 0:
                print("❌ 没有解析到任何字段")
                return False
            
            # 验证解析结果
            print("📊 解析结果:")
            for i, field in enumerate(parsed_fields, 1):
                print(f"  {i}. 字段名: {field.field_name}")
                print(f"     原始值: {field.raw_value}")
                print(f"     缩放值: {field.scaled_value:.6f}")
                print(f"     单位: {field.unit}")
                print(f"     原始字节: {field.raw_bytes.hex()}")
                print()
            
            # 验证特定字段
            roll_field = None
            for field in parsed_fields:
                if "Roll" in field.field_name or "滚转角" in field.field_name:
                    roll_field = field
                    break
            
            if roll_field:
                print(f"✅ 找到Roll字段: {roll_field.field_name} = {roll_field.scaled_value:.6f}{roll_field.unit}")
            else:
                print("❌ 未找到Roll字段")
                return False
            
            print("🎉 帧数据解析测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 帧数据解析测试失败: {str(e)}")
            self.logger.error(f"帧数据解析测试异常: {str(e)}")
            return False
    
    def test_data_callback_simulation(self):
        """测试数据回调函数模拟"""
        print("\n🔍 测试4: 数据回调函数模拟")
        print("=" * 60)
        
        try:
            if not self.flow_controller:
                print("❌ 协议流程控制器未初始化")
                return False
            
            data_parser = self.flow_controller.data_parser
            
            # 构造测试帧数据
            test_frame_hex = "49 00 0C 91 00 00 10 00 20 00 30 00 40 00 50 00 60 00 00 00 00 00 4D 00"
            test_frame_data = hex_to_bytes(test_frame_hex)
            
            # 解析帧数据
            parsed_fields = data_parser.parse_frame(test_frame_data)
            
            # 模拟ContinuousDataResult对象
            class MockContinuousDataResult:
                def __init__(self, frame_data, parsed_fields, frame_number):
                    self.frame_data = frame_data
                    self.parsed_fields = parsed_fields
                    self.timestamp = time.time()
                    self.frame_number = frame_number
            
            mock_result = MockContinuousDataResult(test_frame_data, parsed_fields, 1)
            
            # 模拟data_callback逻辑
            print("🔄 模拟data_callback处理...")
            
            euler_angles = {}
            position_info = {}
            
            for field in mock_result.parsed_fields:
                # 使用修复后的属性访问
                if "Roll" in field.field_name or "滚转角" in field.field_name:
                    euler_angles["Roll"] = f"{field.scaled_value:.2f}{field.unit}"
                elif "Pitch" in field.field_name or "俯仰角" in field.field_name:
                    euler_angles["Pitch"] = f"{field.scaled_value:.2f}{field.unit}"
                elif "Yaw" in field.field_name or "偏航角" in field.field_name:
                    euler_angles["Yaw"] = f"{field.scaled_value:.2f}{field.unit}"
                elif "X" in field.field_name and ("位置" in field.field_name or "Position" in field.field_name):
                    position_info["X"] = f"{field.scaled_value:.3f}{field.unit}"
                elif "Y" in field.field_name and ("位置" in field.field_name or "Position" in field.field_name):
                    position_info["Y"] = f"{field.scaled_value:.3f}{field.unit}"
                elif "Z" in field.field_name and ("位置" in field.field_name or "Position" in field.field_name):
                    position_info["Z"] = f"{field.scaled_value:.3f}{field.unit}"
            
            # 输出结果
            print(f"📊 帧#{mock_result.frame_number:06d}")
            
            if euler_angles:
                print(f"🧭 欧拉角: ", end="")
                for name, value in euler_angles.items():
                    print(f"{name}={value} ", end="")
                print()
            
            if position_info:
                print(f"📍 位置: ", end="")
                for name, value in position_info.items():
                    print(f"{name}={value} ", end="")
                print()
            
            print("🎉 数据回调函数模拟测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 数据回调函数模拟测试失败: {str(e)}")
            self.logger.error(f"数据回调函数模拟测试异常: {str(e)}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 IMU948数据解析测试开始")
        print("=" * 80)
        
        tests = [
            self.test_parsed_data_structure,
            self.test_data_parser_initialization,
            self.test_frame_parsing,
            self.test_data_callback_simulation
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
            else:
                break  # 如果某个测试失败，停止后续测试
        
        print("\n" + "=" * 80)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！数据解析功能正常")
            return True
        else:
            print("❌ 部分测试失败，请检查问题")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.logger_manager:
                self.logger_manager.shutdown()
        except Exception as e:
            print(f"清理资源异常: {str(e)}")


def main():
    """主函数"""
    print("🎯 IMU948数据解析测试程序")
    print("=" * 60)
    print("功能说明:")
    print("1. 🔍 验证ParsedData数据结构")
    print("2. 🔧 测试数据解析器初始化")
    print("3. 📊 测试帧数据解析功能")
    print("4. 🔄 模拟数据回调处理")
    print("=" * 60)
    
    test = IMU948DataParsingTest()
    
    try:
        success = test.run_all_tests()
        return 0 if success else 1
    except Exception as e:
        print(f"❌ 测试程序异常: {str(e)}")
        return 1
    finally:
        test.cleanup()


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
