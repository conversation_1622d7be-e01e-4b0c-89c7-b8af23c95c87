# DataStudio Project Build Script
# Build DataStudio data collection system using PyInstaller
# 
# Author: AI Assistant
# Created: 2025-08-08
# Platform: Windows PowerShell

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "DataStudio Project Build Tool" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check Python environment
Write-Host "[1/6] Checking Python environment..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python environment OK: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "❌ Error: Python environment not found" -ForegroundColor Red
    Write-Host "Please ensure Python is installed and added to PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check PyInstaller
Write-Host ""
Write-Host "[2/6] Checking PyInstaller..." -ForegroundColor Yellow
try {
    python -c "import PyInstaller" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ PyInstaller is installed" -ForegroundColor Green
    } else {
        Write-Host "⚠️  PyInstaller not installed, installing..." -ForegroundColor Yellow
        pip install pyinstaller
        if ($LASTEXITCODE -ne 0) {
            throw "PyInstaller installation failed"
        }
        Write-Host "✅ PyInstaller installed successfully" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Error: PyInstaller installation failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check project dependencies
Write-Host ""
Write-Host "[3/6] Checking project dependencies..." -ForegroundColor Yellow
try {
    python -c "import serial, jsonschema" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Project dependencies OK" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Project dependencies incomplete, installing..." -ForegroundColor Yellow
        pip install pyserial>=3.5 jsonschema>=3.2.0
        if ($LASTEXITCODE -ne 0) {
            throw "Dependencies installation failed"
        }
        Write-Host "✅ Project dependencies installed successfully" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Error: Dependencies installation failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Clean old build files
Write-Host ""
Write-Host "[4/6] Cleaning old build files..." -ForegroundColor Yellow
if (Test-Path "build") {
    Remove-Item -Recurse -Force "build"
    Write-Host "✅ Cleaned build directory" -ForegroundColor Green
}
if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist"
    Write-Host "✅ Cleaned dist directory" -ForegroundColor Green
}

# Clean all __pycache__ directories recursively
Get-ChildItem -Path . -Recurse -Directory -Name "__pycache__" | ForEach-Object {
    $fullPath = Join-Path $PWD $_
    if (Test-Path $fullPath) {
        Remove-Item -Recurse -Force $fullPath
    }
}
Write-Host "✅ Cleaned all cache files" -ForegroundColor Green

# Execute packaging
Write-Host ""
Write-Host "[5/6] Starting packaging..." -ForegroundColor Yellow
Write-Host "Using config file: DataStudio.spec" -ForegroundColor Cyan
Write-Host ""

try {
    pyinstaller DataStudio.spec --clean --noconfirm
    if ($LASTEXITCODE -ne 0) {
        throw "Packaging failed"
    }
} catch {
    Write-Host "❌ Error: Packaging failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Verify packaging result
Write-Host ""
Write-Host "[6/6] Verifying packaging result..." -ForegroundColor Yellow
if (Test-Path "dist\DataStudio\DataStudio.exe") {
    Write-Host "✅ Packaging successful!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📁 Output directory: dist\DataStudio\" -ForegroundColor Cyan
    Write-Host "🚀 Executable file: dist\DataStudio\DataStudio.exe" -ForegroundColor Cyan
    Write-Host "📋 Config files: dist\DataStudio\config\" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Packaging completed!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "💡 Usage instructions:" -ForegroundColor Yellow
    Write-Host "1. Navigate to dist\DataStudio\ directory" -ForegroundColor White
    Write-Host "2. Run DataStudio.exe" -ForegroundColor White
    Write-Host "3. Select config file for testing" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host "❌ Error: Packaged file not found" -ForegroundColor Red
    Write-Host "Please check error messages during packaging process" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Press Enter to exit..."
Read-Host
