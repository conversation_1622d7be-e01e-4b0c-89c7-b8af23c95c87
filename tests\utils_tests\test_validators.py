#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 validators.py 模块
验证配置验证器的功能

作者: LD (Lead Developer)
创建时间: 2025-08-06
版本: 1.0
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.validators import (
    ConfigValidator, ProtocolConfigValidator, SystemConfigValidator,
    validate_protocol_config, validate_system_config
)
from utils.exceptions import ConfigValidationError


def test_protocol_config_validator_valid():
    """测试协议配置验证器 - 有效配置"""
    print("=" * 60)
    print("测试协议配置验证器 - 有效配置")
    print("=" * 60)
    
    try:
        # 使用IMU948配置文件进行测试
        config_file = project_root / "config" / "protocols" / "imu948_example.json"
        
        if not config_file.exists():
            print(f"❌ IMU948配置文件不存在: {config_file}")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        validator = ProtocolConfigValidator()
        result = validator.validate(config_data)
        
        if result:
            print("✅ IMU948配置验证通过")
            return True
        else:
            print("❌ IMU948配置验证失败:")
            for error in validator.get_errors():
                print(f"   - {error}")
            return False
        
    except Exception as e:
        print(f"❌ 协议配置验证测试失败: {e}")
        return False


def test_protocol_config_validator_invalid():
    """测试协议配置验证器 - 无效配置"""
    print("\n" + "=" * 60)
    print("测试协议配置验证器 - 无效配置")
    print("=" * 60)
    
    try:
        # 创建无效的配置
        invalid_config = {
            # 缺少protocol_info
            "serial_config": {
                "port": "COM1",
                "baudrate": 999999,  # 无效波特率
                "databits": 9,       # 无效数据位
                "parity": "invalid", # 无效校验位
                "stopbits": 3,       # 无效停止位
                "timeout": -1        # 无效超时时间
            },
            "protocol_flow": {
                "steps": [
                    {
                        "name": "测试步骤",
                        "type": "invalid_type",  # 无效步骤类型
                        "commands": ["non_existent_cmd"]  # 不存在的指令
                    }
                ]
            },
            "commands": {
                "single": [
                    {
                        "id": "test_cmd",
                        "name": "测试指令",
                        "send": "invalid hex",  # 无效十六进制
                        "response_validation": {
                            "type": "invalid_type",  # 无效验证类型
                            "pattern": "",           # 空模式
                            "timeout": -1,           # 无效超时
                            "retry_count": -1        # 无效重试次数
                        }
                    }
                ]
            }
        }
        
        validator = ProtocolConfigValidator()
        result = validator.validate(invalid_config)
        
        if not result:
            print("✅ 无效配置正确被识别")
            print("   检测到的错误:")
            for error in validator.get_errors():
                print(f"   - {error}")
            return True
        else:
            print("❌ 无效配置未被识别")
            return False
        
    except Exception as e:
        print(f"❌ 无效协议配置验证测试失败: {e}")
        return False


def test_system_config_validator_valid():
    """测试系统配置验证器 - 有效配置"""
    print("\n" + "=" * 60)
    print("测试系统配置验证器 - 有效配置")
    print("=" * 60)
    
    try:
        # 使用系统配置文件进行测试
        config_file = project_root / "config" / "system_config.json"
        
        if not config_file.exists():
            print(f"❌ 系统配置文件不存在: {config_file}")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        validator = SystemConfigValidator()
        result = validator.validate(config_data)
        
        if result:
            print("✅ 系统配置验证通过")
            return True
        else:
            print("❌ 系统配置验证失败:")
            for error in validator.get_errors():
                print(f"   - {error}")
            return False
        
    except Exception as e:
        print(f"❌ 系统配置验证测试失败: {e}")
        return False


def test_system_config_validator_invalid():
    """测试系统配置验证器 - 无效配置"""
    print("\n" + "=" * 60)
    print("测试系统配置验证器 - 无效配置")
    print("=" * 60)
    
    try:
        # 创建无效的系统配置
        invalid_config = {
            "buffer": {
                "size": 100,  # 小于最小缓冲区大小
                "warning_threshold": 1.5  # 超出范围
            },
            "queue": {
                "size": 0,  # 无效队列大小
                "warning_threshold": -0.1,  # 超出范围
                "batch_size": 0  # 无效批处理大小
            },
            "error_handling": {
                "max_consecutive_errors": 0,  # 无效错误次数
                "retry_delay": -1,  # 无效延迟
                "max_retry_attempts": -1  # 无效重试次数
            },
            "logging": {
                "level": "INVALID_LEVEL",  # 无效日志级别
                "max_file_size": 100,  # 小于最小文件大小
                "max_files": 0  # 无效文件数量
            }
        }
        
        validator = SystemConfigValidator()
        result = validator.validate(invalid_config)
        
        if not result:
            print("✅ 无效系统配置正确被识别")
            print("   检测到的错误:")
            for error in validator.get_errors():
                print(f"   - {error}")
            return True
        else:
            print("❌ 无效系统配置未被识别")
            return False
        
    except Exception as e:
        print(f"❌ 无效系统配置验证测试失败: {e}")
        return False


def test_convenience_functions():
    """测试便捷函数"""
    print("\n" + "=" * 60)
    print("测试便捷函数")
    print("=" * 60)
    
    try:
        # 测试协议配置验证便捷函数
        config_file = project_root / "config" / "protocols" / "imu948_example.json"
        with open(config_file, 'r', encoding='utf-8') as f:
            protocol_config = json.load(f)
        
        try:
            validate_protocol_config(protocol_config)
            print("✅ 协议配置验证便捷函数正常")
        except ConfigValidationError:
            print("❌ 协议配置验证便捷函数异常")
            return False
        
        # 测试系统配置验证便捷函数
        config_file = project_root / "config" / "system_config.json"
        with open(config_file, 'r', encoding='utf-8') as f:
            system_config = json.load(f)
        
        try:
            validate_system_config(system_config)
            print("✅ 系统配置验证便捷函数正常")
        except ConfigValidationError:
            print("❌ 系统配置验证便捷函数异常")
            return False
        
        # 测试无效配置时是否抛出异常
        invalid_config = {"invalid": "config"}
        
        try:
            validate_protocol_config(invalid_config)
            print("❌ 应该抛出ConfigValidationError异常")
            return False
        except ConfigValidationError:
            print("✅ 无效配置正确抛出异常")
        
        return True
        
    except Exception as e:
        print(f"❌ 便捷函数测试失败: {e}")
        return False


def test_config_validator_base_class():
    """测试配置验证器基类"""
    print("\n" + "=" * 60)
    print("测试配置验证器基类")
    print("=" * 60)
    
    try:
        validator = ConfigValidator()
        
        # 测试初始状态
        if not validator.has_errors():
            print("✅ 初始状态无错误")
        else:
            print("❌ 初始状态有错误")
            return False
        
        # 添加错误
        validator.add_error("测试错误1")
        validator.add_error("测试错误2")
        
        if validator.has_errors():
            print("✅ 添加错误后状态正确")
        else:
            print("❌ 添加错误后状态不正确")
            return False
        
        # 获取错误列表
        errors = validator.get_errors()
        if len(errors) == 2:
            print("✅ 错误列表长度正确")
        else:
            print("❌ 错误列表长度不正确")
            return False
        
        # 清除错误
        validator.clear_errors()
        if not validator.has_errors():
            print("✅ 清除错误后状态正确")
        else:
            print("❌ 清除错误后状态不正确")
            return False
        
        # 测试抛出异常
        validator.add_error("测试异常")
        try:
            validator.raise_if_errors("test_field")
            print("❌ 应该抛出ConfigValidationError异常")
            return False
        except ConfigValidationError as e:
            print("✅ 正确抛出ConfigValidationError异常")
            if "test_field" in str(e):
                print("✅ 异常包含字段路径信息")
            else:
                print("❌ 异常不包含字段路径信息")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证器基类测试失败: {e}")
        return False


def test_hex_string_validation():
    """测试十六进制字符串验证"""
    print("\n" + "=" * 60)
    print("测试十六进制字符串验证")
    print("=" * 60)
    
    try:
        validator = ProtocolConfigValidator()
        
        # 测试有效的十六进制字符串
        valid_config = {
            "protocol_info": {"name": "测试协议"},
            "serial_config": {
                "port": "COM1", "baudrate": 9600, "databits": 8,
                "parity": "none", "stopbits": 1, "timeout": 1.0
            },
            "protocol_flow": {
                "steps": [{"name": "测试", "type": "single_command", "commands": ["test_cmd"]}]
            },
            "commands": {
                "single": [{
                    "id": "test_cmd", "name": "测试指令",
                    "send": "01 02 03 FF",  # 有效十六进制
                    "response_validation": {
                        "type": "exact", "pattern": "01 02 03 FF",
                        "timeout": 1.0, "retry_count": 1
                    }
                }]
            }
        }
        
        result = validator.validate(valid_config)
        if result:
            print("✅ 有效十六进制字符串验证通过")
        else:
            print("❌ 有效十六进制字符串验证失败")
            return False
        
        # 测试无效的十六进制字符串
        validator.clear_errors()
        invalid_config = valid_config.copy()
        invalid_config["commands"]["single"][0]["send"] = "invalid hex string"
        
        result = validator.validate(invalid_config)
        if not result:
            print("✅ 无效十六进制字符串正确被识别")
        else:
            print("❌ 无效十六进制字符串未被识别")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 十六进制字符串验证测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试 validators.py 模块")
    print("=" * 80)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("协议配置验证器-有效", test_protocol_config_validator_valid()))
    test_results.append(("协议配置验证器-无效", test_protocol_config_validator_invalid()))
    test_results.append(("系统配置验证器-有效", test_system_config_validator_valid()))
    test_results.append(("系统配置验证器-无效", test_system_config_validator_invalid()))
    test_results.append(("便捷函数", test_convenience_functions()))
    test_results.append(("验证器基类", test_config_validator_base_class()))
    test_results.append(("十六进制验证", test_hex_string_validation()))
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {len(test_results)} 个测试")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    
    if failed == 0:
        print("\n🎉 所有测试通过！")
        return True
    else:
        print(f"\n⚠️  有 {failed} 个测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
