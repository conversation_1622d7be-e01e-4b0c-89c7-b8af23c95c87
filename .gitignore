# DataStudio Project .gitignore
# 为DataStudio串口通信工具项目定制的Git忽略文件
# 作者: LD (Lead Developer)
# 创建时间: 2025-08-07

# =============================================================================
# Python 相关文件
# =============================================================================

# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# C扩展
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST


# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# 翻译
*.mo
*.pot

# Django相关
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask相关
instance/
.webassets-cache

# Scrapy相关
.scrapy

# Sphinx文档
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery相关
celerybeat-schedule
celerybeat.pid

# SageMath解析文件
*.sage.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre类型检查器
.pyre/

# pytype静态类型分析器
.pytype/

# Cython调试符号
cython_debug/

# =============================================================================
# IDE 和编辑器相关文件
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# 操作系统相关文件
# =============================================================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# DataStudio 项目特定文件
# =============================================================================

# 配置文件（包含敏感信息）
config/local_config.json
config/production_config.json
config/user_settings.json
*.secret
*.key

# 日志文件
logs/
*.log
*.log.*
log_*.txt

# 数据文件
data/raw/
data/temp/
data/cache/
*.csv
*.xlsx
*.json.bak

# 测试输出
test_output/
test_results/
*.test
coverage_html/

# 串口通信相关
# 虚拟串口配置文件
virtual_ports.conf
com_port_mapping.json

# 通信日志和数据
communication_logs/
serial_data/
*.hex
*.bin
*.raw

# 协议解析缓存
protocol_cache/
parsed_data/

# 设备配置文件（可能包含敏感信息）
device_configs/
*.device
*.profile

# 用户界面相关
ui/temp/
ui/cache/
*.ui.bak

# 性能分析文件
*.prof
*.pstats
profiling_results/

# 备份文件
*.bak
*.backup
*.old
*_backup.*
*_old.*

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 文档生成文件
docs/build/
docs/html/
docs/latex/

# 打包和分发
packaging/
installer/
*.exe
*.msi
*.dmg
*.pkg
*.deb
*.rpm

# =============================================================================
# 开发工具相关
# =============================================================================

# Git相关
*.orig
*.rej

# 版本控制
.svn/
.hg/
.bzr/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 缓存目录
.cache/
cache/

# Node.js (如果使用了前端工具)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# =============================================================================
# 安全和隐私
# =============================================================================

# 密钥和证书
*.pem
*.key
*.crt
*.p12
*.pfx

# 环境变量和配置
.env.local
.env.development
.env.test
.env.production

# 用户特定配置
user_config.json
personal_settings.ini

# =============================================================================
# 其他
# =============================================================================

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 媒体文件（如果不需要版本控制）
*.mp4
*.avi
*.mov
*.wav
*.mp3

# 大文件标记
*.large
*.big
