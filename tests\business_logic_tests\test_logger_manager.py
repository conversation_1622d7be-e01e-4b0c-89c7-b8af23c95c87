#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理器测试

测试 LoggerManager 的所有功能：
- 日志记录和管理
- 性能监控
- 日志轮转
- 多格式输出

作者: DataStudio开发团队
创建时间: 2025-08-08
"""

import sys
import os
import unittest
import tempfile
import shutil
import time
import logging
from pathlib import Path
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from business_logic.logger_manager import (
    LoggerManager,
    LogLevel,
    LogFormat,
    LogEntry,
    PerformanceMetric,
    JSONFormatter,
    PerformanceFormatter
)


class TestLoggerManager(unittest.TestCase):
    """日志管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录用于测试
        self.temp_dir = tempfile.mkdtemp()
        
        # 测试配置
        self.test_config = {
            "log_level": LogLevel.DEBUG,
            "log_format": LogFormat.DETAILED,
            "log_dir": self.temp_dir,
            "max_file_size": 1024,  # 1KB for testing
            "backup_count": 3,
            "enable_console": False,  # 禁用控制台输出避免测试干扰
            "enable_file": True,
            "enable_performance": True
        }
    
    def tearDown(self):
        """测试后清理"""
        # 删除临时目录
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_initialization(self):
        """测试初始化"""
        logger_manager = LoggerManager(self.test_config)
        
        # 验证配置
        self.assertEqual(logger_manager.effective_config["log_level"], LogLevel.DEBUG)
        self.assertEqual(logger_manager.effective_config["log_format"], LogFormat.DETAILED)
        self.assertEqual(logger_manager.effective_config["log_dir"], self.temp_dir)
        self.assertTrue(logger_manager.performance_enabled)
        
        # 验证日志目录创建
        self.assertTrue(Path(self.temp_dir).exists())
        
        print("✅ 日志管理器初始化测试通过")
    
    def test_get_logger(self):
        """测试获取日志器"""
        # 创建独立的日志管理器避免状态污染
        temp_dir = tempfile.mkdtemp()
        test_config = {
            "log_level": LogLevel.DEBUG,
            "log_format": LogFormat.DETAILED,
            "log_dir": temp_dir,
            "enable_console": False,
            "enable_file": True,
        }
        logger_manager = LoggerManager(test_config)

        try:
            # 获取日志器
            logger1 = logger_manager.get_logger("TestLogger1")
            logger2 = logger_manager.get_logger("TestLogger2")
            logger1_again = logger_manager.get_logger("TestLogger1")

            # 验证日志器
            self.assertIsInstance(logger1, logging.Logger)
            self.assertIsInstance(logger2, logging.Logger)
            self.assertIs(logger1, logger1_again)  # 同一个实例
            self.assertIsNot(logger1, logger2)     # 不同实例

            # 验证日志器管理（包括LoggerManager自己创建的日志器）
            self.assertEqual(len(logger_manager.loggers), 3)  # TestLogger1, TestLogger2, LoggerManager
            self.assertIn("TestLogger1", logger_manager.loggers)
            self.assertIn("TestLogger2", logger_manager.loggers)
            self.assertIn("LoggerManager", logger_manager.loggers)
        finally:
            logger_manager.shutdown()
            shutil.rmtree(temp_dir, ignore_errors=True)
        
        print("✅ 获取日志器测试通过")
    
    def test_log_performance(self):
        """测试性能日志记录"""
        logger_manager = LoggerManager(self.test_config)
        
        # 记录性能指标
        logger_manager.log_performance(
            name="response_time",
            value=123.45,
            unit="ms",
            tags={"component": "SerialManager"},
            message="串口响应时间"
        )
        
        # 验证性能指标
        self.assertEqual(len(logger_manager.performance_metrics), 1)
        
        metric = logger_manager.performance_metrics[0]
        self.assertEqual(metric.name, "response_time")
        self.assertEqual(metric.value, 123.45)
        self.assertEqual(metric.unit, "ms")
        self.assertEqual(metric.tags["component"], "SerialManager")
        
        print("✅ 性能日志记录测试通过")
    
    def test_log_structured(self):
        """测试结构化日志记录"""
        logger_manager = LoggerManager(self.test_config)
        
        # 记录结构化日志
        extra_data = {
            "user_id": "12345",
            "session_id": "abcdef",
            "action": "connect_serial"
        }
        
        logger_manager.log_structured(
            logger_name="StructuredTest",
            level=LogLevel.INFO,
            message="用户连接串口",
            extra_data=extra_data
        )
        
        # 验证日志器被创建
        self.assertIn("StructuredTest", logger_manager.loggers)
        
        print("✅ 结构化日志记录测试通过")
    
    def test_create_component_logger(self):
        """测试创建组件日志器"""
        logger_manager = LoggerManager(self.test_config)
        
        # 创建组件日志器
        component_logger = logger_manager.create_component_logger(
            component_name="SerialManager",
            log_file="serial_manager.log"
        )
        
        # 验证日志器
        self.assertIsInstance(component_logger, logging.Logger)
        self.assertEqual(component_logger.name, "SerialManager")
        
        # 验证专用日志文件
        log_file_path = Path(self.temp_dir) / "serial_manager.log"
        
        # 写入一些日志
        component_logger.info("测试日志消息")
        
        # 等待日志写入
        time.sleep(0.1)
        
        print("✅ 创建组件日志器测试通过")
    
    def test_set_log_level(self):
        """测试设置日志级别"""
        logger_manager = LoggerManager(self.test_config)
        
        # 获取日志器
        test_logger = logger_manager.get_logger("TestLogger")
        
        # 设置特定日志器的级别
        logger_manager.set_log_level(LogLevel.ERROR, "TestLogger")
        self.assertEqual(test_logger.level, LogLevel.ERROR.value)
        
        # 设置根日志器的级别
        logger_manager.set_log_level(LogLevel.WARNING)
        self.assertEqual(logging.getLogger().level, LogLevel.WARNING.value)
        
        print("✅ 设置日志级别测试通过")
    
    def test_add_file_handler(self):
        """测试添加文件处理器"""
        logger_manager = LoggerManager(self.test_config)
        
        # 获取日志器
        test_logger = logger_manager.get_logger("FileHandlerTest")
        
        # 添加文件处理器
        test_file_path = Path(self.temp_dir) / "test_handler.log"
        logger_manager.add_file_handler(
            logger_name="FileHandlerTest",
            file_path=str(test_file_path),
            format_type=LogFormat.JSON
        )
        
        # 写入日志
        test_logger.info("测试文件处理器")
        
        # 等待日志写入
        time.sleep(0.1)
        
        print("✅ 添加文件处理器测试通过")
    
    def test_get_performance_metrics(self):
        """测试获取性能指标"""
        logger_manager = LoggerManager(self.test_config)
        
        # 记录多个性能指标
        metrics_data = [
            ("cpu_usage", 45.6, "%"),
            ("memory_usage", 78.9, "%"),
            ("response_time", 123.4, "ms"),
            ("throughput", 567.8, "ops/s")
        ]
        
        for name, value, unit in metrics_data:
            logger_manager.log_performance(name, value, unit)
        
        # 获取性能指标
        metrics = logger_manager.get_performance_metrics(time_window=60.0)
        
        # 验证指标
        self.assertEqual(len(metrics), 4)
        
        # 验证指标内容
        metric_names = [m.name for m in metrics]
        self.assertIn("cpu_usage", metric_names)
        self.assertIn("memory_usage", metric_names)
        self.assertIn("response_time", metric_names)
        self.assertIn("throughput", metric_names)
        
        print("✅ 获取性能指标测试通过")
    
    def test_get_performance_summary(self):
        """测试获取性能摘要"""
        logger_manager = LoggerManager(self.test_config)
        
        # 记录同一指标的多个值
        for i in range(5):
            logger_manager.log_performance("response_time", 100 + i * 10, "ms")
        
        # 获取性能摘要
        summary = logger_manager.get_performance_summary(time_window=60.0)
        
        # 验证摘要
        self.assertIn("response_time", summary)
        
        response_time_summary = summary["response_time"]
        self.assertEqual(response_time_summary["count"], 5)
        self.assertEqual(response_time_summary["min"], 100)
        self.assertEqual(response_time_summary["max"], 140)
        self.assertEqual(response_time_summary["avg"], 120)
        self.assertEqual(response_time_summary["total"], 600)
        
        print("✅ 获取性能摘要测试通过")
    
    def test_clear_performance_metrics(self):
        """测试清空性能指标"""
        logger_manager = LoggerManager(self.test_config)
        
        # 记录一些性能指标
        logger_manager.log_performance("test_metric", 123.45, "unit")
        self.assertEqual(len(logger_manager.performance_metrics), 1)
        
        # 清空性能指标
        logger_manager.clear_performance_metrics()
        self.assertEqual(len(logger_manager.performance_metrics), 0)
        
        print("✅ 清空性能指标测试通过")
    
    def test_get_log_statistics(self):
        """测试获取日志统计信息"""
        # 创建独立的日志管理器避免状态污染
        temp_dir = tempfile.mkdtemp()
        test_config = {
            "log_level": LogLevel.DEBUG,
            "log_format": LogFormat.DETAILED,
            "log_dir": temp_dir,
            "enable_console": False,
            "enable_file": True,
            "enable_performance": True
        }
        logger_manager = LoggerManager(test_config)

        try:
            # 创建一些日志器
            logger_manager.get_logger("Logger1")
            logger_manager.get_logger("Logger2")
            logger_manager.get_logger("Logger3")

            # 记录一些性能指标
            logger_manager.log_performance("metric1", 123, "unit")
            logger_manager.log_performance("metric2", 456, "unit")

            # 获取统计信息
            stats = logger_manager.get_log_statistics()

            # 验证统计信息（至少包含我们创建的日志器）
            self.assertGreaterEqual(stats["total_loggers"], 3)  # 至少包含Logger1, Logger2, Logger3
            self.assertGreaterEqual(len(stats["logger_names"]), 3)
            self.assertEqual(stats["performance_metrics_count"], 2)
            self.assertEqual(stats["log_level"], LogLevel.DEBUG.name)
            self.assertEqual(stats["log_format"], LogFormat.DETAILED.value)
            self.assertEqual(stats["log_directory"], temp_dir)
            self.assertFalse(stats["console_enabled"])
            self.assertTrue(stats["file_enabled"])
            self.assertTrue(stats["performance_enabled"])
        finally:
            logger_manager.shutdown()
            shutil.rmtree(temp_dir, ignore_errors=True)
        
        print("✅ 获取日志统计信息测试通过")
    
    def test_cleanup_old_logs(self):
        """测试清理旧日志文件"""
        logger_manager = LoggerManager(self.test_config)
        
        # 创建一些测试日志文件
        test_files = []
        for i in range(3):
            test_file = Path(self.temp_dir) / f"old_log_{i}.log"
            test_file.write_text(f"测试日志内容 {i}")
            test_files.append(test_file)
        
        # 修改文件时间为旧时间
        old_time = time.time() - (35 * 24 * 60 * 60)  # 35天前
        for test_file in test_files:
            os.utime(test_file, (old_time, old_time))
        
        # 清理旧日志
        logger_manager.cleanup_old_logs(days=30)
        
        # 验证文件被删除
        for test_file in test_files:
            self.assertFalse(test_file.exists())
        
        print("✅ 清理旧日志文件测试通过")
    
    def test_json_formatter(self):
        """测试JSON格式化器"""
        formatter = JSONFormatter()
        
        # 创建测试日志记录
        record = logging.LogRecord(
            name="TestLogger",
            level=logging.INFO,
            pathname="/test/path.py",
            lineno=123,
            msg="测试消息",
            args=(),
            exc_info=None
        )
        
        # 添加额外数据
        record.extra_data = {"key1": "value1", "key2": "value2"}
        
        # 格式化
        formatted = formatter.format(record)
        
        # 验证JSON格式
        import json
        parsed = json.loads(formatted)
        
        self.assertEqual(parsed["level"], "INFO")
        self.assertEqual(parsed["logger"], "TestLogger")
        self.assertEqual(parsed["message"], "测试消息")
        self.assertEqual(parsed["line"], 123)
        self.assertIn("extra", parsed)
        self.assertEqual(parsed["extra"]["key1"], "value1")
        
        print("✅ JSON格式化器测试通过")
    
    def test_performance_formatter(self):
        """测试性能格式化器"""
        formatter = PerformanceFormatter()
        
        # 创建测试日志记录
        record = logging.LogRecord(
            name="Performance",
            level=logging.INFO,
            pathname="/test/path.py",
            lineno=123,
            msg="性能指标记录",
            args=(),
            exc_info=None
        )
        
        # 添加性能数据
        record.performance_data = {
            "name": "response_time",
            "value": 123.45,
            "unit": "ms",
            "tags": {"component": "test"}
        }
        
        # 格式化
        formatted = formatter.format(record)
        
        # 验证格式
        self.assertIn("[PERF]", formatted)
        self.assertIn("response_time", formatted)
        self.assertIn("123.45ms", formatted)
        self.assertIn("性能指标记录", formatted)
        
        print("✅ 性能格式化器测试通过")
    
    def test_shutdown(self):
        """测试关闭日志管理器"""
        logger_manager = LoggerManager(self.test_config)
        
        # 创建一些日志器
        logger1 = logger_manager.get_logger("Logger1")
        logger2 = logger_manager.get_logger("Logger2")
        
        # 记录处理器数量
        initial_handlers = len(logger1.handlers) + len(logger2.handlers)
        
        # 关闭日志管理器
        logger_manager.shutdown()
        
        # 验证处理器被关闭
        final_handlers = len(logger1.handlers) + len(logger2.handlers)
        self.assertEqual(final_handlers, 0)
        
        print("✅ 关闭日志管理器测试通过")


def run_tests():
    """运行所有测试"""
    print("🚀 开始运行日志管理器测试...")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestLoggerManager)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 60)
    print(f"📊 测试结果统计:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功数: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败数: {len(result.failures)}")
    print(f"   错误数: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback}")
    
    if result.errors:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback}")
    
    if result.wasSuccessful():
        print(f"\n🎉 所有测试通过！日志管理器功能正常！")
        return True
    else:
        print(f"\n⚠️  部分测试失败，请检查代码！")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)