#!/usr/bin/env python3
"""
应答验证器测试模块

测试ResponseValidator的所有功能，包括：
1. 基于JSON配置的动态应答验证
2. 精确匹配和正则表达式验证
3. 超时重试机制
4. 验证结果缓存
5. IMU948真实协议测试

运行方式：
python test_response_validator.py

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import sys
import os
import unittest
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from data_processing.response_validator import (
    ResponseValidator, ValidationConfig, ValidationResponse, ValidationResult
)
from utils.helper_utils import hex_to_bytes
from utils.exceptions import DataProcessingError


class TestValidationConfig(unittest.TestCase):
    """测试验证配置类"""
    
    def test_valid_config_creation(self):
        """测试有效配置创建"""
        config = ValidationConfig(
            validation_type="exact",
            pattern="01 03 02 00 01 79 84",
            timeout=2.0,
            retry_count=3
        )
        
        self.assertEqual(config.validation_type, "exact")
        self.assertEqual(config.pattern, "01 03 02 00 01 79 84")
        self.assertEqual(config.timeout, 2.0)
        self.assertEqual(config.retry_count, 3)
    
    def test_invalid_validation_type(self):
        """测试无效验证类型"""
        with self.assertRaises(ValueError):
            ValidationConfig(
                validation_type="invalid_type",
                pattern="01 03",
                timeout=1.0,
                retry_count=1
            )
    
    def test_invalid_timeout(self):
        """测试无效超时时间"""
        with self.assertRaises(ValueError):
            ValidationConfig(
                validation_type="exact",
                pattern="01 03",
                timeout=0,
                retry_count=1
            )
    
    def test_invalid_retry_count(self):
        """测试无效重试次数"""
        with self.assertRaises(ValueError):
            ValidationConfig(
                validation_type="exact",
                pattern="01 03",
                timeout=1.0,
                retry_count=-1
            )


class TestResponseValidator(unittest.TestCase):
    """测试应答验证器类"""
    
    def setUp(self):
        """测试前准备"""
        self.validator = ResponseValidator()
        
        # 精确匹配配置
        self.exact_config = ValidationConfig(
            validation_type="exact",
            pattern="01 03 02 00 01 79 84",
            timeout=1.0,
            retry_count=2
        )
        
        # 正则表达式配置
        self.regex_config = ValidationConfig(
            validation_type="regex",
            pattern=r"01 03 02 [0-9A-F]{2} [0-9A-F]{2} [0-9A-F]{2} [0-9A-F]{2}",
            timeout=1.0,
            retry_count=2
        )
    
    def test_initialization(self):
        """测试初始化"""
        stats = self.validator.get_statistics()
        self.assertEqual(stats["validations_performed"], 0)
        self.assertEqual(stats["successful_validations"], 0)
        self.assertEqual(stats["success_rate"], 0.0)
    
    def test_exact_match_success(self):
        """测试精确匹配成功"""
        response_data = hex_to_bytes("01 03 02 00 01 79 84")
        
        result = self.validator.validate_response(response_data, self.exact_config)
        
        self.assertEqual(result.result, ValidationResult.SUCCESS)
        self.assertTrue(result.matched)
        self.assertEqual(result.response_data, response_data)
        self.assertEqual(result.pattern_used, "01 03 02 00 01 79 84")
        self.assertEqual(result.error_message, "")
    
    def test_exact_match_failure(self):
        """测试精确匹配失败"""
        response_data = hex_to_bytes("01 03 02 00 02 B8 44")  # 不同的数据
        
        result = self.validator.validate_response(response_data, self.exact_config)
        
        self.assertEqual(result.result, ValidationResult.FAILED)
        self.assertFalse(result.matched)
        self.assertEqual(result.response_data, response_data)
        self.assertEqual(result.error_message, "精确匹配失败")
    
    def test_regex_match_success(self):
        """测试正则表达式匹配成功"""
        response_data = hex_to_bytes("01 03 02 12 34 AB CD")
        
        result = self.validator.validate_response(response_data, self.regex_config)
        
        self.assertEqual(result.result, ValidationResult.SUCCESS)
        self.assertTrue(result.matched)
        self.assertEqual(result.response_data, response_data)
        self.assertEqual(result.error_message, "")
    
    def test_regex_match_failure(self):
        """测试正则表达式匹配失败"""
        response_data = hex_to_bytes("02 04 03 12 34 AB CD")  # 不匹配的模式
        
        result = self.validator.validate_response(response_data, self.regex_config)
        
        self.assertEqual(result.result, ValidationResult.FAILED)
        self.assertFalse(result.matched)
        self.assertEqual(result.error_message, "正则表达式匹配失败")
    
    def test_regex_cache(self):
        """测试正则表达式缓存"""
        response_data = hex_to_bytes("01 03 02 12 34 AB CD")
        
        # 第一次验证
        result1 = self.validator.validate_response(response_data, self.regex_config)
        
        # 第二次验证（应该使用缓存）
        result2 = self.validator.validate_response(response_data, self.regex_config)
        
        self.assertEqual(result1.result, ValidationResult.SUCCESS)
        self.assertEqual(result2.result, ValidationResult.SUCCESS)
        
        stats = self.validator.get_statistics()
        self.assertEqual(stats["cache_hits"], 1)  # 第二次使用了缓存
    
    def test_invalid_hex_pattern(self):
        """测试无效的十六进制模式"""
        invalid_config = ValidationConfig(
            validation_type="exact",
            pattern="invalid hex pattern",
            timeout=1.0,
            retry_count=1
        )
        
        response_data = hex_to_bytes("01 02 03")
        
        result = self.validator.validate_response(response_data, invalid_config)
        
        self.assertEqual(result.result, ValidationResult.ERROR)
        self.assertFalse(result.matched)
        self.assertIn("精确匹配验证错误", result.error_message)
    
    def test_invalid_regex_pattern(self):
        """测试无效的正则表达式模式"""
        invalid_config = ValidationConfig(
            validation_type="regex",
            pattern="[invalid regex",  # 缺少闭合括号
            timeout=1.0,
            retry_count=1
        )
        
        response_data = hex_to_bytes("01 02 03")
        
        result = self.validator.validate_response(response_data, invalid_config)
        
        self.assertEqual(result.result, ValidationResult.ERROR)
        self.assertFalse(result.matched)
        self.assertIn("正则表达式验证错误", result.error_message)
    
    def test_statistics_tracking(self):
        """测试统计信息跟踪"""
        response_data = hex_to_bytes("01 03 02 00 01 79 84")
        
        # 执行多次验证
        self.validator.validate_response(response_data, self.exact_config)  # 成功
        self.validator.validate_response(hex_to_bytes("FF FF"), self.exact_config)  # 失败
        
        stats = self.validator.get_statistics()
        self.assertEqual(stats["validations_performed"], 2)
        self.assertEqual(stats["successful_validations"], 1)
        self.assertEqual(stats["failed_validations"], 1)
        self.assertEqual(stats["success_rate"], 50.0)
    
    def test_statistics_reset(self):
        """测试统计信息重置"""
        response_data = hex_to_bytes("01 03 02 00 01 79 84")
        
        # 执行一些验证
        self.validator.validate_response(response_data, self.exact_config)
        
        # 重置统计
        self.validator.reset_statistics()
        
        stats = self.validator.get_statistics()
        self.assertEqual(stats["validations_performed"], 0)
        self.assertEqual(stats["successful_validations"], 0)
    
    def test_create_validation_config_from_dict(self):
        """测试从字典创建验证配置"""
        config_dict = {
            "type": "exact",
            "pattern": "01 03 02 00 01 79 84",
            "timeout": 2.0,
            "retry_count": 3
        }
        
        config = self.validator.create_validation_config(config_dict)
        
        self.assertEqual(config.validation_type, "exact")
        self.assertEqual(config.pattern, "01 03 02 00 01 79 84")
        self.assertEqual(config.timeout, 2.0)
        self.assertEqual(config.retry_count, 3)
    
    def test_create_validation_config_error(self):
        """测试创建验证配置错误"""
        invalid_config_dict = {
            "type": "exact",
            # 缺少必需的字段
        }
        
        with self.assertRaises(DataProcessingError):
            self.validator.create_validation_config(invalid_config_dict)
    
    def test_retry_mechanism(self):
        """测试重试机制"""
        response_data = hex_to_bytes("FF FF FF")  # 不匹配的数据
        retry_count = 0

        def retry_callback():
            nonlocal retry_count
            retry_count += 1
            if retry_count == 1:
                return hex_to_bytes("FF FF FF")  # 第一次重试仍然不匹配
            else:
                return hex_to_bytes("01 03 02 00 01 79 84")  # 第二次重试返回匹配的数据

        result = self.validator.validate_with_retry(response_data, self.exact_config, retry_callback)

        self.assertEqual(result.result, ValidationResult.SUCCESS)
        self.assertTrue(result.matched)
        self.assertEqual(result.retry_count, 2)  # 在第2次重试时成功
    
    def test_retry_all_failed(self):
        """测试所有重试都失败"""
        response_data = hex_to_bytes("FF FF FF")  # 不匹配的数据
        
        def retry_callback():
            return hex_to_bytes("FF FF FF")  # 总是返回不匹配的数据
        
        result = self.validator.validate_with_retry(response_data, self.exact_config, retry_callback)
        
        self.assertEqual(result.result, ValidationResult.FAILED)
        self.assertFalse(result.matched)
        self.assertEqual(result.retry_count, 2)  # 配置的重试次数


class TestIMU948RealProtocol(unittest.TestCase):
    """测试IMU948真实协议应答验证"""
    
    def setUp(self):
        """加载IMU948配置"""
        config_path = project_root / "config" / "protocols" / "imu948_example.json"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            self.protocol_config = json.load(f)
        
        self.validator = ResponseValidator()
    
    def test_imu948_single_commands_validation(self):
        """测试IMU948单条指令应答验证"""
        single_commands = self.protocol_config["commands"]["single"]
        
        for command in single_commands:
            validation_config_dict = command["response_validation"]
            config = self.validator.create_validation_config(validation_config_dict)
            
            # 测试正确的应答
            expected_response = hex_to_bytes(validation_config_dict["pattern"])
            result = self.validator.validate_response(expected_response, config)
            
            self.assertEqual(result.result, ValidationResult.SUCCESS, 
                           f"指令 {command['id']} 应答验证失败")
            self.assertTrue(result.matched)
    
    def test_imu948_continuous_commands_validation(self):
        """测试IMU948连续指令应答验证"""
        continuous_commands = self.protocol_config["commands"]["continuous"]
        
        for command in continuous_commands:
            validation_config_dict = command["response_validation"]
            config = self.validator.create_validation_config(validation_config_dict)
            
            # 测试正确的应答
            expected_response = hex_to_bytes(validation_config_dict["pattern"])
            result = self.validator.validate_response(expected_response, config)
            
            self.assertEqual(result.result, ValidationResult.SUCCESS, 
                           f"指令 {command['id']} 应答验证失败")
            self.assertTrue(result.matched)
    
    def test_imu948_disable_auto_report_validation(self):
        """测试IMU948关闭自动上报指令验证"""
        # 指令: "49 00 01 18 19 4D"
        # 期望应答: "49 00 01 18 19 4D" (相同)
        
        command_config = {
            "type": "exact",
            "pattern": "49 00 01 18 19 4D",
            "timeout": 2.0,
            "retry_count": 3
        }
        
        config = self.validator.create_validation_config(command_config)
        
        # 测试正确应答
        correct_response = hex_to_bytes("49 00 01 18 19 4D")
        result = self.validator.validate_response(correct_response, config)
        
        self.assertEqual(result.result, ValidationResult.SUCCESS)
        self.assertTrue(result.matched)
        
        # 测试错误应答
        wrong_response = hex_to_bytes("49 00 01 18 19 4E")  # 最后一个字节不同
        result = self.validator.validate_response(wrong_response, config)
        
        self.assertEqual(result.result, ValidationResult.FAILED)
        self.assertFalse(result.matched)
    
    def test_imu948_set_sensor_params_validation(self):
        """测试IMU948设置传感器参数指令验证"""
        # 指令: "49 00 0B 12 05 FF 00 04 1E 01 03 05 C0 00 0C 4D"
        # 期望应答: "49 00 01 12 13 4D"
        
        command_config = {
            "type": "exact",
            "pattern": "49 00 01 12 13 4D",
            "timeout": 2.0,
            "retry_count": 3
        }
        
        config = self.validator.create_validation_config(command_config)
        
        # 测试正确应答
        correct_response = hex_to_bytes("49 00 01 12 13 4D")
        result = self.validator.validate_response(correct_response, config)
        
        self.assertEqual(result.result, ValidationResult.SUCCESS)
        self.assertTrue(result.matched)


def run_comprehensive_tests():
    """运行全面测试"""
    print("=" * 60)
    print("应答验证器全面测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加所有测试类
    test_classes = [
        TestValidationConfig,
        TestResponseValidator,
        TestIMU948RealProtocol
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"运行测试数量: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n测试通过率: {success_rate:.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
