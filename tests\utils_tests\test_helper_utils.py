#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 helper_utils.py 模块
验证辅助工具函数的功能

作者: LD (Lead Developer)
创建时间: 2025-08-06
版本: 1.0
"""

import os
import sys
import time
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from utils.helper_utils import (
    HexUtils, TimeUtils, FileUtils, ValidationUtils, StringUtils,
    bytes_to_hex, hex_to_bytes, get_timestamp, get_formatted_time
)


def test_hex_utils():
    """测试十六进制工具类"""
    print("=" * 60)
    print("测试十六进制工具类")
    print("=" * 60)
    
    try:
        # 测试字节转十六进制字符串
        test_bytes = b'\x01\x02\x03\xFF'
        hex_string = HexUtils.bytes_to_hex_string(test_bytes)
        expected = "01 02 03 FF"
        
        if hex_string == expected:
            print(f"✅ 字节转十六进制: {test_bytes} -> {hex_string}")
        else:
            print(f"❌ 字节转十六进制失败: 期望 {expected}, 得到 {hex_string}")
            return False
        
        # 测试不同分隔符
        hex_string_no_sep = HexUtils.bytes_to_hex_string(test_bytes, "")
        if hex_string_no_sep == "010203FF":
            print(f"✅ 无分隔符转换: {hex_string_no_sep}")
        else:
            print(f"❌ 无分隔符转换失败")
            return False
        
        # 测试十六进制字符串转字节
        converted_bytes = HexUtils.hex_string_to_bytes(hex_string)
        if converted_bytes == test_bytes:
            print(f"✅ 十六进制转字节: {hex_string} -> {converted_bytes}")
        else:
            print(f"❌ 十六进制转字节失败")
            return False
        
        # 测试无分隔符的十六进制字符串
        converted_bytes2 = HexUtils.hex_string_to_bytes("010203FF")
        if converted_bytes2 == test_bytes:
            print(f"✅ 无分隔符十六进制转字节成功")
        else:
            print(f"❌ 无分隔符十六进制转字节失败")
            return False
        
        # 测试有效性检查
        if HexUtils.is_valid_hex_string("01 02 03 FF"):
            print("✅ 有效十六进制字符串检查正确")
        else:
            print("❌ 有效十六进制字符串检查失败")
            return False
        
        if not HexUtils.is_valid_hex_string("invalid hex"):
            print("✅ 无效十六进制字符串检查正确")
        else:
            print("❌ 无效十六进制字符串检查失败")
            return False
        
        # 测试校验和计算
        test_data = b'\x01\x02\x03\x04'
        xor_checksum = HexUtils.calculate_checksum(test_data, "xor")
        sum_checksum = HexUtils.calculate_checksum(test_data, "sum")
        crc8_checksum = HexUtils.calculate_checksum(test_data, "crc8")
        
        print(f"✅ 校验和计算: XOR={xor_checksum:02X}, SUM={sum_checksum:02X}, CRC8={crc8_checksum:02X}")
        
        return True
        
    except Exception as e:
        print(f"❌ 十六进制工具测试失败: {e}")
        return False


def test_time_utils():
    """测试时间工具类"""
    print("\n" + "=" * 60)
    print("测试时间工具类")
    print("=" * 60)
    
    try:
        # 测试获取时间戳
        timestamp1 = TimeUtils.get_timestamp()
        time.sleep(0.1)
        timestamp2 = TimeUtils.get_timestamp()
        
        if timestamp2 > timestamp1:
            print(f"✅ 时间戳获取正常: {timestamp1} -> {timestamp2}")
        else:
            print("❌ 时间戳获取异常")
            return False
        
        # 测试格式化时间
        formatted_time = TimeUtils.get_formatted_time(timestamp1)
        print(f"✅ 格式化时间: {formatted_time}")
        
        # 测试自定义格式
        custom_format = TimeUtils.get_formatted_time(timestamp1, "%Y%m%d_%H%M%S")
        print(f"✅ 自定义格式时间: {custom_format}")
        
        # 测试毫秒时间戳
        ms_timestamp = TimeUtils.get_milliseconds()
        if ms_timestamp > timestamp1 * 1000:
            print(f"✅ 毫秒时间戳: {ms_timestamp}")
        else:
            print("❌ 毫秒时间戳异常")
            return False
        
        # 测试便捷函数
        timestamp3 = get_timestamp()
        formatted3 = get_formatted_time(timestamp3)
        print(f"✅ 便捷函数: {timestamp3} -> {formatted3}")
        
        return True
        
    except Exception as e:
        print(f"❌ 时间工具测试失败: {e}")
        return False


def test_file_utils():
    """测试文件工具类"""
    print("\n" + "=" * 60)
    print("测试文件工具类")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # 测试确保目录存在
            test_dir = Path(temp_dir) / "test_subdir" / "nested"
            created_dir = FileUtils.ensure_directory_exists(test_dir)
            
            if created_dir.exists() and created_dir.is_dir():
                print(f"✅ 目录创建成功: {created_dir}")
            else:
                print("❌ 目录创建失败")
                return False
            
            # 测试安全文件名
            unsafe_name = 'test<>:"/\\|?*file.txt'
            safe_name = FileUtils.get_safe_filename(unsafe_name)
            print(f"✅ 安全文件名: {unsafe_name} -> {safe_name}")
            
            # 测试文件大小获取
            test_file = Path(temp_dir) / "test_file.txt"
            test_content = "Hello, World!" * 100
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            file_size = FileUtils.get_file_size(test_file)
            expected_size = len(test_content.encode('utf-8'))
            
            if file_size == expected_size:
                print(f"✅ 文件大小获取正确: {file_size} 字节")
            else:
                print(f"❌ 文件大小获取错误: 期望 {expected_size}, 得到 {file_size}")
                return False
            
            # 测试不存在文件的大小
            non_existent_size = FileUtils.get_file_size("non_existent_file.txt")
            if non_existent_size == 0:
                print("✅ 不存在文件大小返回0")
            else:
                print("❌ 不存在文件大小不为0")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 文件工具测试失败: {e}")
            return False


def test_validation_utils():
    """测试验证工具类"""
    print("\n" + "=" * 60)
    print("测试验证工具类")
    print("=" * 60)
    
    try:
        # 测试端口号验证
        valid_ports = [1, 80, 443, 8080, 65535]
        invalid_ports = [0, -1, 65536, "invalid", None]
        
        for port in valid_ports:
            if ValidationUtils.is_valid_port_number(port):
                print(f"✅ 有效端口号: {port}")
            else:
                print(f"❌ 有效端口号验证失败: {port}")
                return False
        
        for port in invalid_ports:
            if not ValidationUtils.is_valid_port_number(port):
                print(f"✅ 无效端口号: {port}")
            else:
                print(f"❌ 无效端口号验证失败: {port}")
                return False
        
        # 测试波特率验证
        valid_baudrates = [9600, 19200, 38400, 57600, 115200]
        invalid_baudrates = [1234, 999999, "invalid", None]
        
        for baudrate in valid_baudrates:
            if ValidationUtils.is_valid_baudrate(baudrate):
                print(f"✅ 有效波特率: {baudrate}")
            else:
                print(f"❌ 有效波特率验证失败: {baudrate}")
                return False
        
        for baudrate in invalid_baudrates:
            if not ValidationUtils.is_valid_baudrate(baudrate):
                print(f"✅ 无效波特率: {baudrate}")
            else:
                print(f"❌ 无效波特率验证失败: {baudrate}")
                return False
        
        # 测试超时时间验证
        valid_timeouts = [0.1, 1.0, 5.5, 60.0]
        invalid_timeouts = [0, -1, 61, "invalid", None]
        
        for timeout in valid_timeouts:
            if ValidationUtils.is_valid_timeout(timeout):
                print(f"✅ 有效超时时间: {timeout}")
            else:
                print(f"❌ 有效超时时间验证失败: {timeout}")
                return False
        
        for timeout in invalid_timeouts:
            if not ValidationUtils.is_valid_timeout(timeout):
                print(f"✅ 无效超时时间: {timeout}")
            else:
                print(f"❌ 无效超时时间验证失败: {timeout}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证工具测试失败: {e}")
        return False


def test_string_utils():
    """测试字符串工具类"""
    print("\n" + "=" * 60)
    print("测试字符串工具类")
    print("=" * 60)
    
    try:
        # 测试字符串截断
        long_text = "这是一个很长的测试字符串，用于测试截断功能"
        truncated = StringUtils.truncate_string(long_text, 20)
        print(f"✅ 字符串截断: {truncated}")
        
        # 测试不需要截断的情况
        short_text = "短文本"
        not_truncated = StringUtils.truncate_string(short_text, 20)
        if not_truncated == short_text:
            print(f"✅ 短字符串不截断: {not_truncated}")
        else:
            print("❌ 短字符串截断错误")
            return False
        
        # 测试字节大小格式化
        sizes = [0, 512, 1024, 1536, 1048576, 1073741824]
        expected = ["0 B", "512.0 B", "1.0 KB", "1.5 KB", "1.0 MB", "1.0 GB"]
        
        for size, exp in zip(sizes, expected):
            formatted = StringUtils.format_bytes_size(size)
            if formatted == exp:
                print(f"✅ 字节大小格式化: {size} -> {formatted}")
            else:
                print(f"❌ 字节大小格式化错误: {size} -> {formatted} (期望 {exp})")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 字符串工具测试失败: {e}")
        return False


def test_convenience_functions():
    """测试便捷函数"""
    print("\n" + "=" * 60)
    print("测试便捷函数")
    print("=" * 60)
    
    try:
        # 测试十六进制便捷函数
        test_bytes = b'\x01\x02\x03\xFF'
        hex_str = bytes_to_hex(test_bytes)
        converted_back = hex_to_bytes(hex_str)
        
        if converted_back == test_bytes:
            print(f"✅ 十六进制便捷函数: {test_bytes} -> {hex_str} -> {converted_back}")
        else:
            print("❌ 十六进制便捷函数失败")
            return False
        
        # 测试时间便捷函数
        timestamp = get_timestamp()
        formatted = get_formatted_time(timestamp)
        print(f"✅ 时间便捷函数: {timestamp} -> {formatted}")
        
        return True
        
    except Exception as e:
        print(f"❌ 便捷函数测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("测试错误处理")
    print("=" * 60)
    
    try:
        # 测试无效输入的错误处理
        try:
            HexUtils.bytes_to_hex_string("not bytes")
            print("❌ 应该抛出TypeError")
            return False
        except TypeError:
            print("✅ 正确抛出TypeError")
        
        try:
            HexUtils.hex_string_to_bytes(123)
            print("❌ 应该抛出TypeError")
            return False
        except TypeError:
            print("✅ 正确抛出TypeError")
        
        try:
            HexUtils.hex_string_to_bytes("invalid hex")
            print("❌ 应该抛出ValueError")
            return False
        except ValueError:
            print("✅ 正确抛出ValueError")
        
        try:
            HexUtils.hex_string_to_bytes("01 02 0")  # 奇数长度
            print("❌ 应该抛出ValueError")
            return False
        except ValueError:
            print("✅ 奇数长度正确抛出ValueError")
        
        try:
            HexUtils.calculate_checksum(b'\x01\x02', "invalid_type")
            print("❌ 应该抛出ValueError")
            return False
        except ValueError:
            print("✅ 无效校验和类型正确抛出ValueError")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试 helper_utils.py 模块")
    print("=" * 80)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("十六进制工具", test_hex_utils()))
    test_results.append(("时间工具", test_time_utils()))
    test_results.append(("文件工具", test_file_utils()))
    test_results.append(("验证工具", test_validation_utils()))
    test_results.append(("字符串工具", test_string_utils()))
    test_results.append(("便捷函数", test_convenience_functions()))
    test_results.append(("错误处理", test_error_handling()))
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {len(test_results)} 个测试")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    
    if failed == 0:
        print("\n🎉 所有测试通过！")
        return True
    else:
        print(f"\n⚠️  有 {failed} 个测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
