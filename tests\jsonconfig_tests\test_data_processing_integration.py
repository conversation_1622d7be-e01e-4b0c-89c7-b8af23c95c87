#!/usr/bin/env python3
"""
数据处理层集成测试模块

测试数据处理层四个核心组件的集成功能：
1. FrameDetector - 帧检测器
2. DataParser - 数据解析器
3. ResponseValidator - 应答验证器
4. QueueManager - 队列管理器

以及与IMU948真实协议的完整集成测试。

运行方式：
python test_data_processing_integration.py

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import sys
import os
import unittest
import json
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from data_processing.frame_detector import FrameDetector, FrameDetectionConfig
from data_processing.data_parser import DataParser
from data_processing.response_validator import ResponseValidator, ValidationConfig
from data_processing.queue_manager import QueueManager, QueueConfig
from utils.helper_utils import hex_to_bytes, bytes_to_hex


class DataProcessingPipeline:
    """数据处理管道 - 集成所有四个组件"""
    
    def __init__(self, protocol_config):
        """初始化数据处理管道"""
        # 初始化帧检测器
        frame_config_data = protocol_config["continuous_data"]["frame_detection"]
        self.frame_config = FrameDetectionConfig.from_json_config(frame_config_data)
        self.frame_detector = FrameDetector(self.frame_config)
        
        # 初始化数据解析器
        parsing_config = protocol_config["continuous_data"]["data_parsing"]
        self.data_parser = DataParser(parsing_config)
        
        # 初始化应答验证器
        self.response_validator = ResponseValidator()
        
        # 初始化队列管理器
        queue_config = QueueConfig(
            queue_size=100,
            warning_threshold=0.8,
            batch_size=10
        )
        self.queue_manager = QueueManager(queue_config)
        
        # 统计信息
        self.stats = {
            "raw_bytes_processed": 0,
            "frames_detected": 0,
            "frames_parsed": 0,
            "parse_errors": 0,
            "queue_operations": 0
        }
    
    def process_raw_data(self, raw_data: bytes):
        """处理原始数据的完整流程"""
        self.stats["raw_bytes_processed"] += len(raw_data)
        
        # 1. 帧检测
        frames = self.frame_detector.process_data(raw_data)
        self.stats["frames_detected"] += len(frames)
        
        # 2. 数据解析和队列管理
        for frame in frames:
            try:
                # 解析帧数据
                parsed_data = self.data_parser.parse_frame(frame)
                self.stats["frames_parsed"] += 1
                
                # 将解析结果放入队列
                for data_item in parsed_data:
                    self.queue_manager.put(data_item, "parsed_data")
                    self.stats["queue_operations"] += 1
                    
            except Exception as e:
                self.stats["parse_errors"] += 1
                print(f"解析帧数据失败: {e}")
        
        return frames
    
    def validate_command_response(self, response_data: bytes, validation_config: dict):
        """验证指令应答"""
        config = self.response_validator.create_validation_config(validation_config)
        return self.response_validator.validate_response(response_data, config)
    
    def get_parsed_data_batch(self, max_items=None):
        """批量获取解析后的数据"""
        return self.queue_manager.get_batch(max_items)
    
    def get_statistics(self):
        """获取统计信息"""
        stats = self.stats.copy()
        stats.update({
            "frame_detector_stats": self.frame_detector.get_statistics(),
            "data_parser_stats": self.data_parser.get_statistics(),
            "response_validator_stats": self.response_validator.get_statistics(),
            "queue_manager_stats": self.queue_manager.get_statistics()
        })
        return stats


class TestDataProcessingIntegration(unittest.TestCase):
    """测试数据处理层集成功能"""
    
    def setUp(self):
        """测试前准备"""
        # 加载IMU948配置
        config_path = project_root / "config" / "protocols" / "imu948_example.json"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            self.protocol_config = json.load(f)
        
        # 创建数据处理管道
        self.pipeline = DataProcessingPipeline(self.protocol_config)
    
    def test_single_frame_processing(self):
        """测试单帧数据处理"""
        # IMU948真实数据帧
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        
        # 处理数据
        frames = self.pipeline.process_raw_data(frame_data)
        
        # 验证帧检测
        self.assertEqual(len(frames), 1)
        self.assertEqual(frames[0], frame_data)
        
        # 验证数据解析和队列操作
        stats = self.pipeline.get_statistics()
        self.assertEqual(stats["frames_detected"], 1)
        self.assertEqual(stats["frames_parsed"], 1)
        self.assertEqual(stats["queue_operations"], 6)  # 6个解析字段
        
        # 获取解析后的数据
        parsed_items = self.pipeline.get_parsed_data_batch()
        self.assertEqual(len(parsed_items), 6)
        
        # 验证解析结果
        field_names = [item.data.field_name for item in parsed_items]
        expected_names = [
            "Roll_滚转角", "Pitch_俯仰角", "Yaw_偏航角",
            "Position_X", "Position_Y", "Position_Z"
        ]
        self.assertEqual(field_names, expected_names)
    
    def test_multiple_frames_processing(self):
        """测试多帧数据处理"""
        # 三个连续的IMU948数据帧
        frames_data = hex_to_bytes(
            "49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D"
            "49 00 13 11 C0 00 2F 41 05 06 D1 F7 09 FF FE B8 00 00 00 00 00 00 E4 4D"
            "49 00 13 11 C0 00 2F 41 05 06 DC F7 0A FF FE B8 00 00 00 00 00 00 EF 4D"
        )
        
        # 处理数据
        frames = self.pipeline.process_raw_data(frames_data)
        
        # 验证帧检测
        self.assertEqual(len(frames), 3)
        
        # 验证统计信息
        stats = self.pipeline.get_statistics()
        self.assertEqual(stats["frames_detected"], 3)
        self.assertEqual(stats["frames_parsed"], 3)
        self.assertEqual(stats["queue_operations"], 18)  # 3帧 × 6字段
        
        # 批量获取解析后的数据
        parsed_items = self.pipeline.get_parsed_data_batch(max_items=18)
        self.assertEqual(len(parsed_items), 18)
    
    def test_fragmented_data_processing(self):
        """测试分片数据处理"""
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        
        # 分成三部分发送
        part1 = frame_data[:8]
        part2 = frame_data[8:16]
        part3 = frame_data[16:]
        
        # 分别处理
        frames1 = self.pipeline.process_raw_data(part1)
        frames2 = self.pipeline.process_raw_data(part2)
        frames3 = self.pipeline.process_raw_data(part3)
        
        # 前两部分不应该产生完整帧
        self.assertEqual(len(frames1), 0)
        self.assertEqual(len(frames2), 0)
        
        # 第三部分应该产生完整帧
        self.assertEqual(len(frames3), 1)
        self.assertEqual(frames3[0], frame_data)
        
        # 验证最终统计
        stats = self.pipeline.get_statistics()
        self.assertEqual(stats["frames_detected"], 1)
        self.assertEqual(stats["frames_parsed"], 1)
    
    def test_noisy_data_processing(self):
        """测试带噪声数据处理"""
        # 噪声 + 有效帧 + 更多噪声 + 另一个有效帧
        noise1 = hex_to_bytes("FF EE DD CC BB AA")
        frame1 = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        noise2 = hex_to_bytes("11 22 33 44")
        frame2 = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 D1 F7 09 FF FE B8 00 00 00 00 00 00 E4 4D")
        
        combined_data = noise1 + frame1 + noise2 + frame2
        
        # 处理数据
        frames = self.pipeline.process_raw_data(combined_data)
        
        # 应该检测到两个有效帧
        self.assertEqual(len(frames), 2)
        self.assertEqual(frames[0], frame1)
        self.assertEqual(frames[1], frame2)
        
        # 验证解析结果
        stats = self.pipeline.get_statistics()
        self.assertEqual(stats["frames_detected"], 2)
        self.assertEqual(stats["frames_parsed"], 2)
        self.assertEqual(stats["queue_operations"], 12)  # 2帧 × 6字段
    
    def test_command_response_validation(self):
        """测试指令应答验证"""
        # 测试关闭自动上报指令的应答验证
        disable_auto_report_cmd = self.protocol_config["commands"]["single"][0]
        validation_config = disable_auto_report_cmd["response_validation"]
        
        # 正确的应答
        correct_response = hex_to_bytes(validation_config["pattern"])
        result = self.pipeline.validate_command_response(correct_response, validation_config)
        
        self.assertTrue(result.matched)
        self.assertEqual(result.result.value, "success")
        
        # 错误的应答
        wrong_response = hex_to_bytes("49 00 01 18 19 4E")  # 最后一个字节不同
        result = self.pipeline.validate_command_response(wrong_response, validation_config)
        
        self.assertFalse(result.matched)
        self.assertEqual(result.result.value, "failed")
    
    def test_queue_overflow_handling(self):
        """测试队列溢出处理"""
        # 创建一个小队列的管道
        small_queue_config = QueueConfig(
            queue_size=5,
            warning_threshold=0.8,
            batch_size=3
        )
        self.pipeline.queue_manager = QueueManager(small_queue_config)
        
        # 处理足够多的数据以填满队列
        frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        
        # 处理一帧数据（6个字段，会超过队列大小5）
        frames = self.pipeline.process_raw_data(frame_data)
        
        # 验证帧检测成功
        self.assertEqual(len(frames), 1)
        
        # 验证队列状态
        queue_stats = self.pipeline.queue_manager.get_statistics()
        self.assertEqual(queue_stats["current_size"], 5)  # 队列已满
        self.assertEqual(queue_stats["items_dropped"], 1)  # 1个项目被丢弃
    
    def test_performance_benchmark(self):
        """测试性能基准"""
        # 准备大量测试数据
        single_frame = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")
        test_data = single_frame * 100  # 100个连续帧
        
        # 性能测试
        start_time = time.time()
        frames = self.pipeline.process_raw_data(test_data)
        end_time = time.time()
        
        processing_time = end_time - start_time
        frames_per_second = len(frames) / processing_time if processing_time > 0 else 0
        
        # 验证结果
        self.assertEqual(len(frames), 100)
        
        # 性能要求：应该能够处理 > 1000 帧/秒
        print(f"处理性能: {frames_per_second:.1f} 帧/秒")
        self.assertGreater(frames_per_second, 1000, "处理速度应该大于1000帧/秒")
        
        # 验证统计信息
        stats = self.pipeline.get_statistics()
        self.assertEqual(stats["frames_detected"], 100)
        self.assertEqual(stats["frames_parsed"], 100)
        self.assertEqual(stats["parse_errors"], 0)
    
    def test_concurrent_processing(self):
        """测试并发处理"""
        results = []
        results_lock = threading.Lock()

        def process_data_thread(thread_id):
            frame_data = hex_to_bytes("49 00 13 11 C0 00 2F 41 05 06 C6 F7 08 FF FE B8 00 00 00 00 00 00 D9 4D")

            for i in range(5):  # 减少循环次数
                frames = self.pipeline.process_raw_data(frame_data)
                with results_lock:
                    results.append((thread_id, len(frames)))
                time.sleep(0.01)  # 增加延迟

        # 创建少量线程
        threads = []
        for i in range(3):  # 减少线程数
            thread = threading.Thread(target=process_data_thread, args=(i,))
            threads.append(thread)

        # 启动所有线程
        for thread in threads:
            thread.start()

        # 等待所有线程完成（设置超时）
        for thread in threads:
            thread.join(timeout=10.0)  # 10秒超时

        # 验证结果
        self.assertEqual(len(results), 15)  # 3个线程 × 5次处理

        # 验证所有处理都成功
        for thread_id, frame_count in results:
            self.assertEqual(frame_count, 1)

        # 验证最终统计（至少应该有15帧）
        stats = self.pipeline.get_statistics()
        self.assertGreaterEqual(stats["frames_detected"], 15)
        self.assertGreaterEqual(stats["frames_parsed"], 15)


def run_comprehensive_tests():
    """运行全面集成测试"""
    print("=" * 60)
    print("数据处理层集成测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    tests = unittest.TestLoader().loadTestsFromTestCase(TestDataProcessingIntegration)
    test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("集成测试结果汇总")
    print("=" * 60)
    print(f"运行测试数量: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n测试通过率: {success_rate:.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
